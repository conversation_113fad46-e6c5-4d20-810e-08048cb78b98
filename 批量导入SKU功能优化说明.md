# SKU批量导入功能优化说明

## 优化概述

将原来的单个SKU创建改为底层批量创建，并优化了编码生成机制以确保唯一性。

## 主要优化内容

### 1. 服务层批量创建

**新增接口：**
- `SkuService.batchCreate(List<SkuCreateCommand> addReqs)` - 批量创建SKU
- `SkuServiceImpl.batchCreate()` - 批量创建实现

**优化效果：**
- 减少数据库交互次数，从N次单个插入改为1次批量插入
- 提升导入性能，特别是大批量数据导入时
- 保证事务一致性，要么全部成功要么全部失败

### 2. 编码唯一性保证

**新增方法：**
- `CodeGenerationUtil.generateUniqueSkuCodes(List<Long> specCodes)` - 批量生成唯一SKU编码

**唯一性机制：**
- 在同一批次中确保生成的编码不重复
- 使用HashSet检查编码唯一性
- 添加微小延迟确保时间戳差异
- 最多重试100次生成唯一编码

**编码格式：**
```
SKU + 时间戳(yyMMddHHmmssSSS) + 随机数(0-9) + "-" + 规格代号
例：SKU25010814301234567-1
```

### 3. 批量导入流程优化

**原流程：**
```
读取Excel → 逐行验证 → 逐个创建SKU → 返回结果
```

**优化后流程：**
```
读取Excel → 批量验证 → 批量生成编码 → 批量创建SKU → 返回结果
```

**优化效果：**
- 先进行数据验证，避免部分成功部分失败的情况
- 批量生成唯一编码，确保编码不重复
- 批量创建，提升性能和事务一致性

## 技术实现细节

### 1. 数据库层优化

使用MyBatis的批量插入功能：
```xml
<insert id="insertBatch" parameterType="java.util.List">
    INSERT INTO sop_sku_info (...) VALUES
    <foreach collection="skus" item="req" separator=",">
        (#{req.field1}, #{req.field2}, ...)
    </foreach>
</insert>
```

### 2. 编码生成优化

```java
public static List<String> generateUniqueSkuCodes(List<Long> specCodes) {
    List<String> skuCodes = new ArrayList<>();
    Set<String> codeSet = new HashSet<>();
    
    for (Long specCode : specCodes) {
        String skuCode;
        int attempts = 0;
        do {
            skuCode = SKU_PREFIX + generateCode() + "-" + specCode;
            attempts++;
            if (attempts > 100) {
                throw new RuntimeException("生成唯一SKU编码失败");
            }
            Thread.sleep(1); // 确保时间戳不同
        } while (codeSet.contains(skuCode));
        
        codeSet.add(skuCode);
        skuCodes.add(skuCode);
    }
    
    return skuCodes;
}
```

### 3. 事务处理

使用Spring的事务模板确保数据一致性：
```java
return transactionTemplate.execute(status -> {
    try {
        List<Long> createdIds = skuService.batchCreate(createCommands);
        return createdIds.size();
    } catch (Exception e) {
        throw new RuntimeException("批量创建SKU失败：" + e.getMessage(), e);
    }
});
```

## 性能对比

### 导入100条SKU数据对比：

| 方式 | 数据库交互次数 | 编码生成方式 | 预估耗时 |
|------|----------------|--------------|----------|
| 优化前 | 100次单个插入 | 逐个生成，可能重复 | ~10秒 |
| 优化后 | 1次批量插入 | 批量生成，确保唯一 | ~2秒 |

### 导入1000条SKU数据对比：

| 方式 | 数据库交互次数 | 编码生成方式 | 预估耗时 |
|------|----------------|--------------|----------|
| 优化前 | 1000次单个插入 | 逐个生成，可能重复 | ~100秒 |
| 优化后 | 1次批量插入 | 批量生成，确保唯一 | ~5秒 |

## 错误处理改进

### 1. 数据验证前置
- 在批量创建前完成所有数据验证
- 避免部分成功部分失败的情况

### 2. 编码冲突处理
- 自动重试生成唯一编码
- 最多重试100次，避免无限循环

### 3. 事务回滚
- 批量创建失败时自动回滚
- 保证数据一致性

## 测试验证

创建了性能测试类 `SkuBatchImportPerformanceTest`：

1. **唯一性测试**：验证批量生成的编码是否唯一
2. **性能测试**：测试大批量编码生成的耗时
3. **压力测试**：测试1000个相同规格的编码生成

## 向后兼容性

- 保留原有的单个创建接口
- 批量导入接口保持不变
- 现有功能不受影响

## 注意事项

1. **内存使用**：大批量导入时需要注意内存使用情况
2. **数据库连接**：批量插入可能需要较长的数据库连接时间
3. **编码生成**：极大批量时编码生成可能耗时较长
4. **事务超时**：需要合理设置事务超时时间

## 建议

1. **分批处理**：建议单次导入不超过1000条记录
2. **异步处理**：对于超大批量可以考虑异步处理
3. **进度反馈**：可以添加导入进度反馈机制
4. **监控告警**：添加导入性能监控和异常告警 