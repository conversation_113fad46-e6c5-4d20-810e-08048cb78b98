/*
 * Copyright(C) 2024 Spes Co., Ltd. All rights reserved.
 */

package com.spes.sop.third;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.spes.sop.third.jackyun.client.JackYunOpenHttpClient;
import com.spes.sop.third.jackyun.config.JackyunOpenApiConfig;
import com.spes.sop.third.jackyun.model.BaseRequestBizData;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 吉客云开放平台HTTP工具类测试
 * 基于配置文件中的吉客云配置信息进行测试
 *
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE,
        classes = {JackYunOpenHttpClient.class, JackyunOpenApiConfig.class})
@TestPropertySource(properties = {
        "jackyun.app-key=73914332",
        "jackyun.app-secret=a664417f04db4f0086a77fb0a16c99d1",
        "jackyun.server-url=https://open.jackyun.com/open/openapi/do",
        "jackyun.api-version=1.0",
        "jackyun.encoding=UTF-8",
        "jackyun.connect-timeout=30000",
        "jackyun.read-timeout=30000"
})
@DisplayName("吉客云开放平台HTTP工具类测试")
class JackYunOpenHttpClientTest {

    @Autowired
    private JackYunOpenHttpClient jackYunOpenHttpClient;

    @Autowired
    private JackyunOpenApiConfig jackyunOpenApiConfig;

    /**
     * 测试业务数据类 - 用户查询
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    static class UserQueryBizData extends BaseRequestBizData {

        /**
         * 用户ID
         */
        private Long userId;

        /**
         * 查询类型
         */
        private String queryType;

        /**
         * 页码
         */
        private Integer pageNum;

        /**
         * 页大小
         */
        private Integer pageSize;
    }

    /**
     * 测试业务数据类 - 订单查询
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    static class OrderQueryBizData extends BaseRequestBizData {

        /**
         * 订单号
         */
        private String orderNo;

        /**
         * 订单状态
         */
        private String orderStatus;

        /**
         * 开始时间
         */
        private String startTime;

        /**
         * 结束时间
         */
        private String endTime;
    }

    /**
     * 测试业务数据类 - 商品同步
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    static class ProductSyncBizData extends BaseRequestBizData {

        /**
         * 商品编码
         */
        private String productCode;

        /**
         * 商品名称
         */
        private String productName;

        /**
         * 商品分类
         */
        private String category;

        /**
         * 价格
         */
        private String price;

        /**
         * 库存
         */
        private Integer stock;
    }

    private String testToken;

    @BeforeEach
    void setUp() {
        // 模拟授权token（实际使用时应从配置或认证服务获取）
        testToken = "test_token_" + System.currentTimeMillis();
        log.info("测试准备完成，使用Token: {}", testToken);

        // 验证配置是否正确注入
        if (jackyunOpenApiConfig != null) {
            log.info("配置注入成功 - AppKey: {}, ServerUrl: {}",
                    jackyunOpenApiConfig.getAppKey(), jackyunOpenApiConfig.getServerUrl());
        } else {
            log.warn("配置注入失败，将使用默认配置");
        }
    }

    @Test
    @DisplayName("测试用户查询接口")
    void testUserQuery() {
        // 构造测试数据
        UserQueryBizData bizData = UserQueryBizData.builder()
                .userId(12345L)
                .queryType("detail")
                .pageNum(1)
                .pageSize(10)
                .build();

        log.info("测试用户查询接口 - 请求数据: {}", JSONUtil.toJsonPrettyStr(bizData));


        // 调用吉客云开放平台
        String response = JackYunOpenHttpClient.post(
                "user.query",     // 方法名
                "1.0",           // 版本号
                testToken,       // 授权token
                bizData          // 业务数据
        );

        // 验证响应
        assertNotNull(response, "响应结果不能为空");
        log.info("用户查询成功 - 响应: {}", response);

    }

    @Test
    @DisplayName("测试订单查询接口")
    void testOrderQuery() {
        // 构造测试数据
        OrderQueryBizData bizData = OrderQueryBizData.builder()
                .orderNo("ORD" + IdUtil.getSnowflakeNextIdStr())
                .orderStatus("PENDING")
                .startTime(LocalDateTime.now().minusDays(7).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .endTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .build();

        log.info("测试订单查询接口 - 请求数据: {}", JSONUtil.toJsonPrettyStr(bizData));


        // 调用吉客云开放平台
        String response = JackYunOpenHttpClient.post(
                "order.query",    // 方法名
                "2.0",           // 版本号
                testToken,       // 授权token
                bizData          // 业务数据
        );

        // 验证响应
        assertNotNull(response, "响应结果不能为空");
        log.info("订单查询成功 - 响应: {}", response);

    }

    @Test
    @DisplayName("测试商品同步接口")
    void testProductSync() {
        // 构造测试数据
        ProductSyncBizData bizData = ProductSyncBizData.builder()
                .productCode("PRD" + IdUtil.getSnowflakeNextIdStr())
                .productName("测试商品" + System.currentTimeMillis())
                .category("电子产品")
                .price("199.99")
                .stock(100)
                .build();

        log.info("测试商品同步接口 - 请求数据: {}", JSONUtil.toJsonPrettyStr(bizData));


        // 调用吉客云开放平台
        String response = JackYunOpenHttpClient.post(
                "product.sync",   // 方法名
                null,            // 使用默认版本
                testToken,       // 授权token
                bizData          // 业务数据
        );

        // 验证响应
        assertNotNull(response, "响应结果不能为空");
        log.info("商品同步成功 - 响应: {}", response);


    }

    @Test
    @DisplayName("测试无Token的接口调用")
    void testWithoutToken() {
        // 构造测试数据
        UserQueryBizData bizData = UserQueryBizData.builder()
                .userId(12345L)
                .queryType("public")
                .pageNum(1)
                .pageSize(10)
                .build();

        log.info("测试无Token接口调用 - 请求数据: {}", JSONUtil.toJsonPrettyStr(bizData));


        // 调用吉客云开放平台（不传递token）
        String response = JackYunOpenHttpClient.post(
                "public.info",    // 方法名
                "1.0",           // 版本号
                null,            // 不传递token
                bizData          // 业务数据
        );

        // 验证响应
        assertNotNull(response, "响应结果不能为空");
        log.info("无Token接口调用成功 - 响应: {}", response);


    }


    @Test
    @DisplayName("测试JSON序列化")
    void testJsonSerialization() {
        // 构造复杂的测试数据
        ProductSyncBizData bizData = ProductSyncBizData.builder()
                .productCode("PRD001")
                .productName("测试商品")
                .category("电子产品")
                .price("299.99")
                .stock(50)
                .build();

        // 测试JSON序列化
        String jsonStr = JSONUtil.toJsonStr(bizData);
        assertNotNull(jsonStr, "JSON序列化结果不能为空");
        assertTrue(jsonStr.contains("PRD001"), "JSON应包含商品编码");
        assertTrue(jsonStr.contains("测试商品"), "JSON应包含商品名称");

        log.info("JSON序列化测试通过 - 结果: {}", jsonStr);
    }

    @Test
    @DisplayName("测试签名生成逻辑")
    void testSignatureGeneration() {
        // 构造简单的测试数据来验证签名逻辑
        UserQueryBizData bizData = UserQueryBizData.builder()
                .userId(123L)
                .queryType("test")
                .build();

        // 调用API并观察签名生成过程（通过日志）
        JackYunOpenHttpClient.post("test.method", "1.0", testToken, bizData);

    }

    @Test
    @DisplayName("测试网络异常处理")
    void testNetworkExceptionHandling() {
        // 使用无效的网关地址测试网络异常处理
        UserQueryBizData bizData = UserQueryBizData.builder()
                .userId(12345L)
                .queryType("test")
                .build();


        // 这个调用预期会失败，用于测试异常处理
        JackYunOpenHttpClient.post("test.method", "1.0", testToken, bizData);

    }

    @Test
    @DisplayName("测试大数据量请求")
    void testLargeDataRequest() {
        // 构造大数据量测试
        StringBuilder largeContent = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            largeContent.append("测试数据").append(i).append(",");
        }

        ProductSyncBizData bizData = ProductSyncBizData.builder()
                .productCode("LARGE_DATA_TEST")
                .productName(largeContent.toString())
                .category("测试分类")
                .price("0.01")
                .stock(1)
                .build();

        log.info("测试大数据量请求 - 数据大小: {} 字符",
                JSONUtil.toJsonStr(bizData).length());


        String response = JackYunOpenHttpClient.post(
                "product.sync", "1.0", testToken, bizData);
        assertNotNull(response, "大数据量请求响应不能为空");
        log.info("大数据量请求测试通过");
    }
}