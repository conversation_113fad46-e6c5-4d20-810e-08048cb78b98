package com.spes.sop.third.kingdee.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.spes.sop.third.kingdee.service.KingdeeCloudService.KingdeeResult;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 金蝶云服务测试类
 * <AUTHOR>
 */
@SpringBootTest
public class KingdeeCloudServiceTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 测试金蝶返回结果的JSON反序列化
     * 验证修复后的JSON字段映射是否正确
     */
    @Test
    public void testKingdeeResultDeserialization() throws Exception {
        // 模拟金蝶实际返回的JSON响应（基于错误日志中的格式）
        String jsonResponse = """
            {
                "Result": {
                    "ResponseStatus": {
                        "ErrorCode": 500,
                        "IsSuccess": false,
                        "Errors": [
                            {
                                "FieldName": "FNumber",
                                "Message": "系统中已存在相同编码SKU2506201421225696-2324的物料，保存失败，请修改。",
                                "DIndex": 0
                            }
                        ],
                        "SuccessEntitys": [],
                        "SuccessMessages": [],
                        "MsgCode": 11
                    },
                    "Id": "",
                    "Number": ""
                }
            }
            """;

        // 执行反序列化
        KingdeeResult result = objectMapper.readValue(jsonResponse, KingdeeResult.class);

        // 验证反序列化结果
        assertNotNull(result, "反序列化结果不应为空");
        assertNotNull(result.getResult(), "Result字段不应为空");
        assertNotNull(result.getResult().getResponseStatus(), "ResponseStatus字段不应为空");

        // 验证ResponseStatus字段
        KingdeeCloudService.ResultStatus status = result.getResult().getResponseStatus();
        assertEquals(500, status.getErrorCode(), "错误码应为500");
        assertFalse(status.getIsSuccess(), "IsSuccess应为false");
        assertEquals(11, status.getMsgCode(), "MsgCode应为11");

        // 验证错误信息
        assertNotNull(status.getErrors(), "错误列表不应为空");
        assertEquals(1, status.getErrors().size(), "应有1个错误");
        
        KingdeeCloudService.KingdeeError error = status.getErrors().get(0);
        assertEquals("FNumber", error.getFieldName(), "字段名应为FNumber");
        assertTrue(error.getMessage().contains("系统中已存在相同编码"), "错误消息应包含预期文本");
        assertEquals(0, error.getDIndex(), "DIndex应为0");

        // 验证其他字段
        assertEquals("", result.getResult().getId(), "Id应为空字符串");
        assertEquals("", result.getResult().getNumber(), "Number应为空字符串");
        assertNotNull(status.getSuccessEntitys(), "SuccessEntitys不应为空");
        assertNotNull(status.getSuccessMessages(), "SuccessMessages不应为空");
    }

    /**
     * 测试成功响应的反序列化
     */
    @Test
    public void testSuccessfulKingdeeResultDeserialization() throws Exception {
        String successJsonResponse = """
            {
                "Result": {
                    "ResponseStatus": {
                        "ErrorCode": 0,
                        "IsSuccess": true,
                        "Errors": [],
                        "SuccessEntitys": [{"Id": "123", "Number": "MAT001"}],
                        "SuccessMessages": ["保存成功"],
                        "MsgCode": 0
                    },
                    "Id": "123",
                    "Number": "MAT001"
                }
            }
            """;

        KingdeeResult result = objectMapper.readValue(successJsonResponse, KingdeeResult.class);

        assertNotNull(result);
        assertNotNull(result.getResult());
        assertNotNull(result.getResult().getResponseStatus());

        KingdeeCloudService.ResultStatus status = result.getResult().getResponseStatus();
        assertEquals(0, status.getErrorCode());
        assertTrue(status.getIsSuccess());
        assertEquals(0, status.getMsgCode());
        assertEquals("123", result.getResult().getId());
        assertEquals("MAT001", result.getResult().getNumber());
    }
}
