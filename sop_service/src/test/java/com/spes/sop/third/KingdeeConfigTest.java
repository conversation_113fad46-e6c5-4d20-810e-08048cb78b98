/*
 * Copyright(C) 2024 Spes Co., Ltd. All rights reserved.
 */

package com.spes.sop.third;

import com.spes.sop.third.kingdee.config.KingdeeConfig;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * 金蝶配置测试类
 * 专门用于测试配置注入是否正确
 *
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest(classes = {KingdeeConfig.class})
@EnableConfigurationProperties(KingdeeConfig.class)
@TestPropertySource(properties = {
        "kingdee.serverUrl=http://192.168.50.56/k3cloud/",
        "kingdee.acctId=66d67292cd0454",
        "kingdee.username=administrator",
        "kingdee.password=kingdee123@",
        "kingdee.lcid=2052",
        "kingdee.connectTimeout=10000",
        "kingdee.readTimeout=30000",
        "kingdee.sessionExpireTime=3600000"
})
@DisplayName("金蝶配置测试")
class KingdeeConfigTest {

    @Autowired
    private KingdeeConfig kingdeeConfig;

    @Test
    @DisplayName("测试配置属性注入")
    void testConfigProperties() {
        log.info("开始测试金蝶配置属性注入...");

        // 验证配置对象不为空
        assertNotNull(kingdeeConfig, "金蝶配置对象应该不为空");

        // 验证各个配置属性
        assertNotNull(kingdeeConfig.getServerUrl(), "服务器地址不应该为空");
        assertEquals("http://192.168.50.56/k3cloud/", kingdeeConfig.getServerUrl(), "服务器地址应该匹配");

        assertNotNull(kingdeeConfig.getAcctId(), "账套ID不应该为空");
        assertEquals("66d67292cd0454", kingdeeConfig.getAcctId(), "账套ID应该匹配");

        assertNotNull(kingdeeConfig.getUsername(), "用户名不应该为空");
        assertEquals("administrator", kingdeeConfig.getUsername(), "用户名应该匹配");

        assertNotNull(kingdeeConfig.getPassword(), "密码不应该为空");
        assertEquals("kingdee123@", kingdeeConfig.getPassword(), "密码应该匹配");

        assertNotNull(kingdeeConfig.getLcid(), "语言ID不应该为空");
        assertEquals(Integer.valueOf(2052), kingdeeConfig.getLcid(), "语言ID应该匹配");

        assertNotNull(kingdeeConfig.getConnectTimeout(), "连接超时不应该为空");
        assertEquals(Integer.valueOf(10000), kingdeeConfig.getConnectTimeout(), "连接超时应该匹配");

        assertNotNull(kingdeeConfig.getReadTimeout(), "读取超时不应该为空");
        assertEquals(Integer.valueOf(30000), kingdeeConfig.getReadTimeout(), "读取超时应该匹配");

        assertNotNull(kingdeeConfig.getSessionExpireTime(), "会话过期时间不应该为空");
        assertEquals(Long.valueOf(3600000L), kingdeeConfig.getSessionExpireTime(), "会话过期时间应该匹配");

        // 输出配置信息
        log.info("✅ 金蝶配置属性验证成功:");
        log.info("  服务器地址: {}", kingdeeConfig.getServerUrl());
        log.info("  账套ID: {}", kingdeeConfig.getAcctId());
        log.info("  用户名: {}", kingdeeConfig.getUsername());
        log.info("  密码: {}", "***");  // 不输出真实密码
        log.info("  语言ID: {}", kingdeeConfig.getLcid());
        log.info("  连接超时: {}ms", kingdeeConfig.getConnectTimeout());
        log.info("  读取超时: {}ms", kingdeeConfig.getReadTimeout());
        log.info("  会话过期时间: {}ms", kingdeeConfig.getSessionExpireTime());
    }
} 