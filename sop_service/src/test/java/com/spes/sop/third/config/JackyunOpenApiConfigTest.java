/*
 * Copyright(C) 2024 Spes Co., Ltd. All rights reserved.
 */

package com.spes.sop.third.config;

import com.spes.sop.third.jackyun.config.JackyunOpenApiConfig;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 吉客云开放平台配置测试类
 * 验证配置文件中的吉客云相关配置是否正确读取
 *
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE,
        classes = {JackyunOpenApiConfig.class})
@TestPropertySource(properties = {
        "jackyun.open.api.app-key=73914332",
        "jackyun.open.api.app-secret=a664417f04db4f0086a77fb0a16c99d1",
        "jackyun.open.api.gateway=https://open.jackyun.com/open/openapi/do",
        "jackyun.open.api.default-version=1.0",
        "jackyun.open.api.encoding=UTF-8"
})
@DisplayName("吉客云开放平台配置测试")
class JackyunOpenApiConfigTest {

    @Autowired(required = false)
    private JackyunOpenApiConfig jackyunConfig;

    // 从配置文件直接读取值进行验证
    @Value("${jackyun.server-url:}")
    private String serverUrl;

    @Value("${jackyun.api-version:}")
    private String apiVersion;

    @Value("${jackyun.app-key:}")
    private String appKey;

    @Value("${jackyun.app-secret:}")
    private String appSecret;

    @Test
    @DisplayName("测试配置类加载")
    void testConfigurationLoading() {
        // 验证配置类是否正确注入
        if (jackyunConfig != null) {
            log.info("配置类加载成功");

            // 验证默认配置值
            assertNotNull(jackyunConfig.getAppKey(), "AppKey不应为空");
            assertNotNull(jackyunConfig.getAppSecret(), "AppSecret不应为空");
            assertNotNull(jackyunConfig.getServerUrl(), "Gateway不应为空");

            log.info("配置详情:");
            log.info("  AppKey: {}", jackyunConfig.getAppKey());
            log.info("  AppSecret: {}****", jackyunConfig.getAppSecret().substring(0, 8));
            log.info("  Gateway: {}", jackyunConfig.getServerUrl());
            log.info("  DefaultVersion: {}", jackyunConfig.getApiVersion());
            log.info("  ConnectTimeout: {}ms", jackyunConfig.getConnectTimeout());
            log.info("  ReadTimeout: {}ms", jackyunConfig.getReadTimeout());
            log.info("  Encoding: {}", jackyunConfig.getEncoding());
            log.info("  DebugEnabled: {}", jackyunConfig.getDebugEnabled());
            log.info("  RetryCount: {}", jackyunConfig.getRetryCount());
        } else {
            log.warn("配置类未加载，可能配置前缀不匹配");
        }
    }

    @Test
    @DisplayName("测试配置文件值读取")
    void testConfigurationValues() {
        log.info("从配置文件读取的值:");
        log.info("  server-url: {}", serverUrl);
        log.info("  api-version: {}", apiVersion);
        log.info("  app-key: {}", appKey);
        log.info("  app-secret: {}****",
                appSecret.length() > 8 ? appSecret.substring(0, 8) : appSecret);

        // 验证关键配置不为空（基于yml文件中的配置）
        assertNotNull(appKey, "AppKey应从配置文件正确读取");
        assertNotNull(appSecret, "AppSecret应从配置文件正确读取");

        // 验证具体的配置值（基于dev环境配置）
        assertEquals("73914332", appKey, "AppKey应与配置文件中的值匹配");
        assertEquals("a664417f04db4f0086a77fb0a16c99d1", appSecret, "AppSecret应与配置文件中的值匹配");
    }

    @Test
    @DisplayName("测试配置值有效性")
    void testConfigurationValidity() {
        if (jackyunConfig != null) {
            // 验证AppKey格式（应为数字）
            assertTrue(jackyunConfig.getAppKey().matches("\\d+"),
                    "AppKey应为数字格式");

            // 验证AppSecret格式（应为32位十六进制字符串）
            assertTrue(jackyunConfig.getAppSecret().matches("[a-f0-9]{32}"),
                    "AppSecret应为32位十六进制字符串");

            // 验证Gateway URL格式
            assertTrue(jackyunConfig.getServerUrl().startsWith("http"),
                    "Gateway应为有效的HTTP URL");

            // 验证超时时间设置合理
            assertTrue(jackyunConfig.getConnectTimeout() > 0,
                    "连接超时时间应大于0");
            assertTrue(jackyunConfig.getReadTimeout() > 0,
                    "读取超时时间应大于0");

            // 验证重试次数合理
            assertTrue(jackyunConfig.getRetryCount() >= 0,
                    "重试次数应大于等于0");
            assertTrue(jackyunConfig.getRetryCount() <= 10,
                    "重试次数不应过大");

            log.info("所有配置值验证通过");
        }
    }

    @Test
    @DisplayName("测试配置默认值")
    void testDefaultValues() {
        if (jackyunConfig != null) {
            // 验证默认值设置
            assertEquals("1.0", jackyunConfig.getApiVersion(),
                    "默认版本应为1.0");
            assertEquals("UTF-8", jackyunConfig.getEncoding(),
                    "默认编码应为UTF-8");
            assertEquals(300000, jackyunConfig.getConnectTimeout(),
                    "默认连接超时时间应为300000毫秒");
            assertEquals(300000, jackyunConfig.getReadTimeout(),
                    "默认读取超时时间应为300000毫秒");
            assertEquals(3, jackyunConfig.getRetryCount(),
                    "默认重试次数应为3");
            assertEquals(false, jackyunConfig.getDebugEnabled(),
                    "默认调试模式应为关闭");

            log.info("所有默认值验证通过");
        }
    }

    @Test
    @DisplayName("测试配置安全性")
    void testConfigurationSecurity() {
        if (jackyunConfig != null) {
            // 验证敏感信息不为空
            assertFalse(jackyunConfig.getAppSecret().isEmpty(),
                    "AppSecret不应为空");

            // 验证AppSecret长度合理（不应过短）
            assertTrue(jackyunConfig.getAppSecret().length() >= 16,
                    "AppSecret长度应足够安全");

            // 验证生产环境配置建议
            if (jackyunConfig.getDebugEnabled()) {
                log.warn("注意：调试模式已启用，生产环境应关闭");
            }

            log.info("配置安全性检查通过");
        }
    }

    @Test
    @DisplayName("测试配置环境区分")
    void testEnvironmentSpecificConfig() {
        // 验证当前是开发环境配置
        log.info("当前环境: dev");

        if (jackyunConfig != null) {
            // 开发环境特定验证
            assertNotNull(jackyunConfig.getAppKey(), "开发环境AppKey应配置");
            assertNotNull(jackyunConfig.getAppSecret(), "开发环境AppSecret应配置");

            // 记录环境特定的配置信息
            log.info("开发环境配置验证:");
            log.info("  Gateway: {}", jackyunConfig.getServerUrl());
            log.info("  Timeout设置: Connect={}ms, Read={}ms",
                    jackyunConfig.getConnectTimeout(), jackyunConfig.getReadTimeout());

            log.info("环境配置验证通过");
        }
    }
} 