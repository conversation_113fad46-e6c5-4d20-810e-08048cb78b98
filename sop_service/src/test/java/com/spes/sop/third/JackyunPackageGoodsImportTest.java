package com.spes.sop.third;

import com.spes.sop.third.jackyun.client.JackYunOpenHttpClient;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 杰客云组合装导入测试
 *
 * <AUTHOR>
 */
@Slf4j
public class JackyunPackageGoodsImportTest {

    /**
     * 测试组合装导入（只填必填项）
     */
    @Test
    public void testPackageGoodsImport() {
        try {
            // 构建组合装导入数据（只填必填项）
            Map<String, Object> packageGoodsData = buildPackageGoodsData();

            // 调用杰客云API
            String response = JackYunOpenHttpClient.post(
                    "erp.packagegoods.import",  // API方法名
                    "1.0",                      // API版本
                    null,                       // token（如果需要可以设置）
                    packageGoodsData            // 业务数据
            );

            log.info("组合装导入响应结果: {}", response);

            // 可以在这里添加响应结果的断言验证
            // assert response != null;
            // assert response.contains("success");

        } catch (Exception e) {
            log.error("组合装导入测试失败", e);
            throw new RuntimeException("组合装导入测试失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建组合装导入数据（只包含必填项）
     *
     * @return 组合装导入数据
     */
    private Map<String, Object> buildPackageGoodsData() {
        Map<String, Object> data = new HashMap<>();

        // 组合装基本信息（必填项）
        data.put("goodsNo", "ZH" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))); // 商品编码
        data.put("goodsName", "测试组合装商品");  // 商品名称
        data.put("unitName", "件");           // 组合装类型：1-普通组合装

        // 组合装子商品列表（必填项）
        List<Map<String, Object>> subGoodsList = new ArrayList<>();

        // 子商品1
        Map<String, Object> subGoods1 = new HashMap<>();
        subGoods1.put("skuBarcode", "cs69000001");     // 子商品编码（必填）
        subGoods1.put("goodsAmount", 2);                // 子商品数量（必填）
        subGoods1.put("sharePrice", 3);
        subGoodsList.add(subGoods1);

        // 子商品2
        Map<String, Object> subGoods2 = new HashMap<>();
        subGoods2.put("skuBarcode", "cs-01");     // 子商品编码（必填）
        subGoods2.put("goodsAmount", 1);                 // 子商品数量（必填）
        subGoods2.put("sharePrice", 3);
        subGoodsList.add(subGoods2);

        data.put("packageSkuList", subGoodsList); // 子商品列表（必填）

        log.info("构建组合装导入数据: {}", data);
        return data;
    }

    /**
     * 测试组合装导入（包含更多字段）
     */
    @Test
    public void testPackageGoodsImportWithMoreFields() {
        try {
            // 构建包含更多字段的组合装导入数据
            Map<String, Object> packageGoodsData = buildPackageGoodsDataWithMoreFields();

            // 调用杰客云API
            String response = JackYunOpenHttpClient.post(
                    "erp.packagegoods.import",
                    "1.0",
                    null,
                    packageGoodsData
            );

            log.info("组合装导入（含更多字段）响应结果: {}", response);

        } catch (Exception e) {
            log.error("组合装导入（含更多字段）测试失败", e);
            throw new RuntimeException("组合装导入测试失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建包含更多字段的组合装导入数据
     *
     * @return 组合装导入数据
     */
    private Map<String, Object> buildPackageGoodsDataWithMoreFields() {
        Map<String, Object> data = new HashMap<>();

        // 组合装基本信息
        data.put("goods_no", "ZH" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        data.put("goods_name", "高级测试组合装商品");
        data.put("goods_name_en", "Advanced Test Package Goods");  // 英文名称
        data.put("package_type", "1");
        data.put("status", "1");
        data.put("remark", "这是一个测试组合装商品");               // 备注
        data.put("cost_price", "99.99");                          // 成本价
        data.put("sale_price", "199.99");                         // 销售价

        // 组合装子商品列表
        List<Map<String, Object>> subGoodsList = new ArrayList<>();

        // 子商品1（带更多字段）
        Map<String, Object> subGoods1 = new HashMap<>();
        subGoods1.put("goods_no", "SKU001");
        subGoods1.put("qty", 2);
        subGoods1.put("remark", "主要组件");                       // 子商品备注
        subGoodsList.add(subGoods1);

        // 子商品2（带更多字段）
        Map<String, Object> subGoods2 = new HashMap<>();
        subGoods2.put("goods_no", "SKU002");
        subGoods2.put("qty", 1);
        subGoods2.put("remark", "辅助组件");
        subGoodsList.add(subGoods2);

        // 子商品3
        Map<String, Object> subGoods3 = new HashMap<>();
        subGoods3.put("goods_no", "SKU003");
        subGoods3.put("qty", 3);
        subGoods3.put("remark", "配件");
        subGoodsList.add(subGoods3);

        data.put("sub_goods_list", subGoodsList);

        log.info("构建组合装导入数据（含更多字段）: {}", data);
        return data;
    }
} 