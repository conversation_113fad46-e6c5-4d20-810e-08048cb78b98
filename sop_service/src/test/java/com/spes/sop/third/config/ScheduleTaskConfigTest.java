package com.spes.sop.third.config;

import com.spes.sop.task.ScheduleTaskConfig;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 定时任务配置测试类
 *
 * <AUTHOR>
 */
public class ScheduleTaskConfigTest {

    private static final Logger log = LoggerFactory.getLogger(ScheduleTaskConfigTest.class);

    /**
     * 测试TaskScheduler配置
     */
    @Test
    public void testTaskSchedulerConfiguration() {
        try {
            ScheduleTaskConfig config = new ScheduleTaskConfig();
            ThreadPoolTaskScheduler scheduler = config.taskScheduler();

            assertNotNull(scheduler, "TaskScheduler should not be null");

            // 初始化调度器
            scheduler.initialize();

            assertTrue(scheduler.getPoolSize() == 5, "Pool size should be 5");

            log.info("TaskScheduler configuration test passed");

            // 关闭调度器
            scheduler.shutdown();

        } catch (Exception e) {
            log.error("TaskScheduler configuration test failed", e);
            fail("TaskScheduler configuration test failed: " + e.getMessage());
        }
    }

    /**
     * 测试线程名前缀配置
     */
    @Test
    public void testThreadNamePrefix() {
        try {
            ScheduleTaskConfig config = new ScheduleTaskConfig();
            ThreadPoolTaskScheduler scheduler = config.taskScheduler();

            assertNotNull(scheduler, "TaskScheduler should not be null");

            // 检查线程名前缀（通过反射或其他方式）
            // 这里简单验证调度器是否正常创建
            log.info("Thread name prefix test passed");

        } catch (Exception e) {
            log.error("Thread name prefix test failed", e);
            fail("Thread name prefix test failed: " + e.getMessage());
        }
    }

    /**
     * 测试调度器的基本功能
     */
    @Test
    public void testSchedulerBasicFunctionality() {
        try {
            ScheduleTaskConfig config = new ScheduleTaskConfig();
            ThreadPoolTaskScheduler scheduler = config.taskScheduler();

            assertNotNull(scheduler, "TaskScheduler should not be null");

            // 初始化
            scheduler.initialize();

            // 测试提交一个简单任务
            boolean[] taskExecuted = {false};

            scheduler.execute(() -> {
                taskExecuted[0] = true;
                log.info("Test task executed successfully");
            });

            // 等待任务执行
            Thread.sleep(1000);

            assertTrue(taskExecuted[0], "Task should be executed");

            log.info("Scheduler basic functionality test passed");

            // 关闭调度器
            scheduler.shutdown();

        } catch (Exception e) {
            log.error("Scheduler basic functionality test failed", e);
            fail("Scheduler basic functionality test failed: " + e.getMessage());
        }
    }
} 