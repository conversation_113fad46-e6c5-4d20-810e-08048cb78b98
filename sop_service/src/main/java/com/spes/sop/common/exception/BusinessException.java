package com.spes.sop.common.exception;

import lombok.Getter;

/**
 * 业务异常类
 * 用于处理业务逻辑中的异常情况
 *
 * <AUTHOR>

 */
@Getter
public class BusinessException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private final Integer code;

    /**
     * 错误消息
     */
    private final String message;

    /**
     * 构造函数 - 使用默认业务错误码
     */
    public BusinessException(String message) {
        super(message);
        this.code = 201; // 默认业务错误码
        this.message = message;
    }

    /**
     * 构造函数 - 自定义错误码和消息
     */
    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    /**
     * 构造函数 - 包含原因异常
     */
    public BusinessException(String message, Throwable cause) {
        super(message, cause);
        this.code = 201;
        this.message = message;
    }

    /**
     * 构造函数 - 包含原因异常和自定义错误码
     */
    public BusinessException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }
} 
