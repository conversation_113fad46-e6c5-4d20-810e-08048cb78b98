package com.spes.sop.common.enums;

import cn.hutool.core.util.ObjectUtil;
import lombok.Getter;

/**
 * 审核状态枚举
 */
public enum OaStatusEnum {

    CREATED("已创建"),
    APPROVING("审批中"),
    APPROVED("审批通过"),
    REJECTED("审批未通过"),
    CANCELED("已取消"),
    SYNC_FAILED("同步失败");

    @Getter
    private final String desc;

    OaStatusEnum(String desc) {
        this.desc = desc;
    }

    public static OaStatusEnum getByName(String code) {
        if (ObjectUtil.isNull(code)) {
            return null;
        }
        for (OaStatusEnum value : values()) {
            if (value.name().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
