package com.spes.sop.common.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

/**
 * JSON工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class JsonUtils {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * 对象转JSON字符串
     *
     * @param object 要转换的对象
     * @return JSON字符串，转换失败返回null
     */
    public static String toJsonString(Object object) {
        if (object == null) {
            return null;
        }

        try {
            return OBJECT_MAPPER.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("对象转JSON失败: {}", object.getClass().getSimpleName(), e);
            return null;
        }
    }

    /**
     * JSON字符串转对象
     *
     * @param jsonString JSON字符串
     * @param clazz      目标对象类型
     * @param <T>        泛型类型
     * @return 转换后的对象，转换失败返回null
     */
    public static <T> T fromJsonString(String jsonString, Class<T> clazz) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }

        try {
            return OBJECT_MAPPER.readValue(jsonString, clazz);
        } catch (JsonProcessingException e) {
            log.error("JSON转对象失败: {}, json: {}", clazz.getSimpleName(), jsonString, e);
            return null;
        }
    }

    /**
     * JSON字符串转对象，支持复杂泛型
     *
     * @param jsonString JSON字符串
     * @param type       目标类型
     * @param <T>        泛型类型
     * @return 转换后的对象，转换失败返回null
     */
    public static <T> T fromJsonString(String jsonString, TypeReference<T> type) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }

        try {
            return OBJECT_MAPPER.readValue(jsonString, type);
        } catch (JsonProcessingException e) {
            log.error("JSON转对象失败: {}, json: {}", type.getType(), jsonString, e);
            return null;
        }
    }

    /**
     * 安全的对象转JSON字符串，带默认值
     *
     * @param object       要转换的对象
     * @param defaultValue 默认值
     * @return JSON字符串，转换失败返回默认值
     */
    public static String toJsonStringOrDefault(Object object, String defaultValue) {
        String result = toJsonString(object);
        return result != null ? result : defaultValue;
    }

    /**
     * 安全的JSON字符串转对象，带默认值
     *
     * @param jsonString   JSON字符串
     * @param clazz        目标对象类型
     * @param defaultValue 默认值
     * @param <T>          泛型类型
     * @return 转换后的对象，转换失败返回默认值
     */
    public static <T> T fromJsonStringOrDefault(String jsonString, Class<T> clazz, T defaultValue) {
        T result = fromJsonString(jsonString, clazz);
        return result != null ? result : defaultValue;
    }
} 