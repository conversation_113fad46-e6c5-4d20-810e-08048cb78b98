package com.spes.sop.common.config;

import com.spes.sop.common.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * IdWorker配置类
 * 用于配置分布式ID生成器
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "sop.id-worker")
public class IdWorkerConfig {

    /**
     * 工作机器ID (0~31)
     * 默认值：1
     */
    private long workerId = 1L;

    /**
     * 数据中心ID (0~31)
     * 默认值：1
     */
    private long datacenterId = 1L;

    /**
     * 创建IdWorker Bean
     *
     * @return IdWorker实例
     */
    @Bean
    public IdWorker idWorker() {
        log.info("初始化IdWorker，workerId: {}, datacenterId: {}", workerId, datacenterId);
        return new IdWorker(workerId, datacenterId);
    }

    public long getWorkerId() {
        return workerId;
    }

    public void setWorkerId(long workerId) {
        this.workerId = workerId;
    }

    public long getDatacenterId() {
        return datacenterId;
    }

    public void setDatacenterId(long datacenterId) {
        this.datacenterId = datacenterId;
    }
} 