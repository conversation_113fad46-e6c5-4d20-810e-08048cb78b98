package com.spes.sop.common.util;

import com.spes.sop.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;

/**
 * 基于雪花算法的分布式ID生成器
 * <p>
 * 雪花算法ID结构（64位）：
 * 1位符号位（固定为0） + 41位时间戳 + 10位机器ID（5位数据中心ID + 5位工作机器ID） + 12位序列号
 * <p>
 * 特点：
 * - 趋势递增：大体上按时间自增排序，并且整个分布式系统内不会产生ID碰撞
 * - 高性能：每秒能够产生26万ID左右
 * - 分布式：可以在分布式系统中使用，不依赖数据库等第三方系统
 * - 无重复：整个分布式系统内不会产生重复ID
 *
 * <AUTHOR>
 */
@Slf4j
public class IdWorker {

    /**
     * 开始时间戳 (2023-01-01 00:00:00)
     * 可以使用约69年 (1L << 41) / (365L * 24 * 60 * 60 * 1000)
     */
    private static final long START_TIMESTAMP = 1672531200000L;

    /**
     * 序列号占用的位数
     */
    private static final long SEQUENCE_BITS = 12L;

    /**
     * 机器ID占用的位数
     */
    private static final long WORKER_ID_BITS = 5L;

    /**
     * 数据中心ID占用的位数
     */
    private static final long DATACENTER_ID_BITS = 5L;

    /**
     * 支持的最大机器ID，结果是31 (这个移位算法可以很快的计算出几位二进制数所能表示的最大十进制数)
     */
    private static final long MAX_WORKER_ID = ~(-1L << WORKER_ID_BITS);

    /**
     * 支持的最大数据中心ID，结果是31
     */
    private static final long MAX_DATACENTER_ID = ~(-1L << DATACENTER_ID_BITS);

    /**
     * 生成序列的掩码，这里为4095 (0b111111111111=0xfff=4095)
     */
    private static final long SEQUENCE_MASK = ~(-1L << SEQUENCE_BITS);

    /**
     * 机器ID向左移12位
     */
    private static final long WORKER_ID_SHIFT = SEQUENCE_BITS;

    /**
     * 数据中心ID向左移17位(12+5)
     */
    private static final long DATACENTER_ID_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS;

    /**
     * 时间戳向左移22位(5+5+12)
     */
    private static final long TIMESTAMP_LEFT_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS + DATACENTER_ID_BITS;

    /**
     * 工作机器ID(0~31)
     */
    private final long workerId;

    /**
     * 数据中心ID(0~31)
     */
    private final long datacenterId;

    /**
     * 毫秒内序列(0~4095)
     */
    private long sequence = 0L;

    /**
     * 上次生成ID的时间戳
     */
    private long lastTimestamp = -1L;

    /**
     * 单例实例
     */
    private static volatile IdWorker instance;

    /**
     * 构造函数
     *
     * @param workerId     工作机器ID (0~31)
     * @param datacenterId 数据中心ID (0~31)
     */
    public IdWorker(long workerId, long datacenterId) {
        if (workerId > MAX_WORKER_ID || workerId < 0) {
            throw new IllegalArgumentException(
                    String.format("Worker ID can't be greater than %d or less than 0", MAX_WORKER_ID));
        }
        if (datacenterId > MAX_DATACENTER_ID || datacenterId < 0) {
            throw new IllegalArgumentException(
                    String.format("Datacenter ID can't be greater than %d or less than 0", MAX_DATACENTER_ID));
        }
        this.workerId = workerId;
        this.datacenterId = datacenterId;

        log.info("IdWorker初始化完成，workerId: {}, datacenterId: {}", workerId, datacenterId);
    }

    /**
     * 获取单例实例（使用默认配置）
     * 默认workerId=1, datacenterId=1
     *
     * @return IdWorker实例
     */
    public static IdWorker getInstance() {
        if (instance == null) {
            synchronized (IdWorker.class) {
                if (instance == null) {
                    // 默认配置：workerId=1, datacenterId=1
                    instance = new IdWorker(1L, 1L);
                }
            }
        }
        return instance;
    }

    /**
     * 获取单例实例（使用指定配置）
     *
     * @param workerId     工作机器ID
     * @param datacenterId 数据中心ID
     * @return IdWorker实例
     */
    public static IdWorker getInstance(long workerId, long datacenterId) {
        if (instance == null) {
            synchronized (IdWorker.class) {
                if (instance == null) {
                    instance = new IdWorker(workerId, datacenterId);
                }
            }
        }
        return instance;
    }

    /**
     * 获得下一个ID (该方法是线程安全的)
     *
     * @return SnowflakeId
     */
    public synchronized long nextId() {
        long timestamp = timeGen();

        // 如果当前时间小于上一次ID生成的时间戳，说明系统时钟回退过这个时候应当抛出异常
        if (timestamp < lastTimestamp) {
            long offset = lastTimestamp - timestamp;
            if (offset <= 5) {
                try {
                    // 时间偏差大小小于5ms，则等待两倍时间
                    wait(offset << 1);
                    timestamp = timeGen();
                    if (timestamp < lastTimestamp) {
                        throw new BusinessException(
                                String.format("Clock moved backwards. Refusing to generate id for %d milliseconds",
                                        lastTimestamp - timestamp));
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new BusinessException("IdWorker interrupted", e);
                }
            } else {
                throw new BusinessException(
                        String.format("Clock moved backwards. Refusing to generate id for %d milliseconds",
                                lastTimestamp - timestamp));
            }
        }

        // 如果是同一时间生成的，则进行毫秒内序列
        if (lastTimestamp == timestamp) {
            sequence = (sequence + 1) & SEQUENCE_MASK;
            // 毫秒内序列溢出
            if (sequence == 0) {
                // 阻塞到下一个毫秒,获得新的时间戳
                timestamp = tilNextMillis(lastTimestamp);
            }
        } else {
            // 时间戳改变，毫秒内序列重置
            sequence = 0L;
        }

        // 上次生成ID的时间戳
        lastTimestamp = timestamp;

        // 移位并通过或运算拼到一起组成64位的ID
        return ((timestamp - START_TIMESTAMP) << TIMESTAMP_LEFT_SHIFT)
                | (datacenterId << DATACENTER_ID_SHIFT)
                | (workerId << WORKER_ID_SHIFT)
                | sequence;
    }

    /**
     * 阻塞到下一个毫秒，直到获得新的时间戳
     *
     * @param lastTimestamp 上次生成ID的时间戳
     * @return 当前时间戳
     */
    protected long tilNextMillis(long lastTimestamp) {
        long timestamp = timeGen();
        while (timestamp <= lastTimestamp) {
            timestamp = timeGen();
        }
        return timestamp;
    }

    /**
     * 返回以毫秒为单位的当前时间
     *
     * @return 当前时间(毫秒)
     */
    protected long timeGen() {
        return System.currentTimeMillis();
    }

    /**
     * 解析雪花算法生成的ID
     *
     * @param id 雪花算法生成的ID
     * @return ID信息
     */
    public static IdInfo parseId(long id) {
        long timestamp = (id >> TIMESTAMP_LEFT_SHIFT) + START_TIMESTAMP;
        long datacenterId = (id >> DATACENTER_ID_SHIFT) & ~(-1L << DATACENTER_ID_BITS);
        long workerId = (id >> WORKER_ID_SHIFT) & ~(-1L << WORKER_ID_BITS);
        long sequence = id & SEQUENCE_MASK;

        return new IdInfo(id, timestamp, datacenterId, workerId, sequence);
    }

    /**
     * 生成下一个ID的静态方法（便捷方法）
     *
     * @return 生成的ID
     */
    public static long generateId() {
        return getInstance().nextId();
    }

    /**
     * 生成指定数量的ID
     *
     * @param count 生成数量
     * @return ID数组
     */
    public long[] nextIds(int count) {
        if (count <= 0) {
            throw new IllegalArgumentException("Count must be positive");
        }

        long[] ids = new long[count];
        for (int i = 0; i < count; i++) {
            ids[i] = nextId();
        }
        return ids;
    }

    /**
     * 获取当前配置信息
     *
     * @return 配置信息字符串
     */
    public String getConfigInfo() {
        return String.format("IdWorker[workerId=%d, datacenterId=%d, startTimestamp=%d]",
                workerId, datacenterId, START_TIMESTAMP);
    }

    /**
     * ID信息类，用于解析雪花算法生成的ID
     */
    public static class IdInfo {
        private final long id;
        private final long timestamp;
        private final long datacenterId;
        private final long workerId;
        private final long sequence;

        public IdInfo(long id, long timestamp, long datacenterId, long workerId, long sequence) {
            this.id = id;
            this.timestamp = timestamp;
            this.datacenterId = datacenterId;
            this.workerId = workerId;
            this.sequence = sequence;
        }

        public long getId() {
            return id;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public long getDatacenterId() {
            return datacenterId;
        }

        public long getWorkerId() {
            return workerId;
        }

        public long getSequence() {
            return sequence;
        }

        @Override
        public String toString() {
            return String.format("IdInfo{id=%d, timestamp=%d, datacenterId=%d, workerId=%d, sequence=%d}",
                    id, timestamp, datacenterId, workerId, sequence);
        }
    }
} 