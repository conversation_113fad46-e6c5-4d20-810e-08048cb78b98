package com.spes.sop.common.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.spes.sop.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT工具类
 *
 * <AUTHOR>

 */
@Slf4j
public class JwtUtil {

    /**
     * JWT签名密钥（生产环境应从配置文件读取）
     */
    private static final String SECRET = "sop_secret_key_2024";

    /**
     * JWT签发者
     */
    private static final String ISSUER = "sop_system";

    /**
     * 默认过期时间：7天（毫秒）
     */
    private static final long DEFAULT_EXPIRE_TIME = 7 * 24 * 60 * 60 * 1000L;

    /**
     * 生成JWT令牌
     *
     * @param userId     用户ID
     * @param employeeId 工号
     * @param username   用户名
     * @param role       角色
     * @return JWT令牌
     */
    public static String generateToken(Long userId, String employeeId, String username, String role) {
        return generateToken(userId, employeeId, username, role, DEFAULT_EXPIRE_TIME);
    }

    /**
     * 生成JWT令牌（自定义过期时间）
     *
     * @param userId     用户ID
     * @param employeeId 工号
     * @param username   用户名
     * @param role       角色
     * @param expireTime 过期时间（毫秒）
     * @return JWT令牌
     */
    public static String generateToken(Long userId, String employeeId, String username, String role, long expireTime) {
        try {
            Date expireDate = new Date(System.currentTimeMillis() + expireTime);

            Algorithm algorithm = Algorithm.HMAC256(SECRET);

            Map<String, Object> header = new HashMap<>();
            header.put("typ", "JWT");
            header.put("alg", "HS256");

            return JWT.create()
                    .withHeader(header)
                    .withIssuer(ISSUER)
                    .withSubject(String.valueOf(userId))
                    .withClaim("userId", userId)
                    .withClaim("employeeId", employeeId)
                    .withClaim("username", username)
                    .withClaim("role", role)
                    .withIssuedAt(new Date())
                    .withExpiresAt(expireDate)
                    .sign(algorithm);
        } catch (Exception e) {
            log.error("生成JWT令牌失败: userId={}, employeeId={}, error={}", userId, employeeId, e.getMessage(), e);
            throw new BusinessException("生成JWT令牌失败", e);
        }
    }

    /**
     * 验证JWT令牌
     *
     * @param token JWT令牌
     * @return 是否有效
     */
    public static boolean verifyToken(String token) {
        try {
            if (!StringUtils.hasText(token)) {
                return false;
            }

            Algorithm algorithm = Algorithm.HMAC256(SECRET);
            JWTVerifier verifier = JWT.require(algorithm)
                    .withIssuer(ISSUER)
                    .build();

            verifier.verify(token);
            return true;
        } catch (JWTVerificationException e) {
            log.warn("JWT令牌验证失败: token={}, error={}", token, e.getMessage());
            return false;
        } catch (Exception e) {
            log.error("JWT令牌验证异常: token={}, error={}", token, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 解析JWT令牌
     *
     * @param token JWT令牌
     * @return 解析后的JWT信息
     */
    public static DecodedJWT decodeToken(String token) {
        try {
            if (!StringUtils.hasText(token)) {
                throw new IllegalArgumentException("JWT令牌不能为空");
            }

            Algorithm algorithm = Algorithm.HMAC256(SECRET);
            JWTVerifier verifier = JWT.require(algorithm)
                    .withIssuer(ISSUER)
                    .build();

            return verifier.verify(token);
        } catch (JWTVerificationException e) {
            log.warn("JWT令牌解析失败: token={}, error={}", token, e.getMessage());
            throw new BusinessException("JWT令牌无效或已过期", e);
        } catch (Exception e) {
            log.error("JWT令牌解析异常: token={}, error={}", token, e.getMessage(), e);
            throw new BusinessException("JWT令牌解析失败", e);
        }
    }

    /**
     * 从JWT令牌中获取用户ID
     *
     * @param token JWT令牌
     * @return 用户ID
     */
    public static Long getUserId(String token) {
        DecodedJWT decodedJWT = decodeToken(token);
        return decodedJWT.getClaim("userId").asLong();
    }

    /**
     * 从JWT令牌中获取工号
     *
     * @param token JWT令牌
     * @return 工号
     */
    public static String getEmployeeId(String token) {
        DecodedJWT decodedJWT = decodeToken(token);
        return decodedJWT.getClaim("employeeId").asString();
    }

    /**
     * 从JWT令牌中获取用户名
     *
     * @param token JWT令牌
     * @return 用户名
     */
    public static String getUsername(String token) {
        DecodedJWT decodedJWT = decodeToken(token);
        return decodedJWT.getClaim("username").asString();
    }

    /**
     * 从JWT令牌中获取角色
     *
     * @param token JWT令牌
     * @return 角色
     */
    public static String getRole(String token) {
        DecodedJWT decodedJWT = decodeToken(token);
        return decodedJWT.getClaim("role").asString();
    }

    /**
     * 获取JWT令牌的过期时间
     *
     * @param token JWT令牌
     * @return 过期时间
     */
    public static Date getExpireTime(String token) {
        DecodedJWT decodedJWT = decodeToken(token);
        return decodedJWT.getExpiresAt();
    }

    /**
     * 检查JWT令牌是否即将过期（1小时内）
     *
     * @param token JWT令牌
     * @return 是否即将过期
     */
    public static boolean isTokenExpiringSoon(String token) {
        try {
            Date expireTime = getExpireTime(token);
            long timeRemaining = expireTime.getTime() - System.currentTimeMillis();
            // 1小时 = 60 * 60 * 1000 毫秒
            return timeRemaining < 60 * 60 * 1000;
        } catch (Exception e) {
            log.warn("检查JWT令牌过期时间失败: token={}, error={}", token, e.getMessage());
            return true; // 发生异常时认为即将过期
        }
    }

    /**
     * 从Authorization头中提取JWT令牌
     *
     * @param authHeader Authorization头的值
     * @return JWT令牌
     */
    public static String extractTokenFromHeader(String authHeader) {
        if (!StringUtils.hasText(authHeader)) {
            return null;
        }

        if (authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }

        return authHeader;
    }
} 
