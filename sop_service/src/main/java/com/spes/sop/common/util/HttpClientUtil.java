package com.spes.sop.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

/**
 * HTTP客户端工具类
 *
 * <AUTHOR>

 */
@Slf4j
public class HttpClientUtil {

    private static RestTemplate restTemplate;

    /**
     * 初始化RestTemplate实例
     * 在配置类中调用此方法设置RestTemplate
     */
    public static void setRestTemplate(RestTemplate restTemplate) {
        HttpClientUtil.restTemplate = restTemplate;
    }

    /**
     * 获取RestTemplate实例
     * 如果没有设置，则创建默认实例
     */
    private static RestTemplate getRestTemplate() {
        if (restTemplate == null) {
            synchronized (HttpClientUtil.class) {
                if (restTemplate == null) {
                    restTemplate = createDefaultRestTemplate();
                }
            }
        }
        return restTemplate;
    }

    /**
     * 创建默认的RestTemplate实例
     */
    private static RestTemplate createDefaultRestTemplate() {
        RestTemplate template = new RestTemplate();
        // 可以在这里添加默认配置，如超时设置等
        return template;
    }

    /**
     * 发送GET请求
     *
     * @param url     请求URL
     * @param headers 请求头
     * @return 响应结果
     */
    public static String doGet(String url, Map<String, String> headers) {
        try {
            // 参数校验
            if (!StringUtils.hasText(url)) {
                throw new IllegalArgumentException("请求URL不能为空");
            }

            log.info("发送GET请求: url={}", url);

            // 构建请求头
            HttpHeaders httpHeaders = buildHttpHeaders(headers);
            HttpEntity<String> entity = new HttpEntity<>(httpHeaders);

            // 发送请求
            ResponseEntity<String> response = getRestTemplate().exchange(
                    url, HttpMethod.GET, entity, String.class);

            String responseBody = response.getBody();
            log.info("GET请求响应: status={}, body={}", response.getStatusCode(), responseBody);

            return responseBody;
        } catch (RestClientException e) {
            log.error("GET请求失败: url={}, error={}", url, e.getMessage(), e);
            throw new RuntimeException("GET请求失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("GET请求异常: url={}, error={}", url, e.getMessage(), e);
            throw new RuntimeException("GET请求异常: " + e.getMessage(), e);
        }
    }

    /**
     * 发送GET请求（无请求头）
     *
     * @param url 请求URL
     * @return 响应结果
     */
    public static String doGet(String url) {
        return doGet(url, null);
    }

    /**
     * 发送POST请求
     *
     * @param url         请求URL
     * @param requestBody 请求体
     * @param headers     请求头
     * @return 响应结果
     */
    public static String doPost(String url, String requestBody, Map<String, String> headers) {
        try {
            // 参数校验
            if (!StringUtils.hasText(url)) {
                throw new IllegalArgumentException("请求URL不能为空");
            }

            log.info("发送POST请求: url={}, body={}", url, requestBody);

            // 构建请求头
            HttpHeaders httpHeaders = buildHttpHeaders(headers);
            HttpEntity<String> entity = new HttpEntity<>(requestBody, httpHeaders);

            // 发送请求
            ResponseEntity<String> response = getRestTemplate().exchange(
                    url, HttpMethod.POST, entity, String.class);

            String responseBody = response.getBody();
            log.info("POST请求响应: status={}, body={}", response.getStatusCode(), responseBody);

            return responseBody;
        } catch (RestClientException e) {
            log.error("POST请求失败: url={}, error={}", url, e.getMessage(), e);
            throw new RuntimeException("POST请求失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("POST请求异常: url={}, error={}", url, e.getMessage(), e);
            throw new RuntimeException("POST请求异常: " + e.getMessage(), e);
        }
    }

    /**
     * 发送POST请求（无请求头）
     *
     * @param url         请求URL
     * @param requestBody 请求体
     * @return 响应结果
     */
    public static String doPost(String url, String requestBody) {
        return doPost(url, requestBody, null);
    }

    /**
     * 发送PUT请求
     *
     * @param url         请求URL
     * @param requestBody 请求体
     * @param headers     请求头
     * @return 响应结果
     */
    public static String doPut(String url, String requestBody, Map<String, String> headers) {
        try {
            // 参数校验
            if (!StringUtils.hasText(url)) {
                throw new IllegalArgumentException("请求URL不能为空");
            }

            log.info("发送PUT请求: url={}, body={}", url, requestBody);

            // 构建请求头
            HttpHeaders httpHeaders = buildHttpHeaders(headers);
            HttpEntity<String> entity = new HttpEntity<>(requestBody, httpHeaders);

            // 发送请求
            ResponseEntity<String> response = getRestTemplate().exchange(
                    url, HttpMethod.PUT, entity, String.class);

            String responseBody = response.getBody();
            log.info("PUT请求响应: status={}, body={}", response.getStatusCode(), responseBody);

            return responseBody;
        } catch (RestClientException e) {
            log.error("PUT请求失败: url={}, error={}", url, e.getMessage(), e);
            throw new RuntimeException("PUT请求失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("PUT请求异常: url={}, error={}", url, e.getMessage(), e);
            throw new RuntimeException("PUT请求异常: " + e.getMessage(), e);
        }
    }

    /**
     * 发送PUT请求（无请求头）
     *
     * @param url         请求URL
     * @param requestBody 请求体
     * @return 响应结果
     */
    public static String doPut(String url, String requestBody) {
        return doPut(url, requestBody, null);
    }

    /**
     * 发送DELETE请求
     *
     * @param url     请求URL
     * @param headers 请求头
     * @return 响应结果
     */
    public static String doDelete(String url, Map<String, String> headers) {
        try {
            // 参数校验
            if (!StringUtils.hasText(url)) {
                throw new IllegalArgumentException("请求URL不能为空");
            }

            log.info("发送DELETE请求: url={}", url);

            // 构建请求头
            HttpHeaders httpHeaders = buildHttpHeaders(headers);
            HttpEntity<String> entity = new HttpEntity<>(httpHeaders);

            // 发送请求
            ResponseEntity<String> response = getRestTemplate().exchange(
                    url, HttpMethod.DELETE, entity, String.class);

            String responseBody = response.getBody();
            log.info("DELETE请求响应: status={}, body={}", response.getStatusCode(), responseBody);

            return responseBody;
        } catch (RestClientException e) {
            log.error("DELETE请求失败: url={}, error={}", url, e.getMessage(), e);
            throw new RuntimeException("DELETE请求失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("DELETE请求异常: url={}, error={}", url, e.getMessage(), e);
            throw new RuntimeException("DELETE请求异常: " + e.getMessage(), e);
        }
    }

    /**
     * 发送DELETE请求（无请求头）
     *
     * @param url 请求URL
     * @return 响应结果
     */
    public static String doDelete(String url) {
        return doDelete(url, null);
    }

    /**
     * 构建HTTP请求头
     *
     * @param headers 头部信息
     * @return HttpHeaders
     */
    private static HttpHeaders buildHttpHeaders(Map<String, String> headers) {
        HttpHeaders httpHeaders = new HttpHeaders();

        // 设置默认Content-Type
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);

        // 添加自定义请求头
        if (headers != null && !headers.isEmpty()) {
            headers.forEach(httpHeaders::set);
        }

        return httpHeaders;
    }
} 
