package com.spes.sop.common.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * 对象转Map的工具类
 *
 * <AUTHOR>
 */
@Slf4j
public final class ObjectToMapUtils {

    /**
     * 私有构造函数，防止实例化
     */
    private ObjectToMapUtils() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * 将对象转换为Map（过滤null和空字符串）
     *
     * @param obj 要转换的对象
     * @return 转换后的Map
     * @throws RuntimeException 转换异常
     */
    public static Map<String, Object> toMap(Object obj) {
        return JSONObject.parseObject(JSONUtil.toJsonStr(obj), Map.class);
    }


    /**
     * 过滤Map中的空值
     *
     * @param originalMap 原始Map
     * @return 过滤后的Map
     */
    public static Map<String, Object> filterEmptyValues(Map<String, Object> originalMap) {
        if (originalMap == null || originalMap.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, Object> filteredMap = new HashMap<>();

        for (Map.Entry<String, Object> entry : originalMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (isValidValue(value)) {
                filteredMap.put(key, value);
            } else {
                log.debug("过滤空值字段：{} = {}", key, value);
            }
        }

        log.debug("Map过滤完成，原始大小：{}，过滤后大小：{}", originalMap.size(), filteredMap.size());
        return filteredMap;
    }

    /**
     * 检查值是否有效（非null且非空字符串）
     *
     * @param value 要检查的值
     * @return 是否有效
     */
    private static boolean isValidValue(Object value) {
        if (value == null) {
            return false;
        }

        if (value instanceof String) {
            return StrUtil.isNotBlank((String) value);
        }

        return true;
    }

    /**
     * 将对象转换为Map并添加额外的键值对
     *
     * @param obj              要转换的对象
     * @param additionalParams 额外的参数
     * @return 转换后的Map
     * @throws RuntimeException 转换异常
     */
    public static Map<String, Object> toMapWithAdditional(Object obj, Map<String, Object> additionalParams) {
        Map<String, Object> map = toMap(obj);

        if (additionalParams != null && !additionalParams.isEmpty()) {
            Map<String, Object> filteredAdditional = filterEmptyValues(additionalParams);
            map.putAll(filteredAdditional);
        }

        return map;
    }

    /**
     * 对象转Map的简化方法（仅用于调试）
     *
     * @param obj 要转换的对象
     * @return 包含所有字段的Map（包括null值）
     */
    public static Map<String, Object> toMapForDebug(Object obj) {
        if (obj == null) {
            return new HashMap<>();
        }

        try {
            return BeanUtil.beanToMap(obj);
        } catch (Exception e) {
            log.error("调试转换失败：{}", e.getMessage(), e);
            return new HashMap<>();
        }
    }
} 