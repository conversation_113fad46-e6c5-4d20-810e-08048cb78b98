package com.spes.sop.common.enums;

import cn.hutool.core.util.ObjectUtil;
import lombok.Getter;

/**
 * 审核状态枚举
 */
public enum GoodsStatusEnum {

    CREATED("已创建"),
    APPROVING("审批中"),
    APPROVED("审批通过"),
    WEAVER_SYNC_FAILED("泛微同步失败"),
    KINGDEE_SAVE_FAILED("金蝶保存失败"),
    KINGDEE_SUBMIT_FAILED("金蝶提交失败"),
    KINGDEE_AUDIT_FAILED("金蝶审核失败"),
    SUCCESS("成功");

    @Getter
    private final String desc;

    GoodsStatusEnum(String desc) {
        this.desc = desc;
    }

    public static GoodsStatusEnum getByName(String code) {
        if (ObjectUtil.isNull(code)) {
            return null;
        }
        for (GoodsStatusEnum value : values()) {
            if (value.name().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
