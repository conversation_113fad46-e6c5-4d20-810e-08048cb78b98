package com.spes.sop.common.page;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 分页查询基础类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class BasePager {

    private Pager pager;

    //内部类 pager
    @Data
    public static class Pager {
        private int pageNum;
        private int pageSize;
        private int offset;
        private int limit;

        public Pager(int pageNum, int pageSize) {
            this.pageNum = pageNum;
            this.pageSize = pageSize;
            this.offset = (pageNum - 1) * pageSize;
            this.limit = pageSize;
        }
    }

}
