package com.spes.sop.common.util;

import cn.hutool.crypto.digest.BCrypt;
import com.spes.sop.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * 密码工具类
 * 使用BCrypt算法进行密码加密和验证
 *
 * <AUTHOR>

 */
@Slf4j
public class PasswordUtil {

    /**
     * 加密密码
     *
     * @param rawPassword 明文密码
     * @return 加密后的密码
     */
    public static String encryptPassword(String rawPassword) {
        if (!StringUtils.hasText(rawPassword)) {
            throw new IllegalArgumentException("密码不能为空");
        }

        try {
            return BCrypt.hashpw(rawPassword);
        } catch (Exception e) {
            log.error("密码加密失败: error={}", e.getMessage(), e);
            throw new BusinessException("密码加密失败", e);
        }
    }

    /**
     * 验证密码
     *
     * @param rawPassword     明文密码
     * @param encodedPassword 加密后的密码
     * @return 密码是否匹配
     */
    public static boolean verifyPassword(String rawPassword, String encodedPassword) {
        if (!StringUtils.hasText(rawPassword) || !StringUtils.hasText(encodedPassword)) {
            return false;
        }

        try {
            return BCrypt.checkpw(rawPassword, encodedPassword);
        } catch (Exception e) {
            log.error("密码验证失败: error={}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查密码强度
     * 密码强度要求：至少6位，包含字母和数字
     *
     * @param password 密码
     * @return 是否符合强度要求
     */
    public static boolean isStrongPassword(String password) {
        if (!StringUtils.hasText(password)) {
            return false;
        }

        // 至少6位
        if (password.length() < 6) {
            return false;
        }

        // 包含字母
        boolean hasLetter = password.matches(".*[a-zA-Z].*");
        // 包含数字
        boolean hasDigit = password.matches(".*\\d.*");

        return hasLetter && hasDigit;
    }

    /**
     * 生成随机密码
     *
     * @param length 密码长度（最少6位）
     * @return 随机密码
     */
    public static String generateRandomPassword(int length) {
        if (length < 6) {
            length = 6;
        }

        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder password = new StringBuilder();

        // 确保至少包含一个字母和一个数字
        password.append(chars.charAt((int) (Math.random() * 26))); // 大写字母
        password.append(chars.charAt((int) (Math.random() * 26) + 26)); // 小写字母
        password.append(chars.charAt((int) (Math.random() * 10) + 52)); // 数字

        // 填充剩余长度
        for (int i = 3; i < length; i++) {
            password.append(chars.charAt((int) (Math.random() * chars.length())));
        }

        // 打乱顺序
        return shuffleString(password.toString());
    }

    /**
     * 打乱字符串顺序
     *
     * @param str 原字符串
     * @return 打乱后的字符串
     */
    private static String shuffleString(String str) {
        char[] chars = str.toCharArray();
        for (int i = chars.length - 1; i > 0; i--) {
            int j = (int) (Math.random() * (i + 1));
            char temp = chars[i];
            chars[i] = chars[j];
            chars[j] = temp;
        }
        return new String(chars);
    }
} 
