package com.spes.sop.common.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

public enum UserRoleEnum {
    ADMIN("管理员"),
    USER("普通用户"),
    BUSINESS("业务员"),
    FINANCE("财务"),
    PRODUCT("产品"),
    SUPPLY_CHAIN("供应链");

    @Getter
    private final String description;

    UserRoleEnum(String description) {
        this.description = description;
    }

    public static UserRoleEnum getByName(String name) {
        for (UserRoleEnum value : values()) {
            if (value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 可以见到成本的角色名称列表
     */
    public static List<String> getPricePermissionRole() {
        List<String> result = Lists.newArrayList();
        result.add(FINANCE.name());
        result.add(ADMIN.name());
        result.add(PRODUCT.name());
        return result;
    }
}
