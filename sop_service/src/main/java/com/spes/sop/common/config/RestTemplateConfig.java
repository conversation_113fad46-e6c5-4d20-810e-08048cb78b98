package com.spes.sop.common.config;

import com.spes.sop.common.util.HttpClientUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * RestTemplate配置类
 *
 * <AUTHOR>

 */
@Configuration
public class RestTemplateConfig {

    /**
     * 创建RestTemplate Bean
     *
     * @return RestTemplate实例
     */
    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        // 设置请求工厂
        restTemplate.setRequestFactory(clientHttpRequestFactory());

        // 将RestTemplate实例设置到HttpClientUtil工具类中
        HttpClientUtil.setRestTemplate(restTemplate);
        
        return restTemplate;
    }

    /**
     * 创建HTTP请求工厂
     *
     * @return ClientHttpRequestFactory实例
     */
    @Bean
    public ClientHttpRequestFactory clientHttpRequestFactory() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();

        // 连接超时时间（毫秒）
        factory.setConnectTimeout(30000);

        // 读取超时时间（毫秒）
        factory.setReadTimeout(60000);

        return factory;
    }
} 
