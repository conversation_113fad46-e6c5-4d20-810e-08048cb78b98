package com.spes.sop.goods.mapper;

import com.spes.sop.goods.mapper.model.entity.SpuDO;
import com.spes.sop.goods.mapper.model.query.SpuDOGetQuery;
import com.spes.sop.goods.mapper.model.query.SpuDOListQuery;
import com.spes.sop.goods.mapper.model.req.SpuAddReq;
import com.spes.sop.goods.mapper.model.req.SpuUpdateReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * SPU信息Mapper接口
 *
 * <AUTHOR>

 */
@Mapper
public interface SpuMapper {

    /**
     * 根据条件分页查询SPU列表
     *
     * @param query 查询条件（支持批量条件）
     * @return SPU列表
     */
    List<SpuDO> list(@Param("query") SpuDOListQuery query);

    /**
     * 根据条件查询总数
     *
     * @param query 查询条件（支持批量条件）
     * @return 总数
     */
    Long count(@Param("query") SpuDOListQuery query);

    /**
     * 根据条件查询单个记录（取第一条）
     *
     * @param query 查询条件
     * @return SPU信息
     */
    SpuDO getSpu(@Param("query") SpuDOGetQuery query);

    /**
     * 新增单个SPU
     *
     * @param req SPU信息
     * @return 影响行数
     */
    int insert(@Param("req") SpuAddReq req);

    /**
     * 批量新增SPU
     *
     * @param reqs SPU列表
     * @return 影响行数
     */
    int insertBatch(@Param("spus") List<SpuAddReq> reqs);

    /**
     * 根据ID更新SPU（选择性更新）
     *
     * @param req SPU信息
     * @return 影响行数
     */
    int updateById(@Param("req") SpuUpdateReq req);

    /**
     * 根据编码前缀统计SPU数量
     * 用于生成编码时计算序号
     *
     * @param codePrefix 编码前缀（支持LIKE模糊查询）
     * @return 匹配的SPU数量
     */
    int countByCodePrefix(@Param("codePrefix") String codePrefix);
} 
