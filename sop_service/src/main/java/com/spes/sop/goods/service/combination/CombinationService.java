package com.spes.sop.goods.service.combination;

import com.spes.sop.common.page.PageResult;
import com.spes.sop.goods.service.combination.model.bo.CombinationDetailBO;
import com.spes.sop.goods.service.combination.model.bo.CombinationListBO;
import com.spes.sop.goods.service.combination.model.command.CombinationCreateCommand;
import com.spes.sop.goods.service.combination.model.command.CombinationUpdateCommand;
import com.spes.sop.goods.service.combination.model.query.CombinationBOPageQuery;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 组合品服务接口
 *
 * <AUTHOR>

 */
public interface CombinationService {

    /**
     * 分页查询组合品列表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    PageResult<CombinationListBO> page(CombinationBOPageQuery query);

    /**
     * 查询组合品列表
     */
    List<CombinationListBO> list(CombinationBOPageQuery query);

    /**
     * 查询组合品总数
     */
    Long count(CombinationBOPageQuery query);

    /**
     * 根据ID查询组合品详情
     *
     * @param id 组合品ID
     * @return 组合品详情
     */
    CombinationDetailBO getById(@NotNull Long id);

    /**
     * 根据ID查询组合品基本信息(没有关联的sku信息)
     *
     * @param id 组合品ID
     * @return 组合品基本信息
     */
    CombinationDetailBO getBasicById(@NotNull Long id);

    /**
     * 创建组合品
     *
     * @param command 创建DTO
     * @return 组合品ID
     */
    Long create(@Valid CombinationCreateCommand command);

    /**
     * 更新组合品
     *
     * @param command 更新DTO
     */
    void updateOaStatus(@Valid CombinationUpdateCommand command);

    void updateRelationInfo(@Valid CombinationUpdateCommand command);

    /**
     * 检查组合品名称是否存在
     *
     * @param name      组合品名称
     * @param excludeId 排除的ID（可选）
     * @return 是否存在
     */
    boolean existsByName(@NotNull String name, Long excludeId);

    /**
     * 使关联关系生效
     */
    void enableSku(Long combinationId, Integer version, Long updaterId);
} 
