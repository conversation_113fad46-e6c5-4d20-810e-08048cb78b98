package com.spes.sop.goods.mapper.model.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * SKU单个查询条件
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuDOGetQuery {

    /**
     * SKU ID
     */
    private Long id;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * 条码
     */
    private String barCode;

    /**
     * 备案号
     */
    private String filingNumber;

    /**
     * 是否删除（0-未删除，1-已删除），默认查询未删除
     */
    private Integer deleted = 0;
} 