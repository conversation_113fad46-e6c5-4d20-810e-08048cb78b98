package com.spes.sop.goods.service.sku.model.command;

import com.spes.sop.common.enums.GoodsStatusEnum;
import com.spes.sop.goods.service.sku.model.bo.value.SkuBusinessInfoValueBO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * SKU创建命令
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuCreateCommand {
    /**
     * 货品编码（自动生成）
     */
    private String skuCode;
    /**
     * 下单产品名称
     */
    private String skuName;
    /**
     * 审核状态
     */
    private GoodsStatusEnum oaStatus;
    /**
     * 规格
     */
    private Long specification;
    /**
     * 运营信息
     */
    private SkuBusinessInfoValueBO businessInfo;
    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 更新人ID
     */
    private Long updaterId;

} 