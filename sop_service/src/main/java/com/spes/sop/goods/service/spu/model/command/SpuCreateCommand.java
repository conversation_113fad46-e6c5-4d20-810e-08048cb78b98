package com.spes.sop.goods.service.spu.model.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpuCreateCommand {
    /**
     * SPU ID
     */
    private Long id;

    /**
     * SPU编码
     */
    private String spuCode;

    /**
     * SPU名称
     */
    private String spuName;

    /**
     * 描述
     */
    private String description;

    /**
     * 品牌 id
     */
    private Long brandId;

    /**
     * 一级分类
     */
    private Long firstClassification;

    /**
     * 二级分类
     */
    private Long secondClassification;

    /**
     * 三级分类
     */
    private Long thirdClassification;

    /**
     * 四级类目
     */
    private Long fourthClassification;

    /**
     * 渠道 id
     */
    private Long channelId;

    /**
     * 系列 id
     */
    private Long seriesId;

    /**
     * 分类 id
     */
    private Long categoryId;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 更新人ID
     */
    private Long updaterId;
}
