package com.spes.sop.goods.service.sku.model.bo;

import com.spes.sop.common.enums.GoodsStatusEnum;
import com.spes.sop.goods.service.sku.model.bo.value.SkuBusinessInfoValueBO;
import com.spes.sop.goods.service.sku.model.bo.value.SkuProductInfoValueBO;
import com.spes.sop.goods.service.sku.model.bo.value.SkuSupplyChainInfoValueBO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuListBO {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 货品ID（SPU ID）
     */
    private Long spuId;

    /**
     * 货品编码
     */
    private String skuCode;

    /**
     * 下单产品名称
     */
    private String skuName;

    /**
     * 条码
     */
    private String barCode;

    /**
     * 备案号
     */
    private String filingNumber;

    /**
     * 状态
     */
    private GoodsStatusEnum status;

    /**
     * 审批流程 id
     */
    private Long oaId;
    /**
     * 同步失败原因
     */
    private String syncFail;

    /**
     * 规格
     */
    private Integer specification;

    /**
     * 是否赠品（1-是，0-否）
     */
    private Integer gift;

    /**
     * 是否为包裹卡（1-是，0-否）
     */
    private Integer card;

    /**
     * 运营信息（JSON格式存储）
     */
    private SkuBusinessInfoValueBO businessInfo;

    /**
     * 产品信息（JSON格式存储）
     */
    private SkuProductInfoValueBO productInfo;

    /**
     * 供应链信息（JSON格式存储）
     */
    private SkuSupplyChainInfoValueBO supplyChainInfo;

    /**
     * 公允价值
     */
    private BigDecimal fairPrice;

    /**
     * 底价
     */
    private BigDecimal basePrice;

    /**
     * 成本
     */
    private BigDecimal cost;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 更新人ID
     */
    private Long updaterId;
}
