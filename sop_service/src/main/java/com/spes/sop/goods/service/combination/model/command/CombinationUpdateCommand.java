package com.spes.sop.goods.service.combination.model.command;

import com.spes.sop.common.enums.GoodsStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 组合品更新 command（Service层）
 *
 * <AUTHOR>

 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CombinationUpdateCommand {

    /**
     * 组合品ID
     */
    private Long id;

    /**
     * 组合品名称
     */
    private String name;

    /**
     * 组合品描述
     */
    private String description;

    /**
     * 品牌 id
     */
    private Long brandId;

    /**
     * 一级分类
     */
    private Long firstClassification;

    /**
     * 二级分类
     */
    private Long secondClassification;

    /**
     * 三级分类
     */
    private Long thirdClassification;

    /**
     * 四级类目
     */
    private Integer fourthClassification;

    /**
     * 系列列表（id）
     */
    private List<Long> seriesId;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 渠道ID
     */
    private Long channelId;

    /**
     * 关联的SKU列表
     */
    private List<CombinationSkuRelationUpdateCommand> skuItems;

    /**
     * 状态
     * @see GoodsStatusEnum
     */
    private GoodsStatusEnum status;

    /**
     * 审批流程 id
     */
    private Long oaId;

    /**
     * 修改审核状态
     */
    private GoodsStatusEnum modifiedOaStatus;

    /**
     * 修改审批流程 id
     */
    private Integer modifiedOaId;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 操作人
     */
    private Long operatorId;
} 
