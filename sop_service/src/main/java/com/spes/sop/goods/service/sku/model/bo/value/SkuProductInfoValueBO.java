package com.spes.sop.goods.service.sku.model.bo.value;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuProductInfoValueBO {
    /**
     * 需求到货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date requiredArrivalDate;

    /**
     * 公允价值（标价/零售价）
     */
    private BigDecimal fairPrice;

    /**
     * 底价
     */
    private BigDecimal basePrice;

    /**
     * 最终售价
     */
    private BigDecimal finalPrice;

    /**
     * 附件列表
     */
    private List<String> attachments;

}
