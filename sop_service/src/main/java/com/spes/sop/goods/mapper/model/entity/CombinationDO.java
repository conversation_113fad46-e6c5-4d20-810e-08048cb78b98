package com.spes.sop.goods.mapper.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 组合品实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CombinationDO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 组合品编码
     */
    private String combinationCode;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 品牌 id
     */
    private Long brandId;

    /**
     * 一级分类
     */
    private Long firstClassification;

    /**
     * 二级分类
     */
    private Long secondClassification;

    /**
     * 三级分类
     */
    private Long thirdClassification;

    /**
     * 四级类目
     */
    private Long fourthClassification;

    /**
     * 系列列表（id）
     */
    private Long seriesId;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 渠道 id
     */
    private Long channelId;

    /**
     * 状态
     */
    private String status;

    /**
     * 审批流程 id
     */
    private Long oaId;

    /**
     * 修改审核状态
     */
    private String modifiedOaStatus;

    /**
     * 修改审批流程 id
     */
    private Integer modifiedOaId;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    private Boolean deleted;
} 
