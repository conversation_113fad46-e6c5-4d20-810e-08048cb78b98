package com.spes.sop.goods.service.combination.model.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 组合品创建 command（Service层）
 *
 * <AUTHOR>

 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CombinationCreateCommand {

    /**
     * 组合品名称
     */
    private String name;

    /**
     * 组合品描述
     */
    private String description;
    /**
     * 编码
     */
    private String combinationCode;

    /**
     * 品牌 id
     */
    private Long brandId;

    /**
     * 一级分类
     */
    private Long firstClassification;

    /**
     * 二级分类
     */
    private Long secondClassification;

    /**
     * 三级分类
     */
    private Long thirdClassification;

    /**
     * 四级类目
     */
    private Long fourthClassification;

    /**
     * 系列列表（id）
     */
    private Long seriesId;

    /**
     * 分类ID
     */
    private Long categoryId;
    /**
     * 渠道 id
     */
    private Long channelId;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 关联的SKU列表
     */
    private List<CombinationSkuRelationCreateCommand> skuItems;

    /**
     * 操作人ID
     */
    private Long operatorId;
} 
