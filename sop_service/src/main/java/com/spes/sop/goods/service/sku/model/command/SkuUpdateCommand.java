package com.spes.sop.goods.service.sku.model.command;

import com.spes.sop.common.enums.GoodsStatusEnum;
import com.spes.sop.goods.service.sku.model.bo.value.SkuBusinessInfoValueBO;
import com.spes.sop.goods.service.sku.model.bo.value.SkuProductInfoValueBO;
import com.spes.sop.goods.service.sku.model.bo.value.SkuSupplyChainInfoValueBO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuUpdateCommand {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 货品ID（SPU ID）
     */
    private Long spuId;
    /**
     * 货品编码（自动生成）
     */
    private String skuCode;
    /**
     * 下单产品名称
     */
    private String skuName;
    /**
     * 条码
     */
    private String barCode;
    /**
     * 状态
     */
    private GoodsStatusEnum status;
    /**
     * 审批流程 id
     */
    private Long oaId;
    /**
     * 同步失败原因
     */
    private String syncFail;
    /**
     * 备案号
     */
    private String filingNumber;
    /**
     * 规格
     */
    private Integer specification;

    /**
     * 运营信息
     */
    private SkuBusinessInfoValueBO businessInfo;
    /**
     * 产品信息
     */
    private SkuProductInfoValueBO productInfo;
    /**
     * 供应链信息
     */
    private SkuSupplyChainInfoValueBO supplyChainInfo;
    /**
     * 公允价值
     */
    private BigDecimal fairPrice;
    /**
     * 底价
     */
    private BigDecimal basePrice;
    /**
     * 成本
     */
    private BigDecimal cost;
    /**
     * 是否赠品（1-是，0-否）
     */
    private Integer gift;

    /**
     * 是否为包裹卡（1-是，0-否）
     */
    private Integer card;
    /**
     * 更新人ID
     */
    private Long updaterId;
}
