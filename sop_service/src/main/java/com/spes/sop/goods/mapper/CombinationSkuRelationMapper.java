package com.spes.sop.goods.mapper;

import com.spes.sop.goods.mapper.model.entity.CombinationSkuRelationDO;
import com.spes.sop.goods.mapper.model.query.CombinationSkuRelationDOListQuery;
import com.spes.sop.goods.mapper.model.req.CombinationSkuRelationAddReq;
import com.spes.sop.goods.mapper.model.req.CombinationSkuRelationDeleteReq;
import com.spes.sop.goods.mapper.model.req.CombinationSkuRelationUpdateReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 组合品SKU关联关系Mapper接口
 */
@Mapper
public interface CombinationSkuRelationMapper {

    /**
     * 分页查询组合品SKU关联关系列表
     *
     * @param query 查询条件
     * @return 关联关系列表
     */
    List<CombinationSkuRelationDO> list(@Param("query") CombinationSkuRelationDOListQuery query);

    /**
     * 查询组合品SKU关联关系总数
     *
     * @param query 查询条件
     * @return 总数
     */
    Long count(@Param("query") CombinationSkuRelationDOListQuery query);

    /**
     * 新增组合品SKU关联关系
     *
     * @param req 新增请求
     * @return 影响行数
     */
    int insert(@Param("req") CombinationSkuRelationAddReq req);

    /**
     * 批量新增组合品SKU关联关系
     *
     * @param reqList 新增请求列表
     * @return 影响行数
     */
    int insertBatch(@Param("req") List<CombinationSkuRelationAddReq> reqList);

    /**
     * 更新组合品SKU关联关系
     *
     * @param req 更新请求
     * @return 影响行数
     */
    int updateById(@Param("req") CombinationSkuRelationUpdateReq req);

    /**
     * 逻辑删除组合品SKU关联关系
     *
     * @param req 删除请求
     * @return 影响行数
     */
    int delete(@Param("req") CombinationSkuRelationDeleteReq req);

    /**
     * 批量设置组合品SKU关联关系失效
     *
     * @param combinationId 组合品ID
     * @param updaterId     更新人ID
     * @return 影响行数
     */
    int batchSetIneffective(@Param("combinationId") Long combinationId,
                            @Param("version") Integer version,
                            @Param("updaterId") Long updaterId);

    /**
     * 批量设置组合品SKU关联关系生效
     */
    int batchSetEffective(@Param("combinationId") Long combinationId,
                          @Param("version") Integer version,
                            @Param("updaterId") Long updaterId);
} 
