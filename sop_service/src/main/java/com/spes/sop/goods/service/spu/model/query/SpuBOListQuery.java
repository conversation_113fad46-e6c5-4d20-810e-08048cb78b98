package com.spes.sop.goods.service.spu.model.query;

import com.spes.sop.common.page.BasePager;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * SPU分页查询参数封装对象 - 业务层
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class SpuBOListQuery extends BasePager {
    /**
     * SPU ID列表
     */
    private List<Long> ids;

    /**
     * SPU编码列表
     */
    private List<String> spuCodes;

    /**
     * SPU名称（模糊查询）
     */
    private String spuName;

    /**
     * 品牌ID列表
     */
    private List<Long> brandIds;

    /**
     * 一级分类列表
     */
    private List<Long> firstClassifications;

    /**
     * 二级分类列表
     */
    private List<Long> secondClassifications;

    /**
     * 三级分类列表
     */
    private List<Long> thirdClassifications;

    /**
     * 四级分类列表
     */
    private List<Long> fourthClassifications;

    /**
     * 渠道ID列表
     */
    private List<Long> channelIds;

    /**
     * 系列ID列表
     */
    private List<Long> seriesIds;

    /**
     * 分类ID列表
     */
    private List<Long> categoryIds;
} 
