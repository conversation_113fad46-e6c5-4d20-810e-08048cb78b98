package com.spes.sop.goods.mapper.model.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 组合品SKU关联关系列表查询对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CombinationSkuRelationDOListQuery {

    /**
     * 主键ID
     */
    private List<Long> ids;

    /**
     * 组合品ID
     */
    private List<Long> combinationIds;

    /**
     * 版本
     */
    private List<Integer> versions;

    /**
     * SKU ID
     */
    private List<Long> skuIds;

    /**
     * 是否主品
     */
    private Boolean main;

    /**
     * 是否赠品
     */
    private Boolean gift;

    /**
     * 生效时间开始
     */
    private Date effectiveTimeStart;

    /**
     * 生效时间结束
     */
    private Date effectiveTimeEnd;

    /**
     * 失效时间开始
     */
    private Date expirationTimeStart;

    /**
     * 失效时间结束
     */
    private Date expirationTimeEnd;

    /**
     * 是否生效
     */
    private Boolean effective;

    /**
     * 分页起始位置
     */
    private Integer offset;

    /**
     * 分页大小
     */
    private Integer limit;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序方向
     */
    private String orderDirection;
} 
