package com.spes.sop.goods.mapper.model.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * SPU更新请求对象 - 数据访问层
 *
 * <AUTHOR>

 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpuUpdateReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * SPU ID
     */
    private Long id;

    /**
     * SPU编码
     */
    private String spuCode;

    /**
     * SPU名称
     */
    private String spuName;

    /**
     * 描述
     */
    private String description;

    /**
     * 品牌 id
     */
    private Long brandId;

    /**
     * 一级分类
     */
    private Long firstClassification;

    /**
     * 二级分类
     */
    private Long secondClassification;

    /**
     * 三级分类
     */
    private Long thirdClassification;

    /**
     * 四级类目
     */
    private Long fourthClassification;

    /**
     * 渠道 id
     */
    private Long channelId;

    /**
     * 系列 id
     */
    private Long seriesId;

    /**
     * 分类 id
     */
    private Long categoryId;

    /**
     * 操作人ID
     */
    private Long operatorId;
} 
