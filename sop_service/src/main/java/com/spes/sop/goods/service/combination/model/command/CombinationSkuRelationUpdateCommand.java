package com.spes.sop.goods.service.combination.model.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CombinationSkuRelationUpdateCommand {

    /**
     * 组合品ID
     */
    private Long combinationId;

    /**
     * SKU ID
     */
    private Long skuId;

    /**
     * SKU 数量
     */
    private Integer amount;

    /**
     * 是否主品
     */
    private Boolean main;

    /**
     * 是否赠品
     */
    private Boolean gift;

    /**
     * 生效时间
     */
    private Date effectiveTime;

    /**
     * 失效时间
     */
    private Date expireTime;


}
