package com.spes.sop.goods.service.spu;

import com.spes.sop.goods.service.spu.model.bo.SpuBO;
import com.spes.sop.goods.service.spu.model.command.SpuCreateCommand;
import com.spes.sop.goods.service.spu.model.query.SpuBOListQuery;

import java.util.List;

/**
 * SPU信息服务接口
 *
 * <AUTHOR>
 */
public interface SpuService {

    /**
     * 查询SPU列表
     *
     * @param pageQuery 查询条件
     * @return SPU列表
     */
    List<SpuBO> list(SpuBOListQuery pageQuery);

    /**
     * 查询SPU总数
     *
     * @param pageQuery 查询条件
     * @return 总数
     */
    Long count(SpuBOListQuery pageQuery);

    /**
     * 根据ID查询SPU详情
     *
     * @param id SPU ID
     * @return SPU详情，如果不存在则返回null
     */
    SpuBO getById(Long id);

    /**
     * 根据名称查询 spu
     */
    SpuBO getBySpuName(String spuName);

    /**
     * 创建SPU
     *
     * @param spuBO SPU业务对象
     * @return 创建的SPU ID
     */
    Long create(SpuCreateCommand spuBO);

    /**
     * 更新SPU
     *
     * @param spuBO SPU业务对象
     * @return 是否更新成功
     */
    boolean update(SpuBO spuBO);
} 
