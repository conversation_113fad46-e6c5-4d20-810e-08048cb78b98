package com.spes.sop.goods.mapper;

import com.spes.sop.goods.mapper.model.entity.SkuDO;
import com.spes.sop.goods.mapper.model.query.SkuDOGetQuery;
import com.spes.sop.goods.mapper.model.query.SkuDOListQuery;
import com.spes.sop.goods.mapper.model.req.SkuAddReq;
import com.spes.sop.goods.mapper.model.req.SkuUpdateReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * SKU信息Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface SkuMapper {

    /**
     * 根据条件分页查询SKU列表
     *
     * @param query 查询条件（支持批量条件）
     * @return SKU列表
     */
    List<SkuDO> list(@Param("query") SkuDOListQuery query);

    /**
     * 根据条件查询总数
     *
     * @param query 查询条件（支持批量条件）
     * @return 总数
     */
    Long count(@Param("query") SkuDOListQuery query);

    /**
     * 根据条件查询单个记录（取第一条）
     *
     * @param query 查询条件
     * @return SKU信息
     */
    SkuDO getSku(@Param("query") SkuDOGetQuery query);


    /**
     * 新增单个SKU
     *
     * @param req SKU信息
     * @return 影响行数
     */
    int insert(@Param("req") SkuAddReq req);

    /**
     * 批量新增SKU
     *
     * @param reqs SKU列表
     * @return 影响行数
     */
    int insertBatch(@Param("skus") List<SkuAddReq> reqs);

    /**
     * 根据ID更新SKU（选择性更新）
     *
     * @param req SKU信息
     * @return 影响行数
     */
    int updateById(@Param("req") SkuUpdateReq req);

    /**
     * 根据ID逻辑删除SKU
     *
     * @param id        SKU ID
     * @param updaterId 更新人ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id, @Param("updaterId") Long updaterId);

    /**
     * 根据年月统计SKU数量
     *
     * @param codePrefix 编码前缀
     * @return 该月创建的SKU数量
     */
    int countByYearMonth(@Param("codePrefix") String codePrefix);

    /**
     * 根据ID批量逻辑删除SKU
     *
     * @param ids       SKU ID列表
     * @param updaterId 更新人ID
     * @return 影响行数
     */
    int deleteBatch(@Param("ids") List<Long> ids, @Param("updaterId") Long updaterId);
} 