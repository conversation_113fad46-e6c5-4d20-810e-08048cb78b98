package com.spes.sop.goods.service.spu.convert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.spes.sop.common.util.IdWorker;
import com.spes.sop.goods.mapper.model.entity.SkuDO;
import com.spes.sop.goods.mapper.model.entity.SpuDO;
import com.spes.sop.goods.mapper.model.query.SpuDOGetQuery;
import com.spes.sop.goods.mapper.model.query.SpuDOListQuery;
import com.spes.sop.goods.mapper.model.req.SpuAddReq;
import com.spes.sop.goods.mapper.model.req.SpuUpdateReq;
import com.spes.sop.goods.service.spu.model.bo.SpuBO;
import com.spes.sop.goods.service.spu.model.command.SpuCreateCommand;
import com.spes.sop.goods.service.spu.model.query.SpuBOListQuery;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * SPU业务对象转换器
 *
 * <AUTHOR>
 */
public class SpuBOConvert {

    /**
     * 转换分页查询参数
     */
    public static SpuDOListQuery convert(SpuBOListQuery pageQuery) {
        if (pageQuery == null) {
            return null;
        }
        SpuDOListQuery result = SpuDOListQuery.builder()
                .spuCodes(pageQuery.getSpuCodes())
                .spuName(pageQuery.getSpuName())
                .brandIds(pageQuery.getBrandIds())
                .firstClassifications(pageQuery.getFirstClassifications())
                .secondClassifications(pageQuery.getSecondClassifications())
                .thirdClassifications(pageQuery.getThirdClassifications())
                .fourthClassifications(pageQuery.getFourthClassifications())
                .channelIds(pageQuery.getChannelIds())
                .seriesIds(pageQuery.getSeriesIds())
                .categoryIds(pageQuery.getCategoryIds())
                .ids(pageQuery.getIds())
                .build();
        if (ObjectUtil.isNotNull(pageQuery.getPager())) {
            result.setLimit(pageQuery.getPager().getLimit());
            result.setOffset(pageQuery.getPager().getOffset());
        }
        return result;
    }

    /**
     * 转换单个查询参数
     */
    public static SpuDOGetQuery convertToGetQuery(Long id) {
        if (id == null) {
            return null;
        }
        return SpuDOGetQuery.builder()
                .id(id)
                .build();
    }

    /**
     * 转换业务对象为新增请求
     */
    public static SpuAddReq convertToAddReq(SpuCreateCommand spuBO) {
        if (spuBO == null) {
            return null;
        }
        return SpuAddReq.builder()
                .id(IdWorker.generateId())
                .spuCode(spuBO.getSpuCode())
                .spuName(spuBO.getSpuName())
                .description(spuBO.getDescription())
                .brandId(spuBO.getBrandId())
                .firstClassification(spuBO.getFirstClassification())
                .secondClassification(spuBO.getSecondClassification())
                .thirdClassification(spuBO.getThirdClassification())
                .fourthClassification(spuBO.getFourthClassification())
                .channelId(spuBO.getChannelId())
                .seriesId(spuBO.getSeriesId())
                .categoryId(spuBO.getCategoryId())
                .operatorId(spuBO.getCreatorId())
                .build();
    }

    /**
     * 转换业务对象为更新请求
     */
    public static SpuUpdateReq convertToUpdateReq(SpuBO spuBO) {
        if (spuBO == null) {
            return null;
        }
        return SpuUpdateReq.builder()
                .id(spuBO.getId())
                .spuCode(spuBO.getSpuCode())
                .spuName(spuBO.getSpuName())
                .description(spuBO.getDescription())
                .brandId(spuBO.getBrandId())
                .firstClassification(spuBO.getFirstClassification())
                .secondClassification(spuBO.getSecondClassification())
                .thirdClassification(spuBO.getThirdClassification())
                .fourthClassification(spuBO.getFourthClassification())
                .channelId(spuBO.getChannelId())
                .seriesId(spuBO.getSeriesId())
                .categoryId(spuBO.getCategoryId())
                .operatorId(spuBO.getUpdaterId())
                .build();
    }

    /**
     * 转换SpuDO列表为SpuBO列表
     */
    public static List<SpuBO> convertToBO(List<SpuDO> spuDOList) {
        if (CollUtil.isEmpty(spuDOList)) {
            return Lists.newArrayList();
        }
        return spuDOList.stream()
                .map(SpuBOConvert::convertToBO)
                .collect(Collectors.toList());
    }

    /**
     * 单个SpuDO转SpuBO
     */
    public static SpuBO convertToBO(SpuDO spuDO) {
        if (spuDO == null) {
            return null;
        }
        return SpuBO.builder()
                .id(spuDO.getId())
                .spuCode(spuDO.getSpuCode())
                .spuName(spuDO.getSpuName())
                .description(spuDO.getDescription())
                .brandId(spuDO.getBrandId())
                .firstClassification(spuDO.getFirstClassification())
                .secondClassification(spuDO.getSecondClassification())
                .thirdClassification(spuDO.getThirdClassification())
                .fourthClassification(spuDO.getFourthClassification())
                .channelId(spuDO.getChannelId())
                .seriesId(spuDO.getSeriesId())
                .categoryId(spuDO.getCategoryId())
                .creatorId(spuDO.getCreatorId())
                .updaterId(spuDO.getUpdaterId())
                .createTime(spuDO.getCreateTime())
                .updateTime(spuDO.getUpdateTime())
                .build();
    }

    /**
     * 转换SpuDO列表为SpuBO列表，包含SKU信息
     */
    public static List<SpuBO> convertToBO(List<SpuDO> spuDOList, List<SkuDO> skus) {
        if (CollUtil.isEmpty(spuDOList)) {
            return Lists.newArrayList();
        }
        List<SpuBO> result = spuDOList.stream()
                .map(SpuBOConvert::convertToBO)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(skus)) {
            return result;
        }
        Map<Long, List<Long>> spuSkuList = skus.stream().collect(Collectors.groupingBy(SkuDO::getSpuId,
                Collectors.mapping(SkuDO::getId,
                        Collectors.toList())));
        result.forEach(spuBO -> {
            List<Long> skuIds = spuSkuList.get(spuBO.getSpuCode());
            if (CollUtil.isNotEmpty(skuIds)) {
                spuBO.setSkuIds(skuIds);
            }
        });
        return result;
    }
}
