package com.spes.sop.goods.service.combination.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.spes.sop.common.enums.GoodsStatusEnum;
import com.spes.sop.common.exception.BusinessException;
import com.spes.sop.common.page.PageResult;
import com.spes.sop.common.util.IdWorker;
import com.spes.sop.goods.mapper.CombinationMapper;
import com.spes.sop.goods.mapper.CombinationSkuRelationMapper;
import com.spes.sop.goods.mapper.model.entity.CombinationDO;
import com.spes.sop.goods.mapper.model.entity.CombinationSkuRelationDO;
import com.spes.sop.goods.mapper.model.query.CombinationDOGetQuery;
import com.spes.sop.goods.mapper.model.query.CombinationDOListQuery;
import com.spes.sop.goods.mapper.model.query.CombinationSkuRelationDOListQuery;
import com.spes.sop.goods.mapper.model.req.CombinationAddReq;
import com.spes.sop.goods.mapper.model.req.CombinationSkuRelationAddReq;
import com.spes.sop.goods.mapper.model.req.CombinationUpdateReq;
import com.spes.sop.goods.service.combination.CombinationService;
import com.spes.sop.goods.service.combination.convert.CombinationConvert;
import com.spes.sop.goods.service.combination.model.bo.CombinationDetailBO;
import com.spes.sop.goods.service.combination.model.bo.CombinationListBO;
import com.spes.sop.goods.service.combination.model.command.CombinationCreateCommand;
import com.spes.sop.goods.service.combination.model.command.CombinationSkuRelationCreateCommand;
import com.spes.sop.goods.service.combination.model.command.CombinationSkuRelationUpdateCommand;
import com.spes.sop.goods.service.combination.model.command.CombinationUpdateCommand;
import com.spes.sop.goods.service.combination.model.query.CombinationBOPageQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 组合品服务实现类
 *
 * <AUTHOR>

 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CombinationServiceImpl implements CombinationService {

    private final CombinationMapper combinationMapper;
    private final CombinationSkuRelationMapper combinationSkuRelationMapper;
    private final TransactionTemplate transactionTemplate;

    @Override
    public PageResult<CombinationListBO> page(CombinationBOPageQuery query) {
        CombinationDOListQuery pageQuery = CombinationConvert.toDOPageQuery(query);
        Long totalCount = combinationMapper.count(pageQuery);
        if (totalCount == null || totalCount <= 0L) {
            return PageResult.of(Lists.newArrayList(), 0L, query.getPager().getPageNum(),
                    query.getPager().getPageSize());
        }
        List<CombinationDO> combinations = combinationMapper.list(pageQuery);
        List<Long> combinationIds = combinations.stream().map(CombinationDO::getId).collect(Collectors.toList());
        List<CombinationSkuRelationDO> relations =
                combinationSkuRelationMapper.list(CombinationSkuRelationDOListQuery.builder()
                        .combinationIds(combinationIds)
                        .effective(true)  // 只查询有效的关联关系
                        .build());

        List<CombinationListBO> data = CombinationConvert.toBOList(combinations, relations);
        // 这里先返回一个示例分页结果
        PageResult<CombinationListBO> pageResult = PageResult.of(
                data,
                totalCount,
                query.getPager().getPageNum(),
                query.getPager().getPageSize()
        );

        log.info("Service层：分页查询组合品列表成功，返回{}条记录", pageResult.getRecords().size());
        return pageResult;
    }

    @Override
    public List<CombinationListBO> list(CombinationBOPageQuery query) {
        if (ObjectUtil.isNull(query)) {
            return Lists.newArrayList();
        }
        CombinationDOListQuery pageQuery = CombinationConvert.toDOPageQuery(query);
        List<CombinationDO> combinations = combinationMapper.list(pageQuery);
        List<Long> combinationIds = combinations.stream().map(CombinationDO::getId).collect(Collectors.toList());
        List<CombinationSkuRelationDO> relations =
                combinationSkuRelationMapper.list(CombinationSkuRelationDOListQuery.builder()
                        .combinationIds(combinationIds)
                        .effective(true)  // 只查询有效的关联关系
                        .build());
        return CombinationConvert.toBOList(combinations, relations);

    }

    @Override
    public Long count(CombinationBOPageQuery query) {
        if (ObjectUtil.isNull(query)) {
            return 0L;
        }
        CombinationDOListQuery pageQuery = CombinationConvert.toDOPageQuery(query);
        return combinationMapper.count(pageQuery);
    }

    @Override
    public CombinationDetailBO getById(@NotNull Long id) {
        CombinationDO data = combinationMapper.getCombination(CombinationDOGetQuery.builder().id(id).build());
        if (ObjectUtil.isNull(data)) {
            return null;
        }
        CombinationDetailBO detail = CombinationConvert.toDetailBO(data);
        // 查询所有关联关系（包括生效和失效的）
        List<CombinationSkuRelationDO> relations =
                combinationSkuRelationMapper.list(CombinationSkuRelationDOListQuery.builder()
                        .combinationIds(Collections.singletonList(detail.getId()))
                        // 不设置effective过滤，查询所有关联关系
                        .build());
        if (CollUtil.isNotEmpty(relations)) {
            // 为了兼容现有逻辑，仍然设置skus字段（只包含生效的）
            List<Long> effectiveSkuIds = relations.stream()
                    .filter(relation -> Boolean.TRUE.equals(relation.getEffective()))
                    .map(CombinationSkuRelationDO::getSkuId)
                    .collect(Collectors.toList());
            detail.setSkus(effectiveSkuIds);

            // 设置所有关联关系详情（转换为BO）
            detail.setSkuRelations(CombinationConvert.toSkuRelationBOList(relations));
        }
        return detail;
    }

    @Override
    public CombinationDetailBO getBasicById(Long id) {
        CombinationDO data = combinationMapper.getCombination(CombinationDOGetQuery.builder().id(id).build());
        if (ObjectUtil.isNull(data)) {
            return null;
        }
        return CombinationConvert.toDetailBO(data);
    }

    @Override
    public Long create(CombinationCreateCommand command) {
        Long result = transactionTemplate.execute(status -> {
            // 2. 保存组合品基本信息
            Long combinationId = saveCombination(command);

            // 3. 保存SKU关联关系
            if (CollUtil.isNotEmpty(command.getSkuItems())) {
                saveCombinationSkuRelations(combinationId, command.getSkuItems(), command.getOperatorId(), 1);
            }

            log.info("Service层：创建组合品完成，ID：{}，名称：{}", combinationId, command.getName());
            return combinationId;
        });
        return result;
    }

    @Override
    public void updateOaStatus(CombinationUpdateCommand command) {
        CombinationUpdateReq updateReq = CombinationUpdateReq.builder()
                .id(command.getId())
                .oaId(ObjectUtil.isNotNull(command.getOaId()) ? command.getOaId() : null)
                .status(ObjectUtil.isNotNull(command.getStatus()) ? command.getStatus().name() : null)
                .modifiedOaId(ObjectUtil.isNotNull(command.getModifiedOaId()) ? command.getModifiedOaId() : null)
                .modifiedOaStatus(ObjectUtil.isNotNull(command.getModifiedOaStatus()) ?
                        command.getModifiedOaStatus().name() : null)
                .build();

        combinationMapper.updateById(updateReq);
    }

    @Override
    public void updateRelationInfo(@Valid CombinationUpdateCommand command) {
        // 1. 校验组合品是否存在
        validateCombinationExists(command.getId());
        // 2. 校验数据
        validateUpdateCommand(command);
        transactionTemplate.execute(status -> {

            // 3. 获取当前组合品信息和版本号
            CombinationDO currentCombination = combinationMapper.getCombination(
                    CombinationDOGetQuery.builder().id(command.getId()).build());
            if (currentCombination == null) {
                throw new BusinessException("组合品不存在，ID：" + command.getId());
            }

            // 4. 更新组合品基本信息（递增版本号）
            updateCombinationBasicInfo(command, currentCombination.getVersion());

            // 5. 处理SKU关联关系
            if (CollUtil.isNotEmpty(command.getSkuItems())) {
                // 5.1 新增关联关系（使用新版本号）
                addNewSkuRelations(command.getId(), command.getSkuItems(), command.getOperatorId(),
                        currentCombination.getVersion() + 1);
            }
            log.info("Service层：更新组合品完成，ID：{}", command.getId());
            return null;
        });

    }

    @Override
    public boolean existsByName(@NotNull String name, Long excludeId) {
        CombinationDO combination = combinationMapper.getCombination(CombinationDOGetQuery
                .builder().name(name).build());
        if (ObjectUtil.isNull(combination)) {
            return false;
        }
        if (Objects.equals(combination.getId(), excludeId)) {
            return false;
        }
        return true;
    }

    @Override
    public void enableSku(Long combinationId, Integer version, Long updaterId) {
        combinationSkuRelationMapper.batchSetEffective(combinationId, version, updaterId);
    }

    /**
     * 保存组合品基本信息
     *
     * @param command         创建命令
     * @return 组合品ID
     */
    private Long saveCombination(CombinationCreateCommand command) {
        CombinationAddReq addReq = CombinationAddReq.builder()
                .id(IdWorker.generateId())
                .combinationCode(command.getCombinationCode())
                .name(command.getName())
                .description(command.getDescription())
                .brandId(command.getBrandId())
                .firstClassification(command.getFirstClassification())
                .secondClassification(command.getSecondClassification())
                .thirdClassification(command.getThirdClassification())
                .fourthClassification(command.getFourthClassification())
                .seriesId(command.getSeriesId())
                .categoryId(command.getCategoryId())
                .channelId(command.getChannelId())
                .status(GoodsStatusEnum.CREATED.name())
                .version(1)  // 新创建的组合品版本号为1
                .creatorId(command.getOperatorId())
                .updaterId(command.getOperatorId())
                .build();

        int result = combinationMapper.insert(addReq);
        if (result <= 0) {
            throw new BusinessException("保存组合品基本信息失败");
        }

        // 获取生成的ID（通过useGeneratedKeys自动填充到addReq.id中）
        Long generatedId = addReq.getId();
        if (generatedId == null) {
            // 如果自动生成ID失败，则通过编码查询
            CombinationDO saved = combinationMapper.getCombination(
                    CombinationDOGetQuery.builder().combinationCode(command.getCombinationCode()).build());
            if (saved == null) {
                throw new BusinessException("保存组合品后查询失败");
            }
            generatedId = saved.getId();
        }
        return generatedId;
    }

    /**
     * 保存组合品SKU关联关系
     *
     * @param combinationId 组合品ID
     * @param skuItems      SKU关联项列表
     * @param operatorId    操作人ID
     * @param version       组合品版本号
     */
    private void saveCombinationSkuRelations(Long combinationId,
                                             List<CombinationSkuRelationCreateCommand> skuItems,
                                             Long operatorId,
                                             Integer version) {
        List<CombinationSkuRelationAddReq> addReqs = skuItems.stream()
                .map(item -> CombinationSkuRelationAddReq.builder()
                        .combinationId(combinationId)
                        .combinationVersion(version)  // 设置组合品版本号
                        .skuId(item.getSkuId())
                        .amount(item.getAmount())
                        .main(item.getMain())
                        .gift(item.getGift())
                        .effective(Boolean.FALSE)  // 新增的关联关系默认不生效
                        .creatorId(operatorId)
                        .updaterId(operatorId)
                        .operatorId(operatorId)
                        .build())
                .collect(Collectors.toList());

        int result = combinationSkuRelationMapper.insertBatch(addReqs);
        if (result <= 0) {
            throw new BusinessException("保存组合品SKU关联关系失败");
        }
    }

    /**
     * 校验组合品是否存在
     *
     * @param combinationId 组合品ID
     */
    private void validateCombinationExists(Long combinationId) {
        CombinationDO combination = combinationMapper.getCombination(
                CombinationDOGetQuery.builder().id(combinationId).build());
        if (combination == null) {
            throw new BusinessException("组合品不存在，ID：" + combinationId);
        }
    }

    /**
     * 校验更新组合品命令参数
     *
     * @param updateDTO 更新命令
     */
    private void validateUpdateCommand(CombinationUpdateCommand updateDTO) {
        // 校验名称重复（排除自身）
        if (updateDTO.getName() != null && !updateDTO.getName().trim().isEmpty()) {
            if (existsByName(updateDTO.getName().trim(), updateDTO.getId())) {
                throw new BusinessException("组合品名称已存在：" + updateDTO.getName().trim());
            }
        }
        // 校验SKU列表
        if (CollUtil.isNotEmpty(updateDTO.getSkuItems())) {
            // 校验是否只有一个主品
            long mainCount = updateDTO.getSkuItems().stream()
                    .filter(CombinationSkuRelationUpdateCommand::getMain)
                    .count();
            if (mainCount != 1) {
                throw new BusinessException("组合品只能有一个主品");
            }
        }
    }

    /**
     * 更新组合品基本信息
     *
     * @param updateDTO      更新命令
     * @param currentVersion 当前版本号
     */
    private void updateCombinationBasicInfo(CombinationUpdateCommand updateDTO, Integer currentVersion) {
        CombinationUpdateReq updateReq = CombinationUpdateReq.builder()
                .id(updateDTO.getId())
                .name(updateDTO.getName())
                .description(updateDTO.getDescription())
                .brandId(updateDTO.getBrandId())
                .firstClassification(updateDTO.getFirstClassification())
                .secondClassification(updateDTO.getSecondClassification())
                .thirdClassification(updateDTO.getThirdClassification())
                .fourthClassification(updateDTO.getFourthClassification())
                .seriesId(CollUtil.isNotEmpty(updateDTO.getSeriesId()) ?
                        updateDTO.getSeriesId().get(0) : null)
                .categoryId(updateDTO.getCategoryId())
                .channelId(updateDTO.getChannelId())
                .modifiedOaStatus(GoodsStatusEnum.CREATED.name())
                .version(currentVersion + 1)  // 递增版本号
                .updaterId(updateDTO.getOperatorId())
                .build();

        int result = combinationMapper.updateById(updateReq);
        if (result <= 0) {
            throw new BusinessException("更新组合品基本信息失败，可能记录不存在或已被删除");
        }
    }

    /**
     * 新增SKU关联关系
     *
     * @param combinationId 组合品ID
     * @param skuItems      SKU关联项列表
     * @param operatorId    操作人ID
     * @param version       组合品版本号
     */
    private void addNewSkuRelations(Long combinationId,
                                    List<CombinationSkuRelationUpdateCommand> skuItems,
                                    Long operatorId,
                                    Integer version) {
        List<CombinationSkuRelationAddReq> addReqs = skuItems.stream()
                .map(item -> CombinationSkuRelationAddReq.builder()
                        .combinationId(combinationId)
                        .combinationVersion(version)  // 设置组合品版本号
                        .skuId(item.getSkuId())
                        .amount(item.getAmount())
                        .main(item.getMain())
                        .gift(item.getGift())
                        .effective(Boolean.FALSE)  // 新增的关联关系默认不生效
                        .creatorId(operatorId)
                        .updaterId(operatorId)
                        .operatorId(operatorId)
                        .build())
                .collect(Collectors.toList());
        int result = combinationSkuRelationMapper.insertBatch(addReqs);
        if (result <= 0) {
            throw new BusinessException("新增SKU关联关系失败");
        }

    }
}
