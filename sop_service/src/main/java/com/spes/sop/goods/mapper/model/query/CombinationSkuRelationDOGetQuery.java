package com.spes.sop.goods.mapper.model.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 组合品SKU关联关系单个查询对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CombinationSkuRelationDOGetQuery {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 组合品ID
     */
    private Long combinationId;

    /**
     * SKU ID
     */
    private Long skuId;

    /**
     * 是否主品
     */
    private Boolean main;

    /**
     * 是否赠品
     */
    private Boolean gift;
} 
