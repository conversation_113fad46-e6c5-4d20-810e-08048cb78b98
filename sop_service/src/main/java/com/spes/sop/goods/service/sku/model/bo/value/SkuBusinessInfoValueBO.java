package com.spes.sop.goods.service.sku.model.bo.value;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 产品立项申请VO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuBusinessInfoValueBO {

    /**
     * 立项申请时间
     */
    private Date applicationDate;

    /**
     * 产品上线时间
     */
    private Date launchDate;

    /**
     * 是否渠道特供品（true-是，false-否）
     */
    private Boolean channelExclusive;

    /**
     * 产品用途
     */
    private String productPurpose;

    /**
     * 预估售价
     */
    private BigDecimal estimatedPrice;

    /**
     * 首批需求数量
     */
    private Integer firstBatchQuantity;

    /**
     * 工厂起订量
     */
    private Integer factoryMinOrderQuantity;

    /**
     * 最少起订量
     */
    private Integer minOrderQuantity;

    /**
     * 最少起订原因
     */
    private String minOrderReason;

    /**
     * 效期要求
     */
    private String shelfLifeRequirement;

    /**
     * 是否包销（true-是，false-否）
     */
    private Boolean exclusiveSale;

    /**
     * 销售负责人工号
     */
    private String salesResponsibleEmployeeId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 附件列表
     */
    private List<String> attachments;


} 
