package com.spes.sop.goods.service.combination.model.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 组合品SKU关联关系BO（Service层）
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CombinationSkuRelationBO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 组合品ID
     */
    private Long combinationId;

    /**
     * 组合品版本号
     */
    private Integer combinationVersion;

    /**
     * SKU ID
     */
    private Long skuId;

    /**
     * SKU 数量
     */
    private Integer amount;

    /**
     * 是否主品
     */
    private Boolean main;

    /**
     * 是否赠品
     */
    private Boolean gift;

    /**
     * 生效时间
     */
    private Date effectiveTime;

    /**
     * 失效时间
     */
    private Date expirationTime;

    /**
     * 是否生效
     */
    private Boolean effective;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人ID
     */
    private Long updaterId;
} 