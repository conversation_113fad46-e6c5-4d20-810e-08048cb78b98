package com.spes.sop.goods.mapper.model.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * SKU列表查询条件
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuDOListQuery {

    /**
     * SKU ID列表
     */
    private List<Long> ids;
    /**
     * SPU ID列表
     */
    private List<Long> spuIds;
    /**
     * SKU编码列表
     */
    private List<String> skuCodes;

    /**
     * SKU名称（模糊查询）
     */
    private String skuNameLike;

    /**
     * 条码
     */
    private List<String> barCodes;

    /**
     * 备案号
     */
    private List<String> filingNumbers;
    /**
     * 审核状态列表
     */
    private List<String> statuses;

    /**
     * 渠道ID列表
     */
    private List<Long> channelIds;
    /**
     * 规格列表
     */
    private List<Long> specifications;
    /**
     * 分类ID列表
     */
    private List<Long> categoryIds;
    /**
     * 系列列表
     */
    private Long series;
    /**
     * 是否赠品
     */
    private Boolean gift;

    /**
     * 是否为包裹卡
     */
    private Boolean card;

    /**
     * 分页偏移量
     */
    private Integer offset;

    /**
     * 分页大小
     */
    private Integer limit;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序方向（ASC/DESC）
     */
    private String orderDirection;
} 