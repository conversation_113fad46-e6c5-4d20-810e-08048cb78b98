package com.spes.sop.goods.mapper.model.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 组合品单个查询对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CombinationDOGetQuery {

    /**
     * 主键
     */
    private Long id;

    /**
     * 组合品编码
     */
    private String combinationCode;

    /**
     * 名称
     */
    private String name;

} 
