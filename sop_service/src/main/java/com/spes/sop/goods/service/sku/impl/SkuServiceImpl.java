package com.spes.sop.goods.service.sku.impl;

import com.spes.sop.common.exception.BusinessException;
import com.spes.sop.common.util.IdWorker;
import com.spes.sop.goods.mapper.SkuMapper;
import com.spes.sop.goods.mapper.model.entity.SkuDO;
import com.spes.sop.goods.mapper.model.query.SkuDOGetQuery;
import com.spes.sop.goods.mapper.model.query.SkuDOListQuery;
import com.spes.sop.goods.mapper.model.req.SkuAddReq;
import com.spes.sop.goods.mapper.model.req.SkuUpdateReq;
import com.spes.sop.goods.service.sku.SkuService;
import com.spes.sop.goods.service.sku.convert.SkuBOConvert;
import com.spes.sop.goods.service.sku.model.bo.SkuDetailBO;
import com.spes.sop.goods.service.sku.model.bo.SkuListBO;
import com.spes.sop.goods.service.sku.model.command.SkuCreateCommand;
import com.spes.sop.goods.service.sku.model.command.SkuUpdateCommand;
import com.spes.sop.goods.service.sku.model.query.SkuBOListQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * SKU服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SkuServiceImpl implements SkuService {

    private final SkuMapper skuMapper;

    @Override
    public List<SkuListBO> list(SkuBOListQuery query) {
        SkuDOListQuery listQuery = SkuBOConvert.convert(query);
        List<SkuDO> skus = skuMapper.list(listQuery);
        return SkuBOConvert.convert(skus);
    }

    @Override
    public Long count(SkuBOListQuery query) {
        SkuDOListQuery listQuery = SkuBOConvert.convert(query);
        return skuMapper.count(listQuery);
    }

    @Override
    public SkuDetailBO getById(@NotNull Long id) {
        Objects.requireNonNull(id, "SKU ID不能为空");

        log.debug("Service层：查询SKU详情，ID：{}", id);

        SkuDOGetQuery getQuery = SkuDOGetQuery.builder().id(id).build();
        SkuDO skuDO = skuMapper.getSku(getQuery);

        if (skuDO == null) {
            log.debug("SKU不存在，ID：{}", id);
            return null;
        }

        return SkuBOConvert.convertToDetailBO(skuDO);
    }

    @Override
    public Long create(@NotNull SkuCreateCommand command) {
        Objects.requireNonNull(command, "SKU新增请求不能为空");
        log.info("Service层：创建SKU，名称：{}", command.getSkuName());
        long id = IdWorker.generateId();
        SkuAddReq skuAddReq = SkuBOConvert.convertCommandToAddReq(command, id);
        int result = skuMapper.insert(skuAddReq);
        if (result > 0) {
            log.info("创建SKU成功，名称：{}", command.getSkuName());
            return id;
        } else {
            throw new BusinessException("创建SKU失败");
        }
    }

    @Override
    public List<Long> batchCreate(@NotNull List<SkuCreateCommand> commands) {
        Objects.requireNonNull(commands, "SKU批量新增请求不能为空");
        if (commands.isEmpty()) {
            return new ArrayList<>();
        }
        
        log.info("Service层：批量创建SKU，数量：{}", commands.size());
        
        List<Long> ids = new ArrayList<>();
        List<SkuAddReq> addReqs = new ArrayList<>();
        
        // 生成ID和转换请求对象
        for (SkuCreateCommand command : commands) {
            long id = IdWorker.generateId();
            ids.add(id);
            SkuAddReq skuAddReq = SkuBOConvert.convertCommandToAddReq(command, id);
            addReqs.add(skuAddReq);
        }
        
        // 批量插入
        int result = skuMapper.insertBatch(addReqs);
        if (result == commands.size()) {
            log.info("批量创建SKU成功，数量：{}", commands.size());
            return ids;
        } else {
            throw new BusinessException("批量创建SKU失败，期望插入：" + commands.size() + "，实际插入：" + result);
        }
    }

    @Override
    public boolean update(@NotNull SkuUpdateCommand command) {
        log.info("Service层：更新SKU，ID：{}，名称：{}", command.getId(), command.getSkuName());

        SkuUpdateReq updateReq = SkuBOConvert.convertCommandToUpdateReq(command);

        int result = skuMapper.updateById(updateReq);
        boolean success = result > 0;

        if (success) {
            log.info("更新SKU成功，ID：{}", command.getId());
        } else {
            log.warn("更新SKU失败，ID：{}，可能记录不存在", command.getId());
        }

        return success;
    }

    @Override
    public boolean delete(@NotNull Long id, @NotNull Long updaterId) {
        Objects.requireNonNull(id, "SKU ID不能为空");
        Objects.requireNonNull(updaterId, "更新人ID不能为空");
        log.info("Service层：删除SKU，ID：{}，操作人：{}", id, updaterId);

        int result = skuMapper.deleteById(id, updaterId);
        boolean success = result > 0;

        if (success) {
            log.info("删除SKU成功，ID：{}", id);
        } else {
            log.warn("删除SKU失败，ID：{}，可能记录不存在", id);
        }

        return success;
    }
}
