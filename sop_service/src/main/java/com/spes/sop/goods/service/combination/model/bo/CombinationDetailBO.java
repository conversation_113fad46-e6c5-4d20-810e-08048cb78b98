package com.spes.sop.goods.service.combination.model.bo;

import com.spes.sop.common.enums.GoodsStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 组合品详情BO（Service层）
 *
 * <AUTHOR>

 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CombinationDetailBO {

    /**
     * 组合品ID
     */
    private Long id;

    /**
     * 组合品名称
     */
    private String name;

    /**
     * 组合编码
     */
    private String combinationCode;

    /**
     * 组合品描述
     */
    private String description;

    /**
     * 品牌 id
     */
    private Long brandId;

    /**
     * 状态
     */
    private GoodsStatusEnum status;

    /**
     * 审批流程 id
     */
    private Long oaId;

    /**
     * 修改审核状态
     */
    private String modifiedOaStatus;

    /**
     * 修改审批流程 id
     */
    private Integer modifiedOaId;

    /**
     * 版本号
     */
    private Integer version;
    /**
     * 一级分类
     */
    private Long firstClassification;

    /**
     * 二级分类
     */
    private Long secondClassification;

    /**
     * 三级分类
     */
    private Long thirdClassification;

    /**
     * 四级类目
     */
    private Long fourthClassification;

    /**
     * 系列列表（id）
     */
    private Long seriesId;
    /**
     * 渠道 id
     */
    private Long channelId;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 关联的SKU列表（仅生效的SKU ID）
     */
    private List<Long> skus;

    /**
     * 所有SKU关联关系详情（包括生效和失效的）
     */
    private List<CombinationSkuRelationBO> skuRelations;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private Long creatorId;
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private Long updaterId;

} 
