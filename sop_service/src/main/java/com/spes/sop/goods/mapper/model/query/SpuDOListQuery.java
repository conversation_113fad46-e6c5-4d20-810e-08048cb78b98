package com.spes.sop.goods.mapper.model.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * SPU列表查询参数封装对象 - 数据访问层
 *
 * <AUTHOR>

 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpuDOListQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * SPU ID列表
     */
    private List<Long> ids;

    /**
     * SPU编码列表
     */
    private List<String> spuCodes;

    /**
     * SPU名称（模糊查询）
     */
    private String spuName;

    /**
     * 品牌ID列表
     */
    private List<Long> brandIds;

    /**
     * 一级分类列表
     */
    private List<Long> firstClassifications;

    /**
     * 二级分类列表
     */
    private List<Long> secondClassifications;

    /**
     * 三级分类列表
     */
    private List<Long> thirdClassifications;

    /**
     * 四级分类列表
     */
    private List<Long> fourthClassifications;

    /**
     * 渠道ID列表
     */
    private List<Long> channelIds;

    /**
     * 系列ID列表
     */
    private List<Long> seriesIds;

    /**
     * 分类ID列表
     */
    private List<Long> categoryIds;

    /**
     * 排序字段
     */
    private String orderBy = "create_time";

    /**
     * 排序方向：ASC/DESC
     */
    private String orderDirection = "DESC";

    /**
     * 分页参数
     */
    private Integer offset;
    private Integer limit;
} 
