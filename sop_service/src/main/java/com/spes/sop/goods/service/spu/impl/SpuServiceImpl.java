package com.spes.sop.goods.service.spu.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.spes.sop.common.exception.BusinessException;
import com.spes.sop.goods.mapper.SkuMapper;
import com.spes.sop.goods.mapper.SpuMapper;
import com.spes.sop.goods.mapper.model.entity.SkuDO;
import com.spes.sop.goods.mapper.model.entity.SpuDO;
import com.spes.sop.goods.mapper.model.query.SkuDOListQuery;
import com.spes.sop.goods.mapper.model.query.SpuDOGetQuery;
import com.spes.sop.goods.mapper.model.query.SpuDOListQuery;
import com.spes.sop.goods.mapper.model.req.SpuAddReq;
import com.spes.sop.goods.mapper.model.req.SpuUpdateReq;
import com.spes.sop.goods.service.spu.SpuService;
import com.spes.sop.goods.service.spu.convert.SpuBOConvert;
import com.spes.sop.goods.service.spu.model.bo.SpuBO;
import com.spes.sop.goods.service.spu.model.command.SpuCreateCommand;
import com.spes.sop.goods.service.spu.model.query.SpuBOListQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * SPU信息服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpuServiceImpl implements SpuService {

    private final SpuMapper spuMapper;
    private final SkuMapper skuMapper;

    @Override
    public List<SpuBO> list(SpuBOListQuery pageQuery) {
        if (ObjectUtil.isNull(pageQuery)) {
            return Lists.newArrayList();
        }
        // 转换为数据层查询对象
        SpuDOListQuery doQuery = SpuBOConvert.convert(pageQuery);
        List<SpuDO> spuDOList = spuMapper.list(doQuery);
        if (CollUtil.isEmpty(spuDOList)) {
            return Lists.newArrayList();
        }
        List<Long> spuIds = spuDOList.stream().map(SpuDO::getId).collect(Collectors.toList());
        List<SkuDO> skus = skuMapper.list(SkuDOListQuery.builder()
                .spuIds(spuIds)
                .build());
        return SpuBOConvert.convertToBO(spuDOList, skus);
    }

    @Override
    public Long count(SpuBOListQuery pageQuery) {
        // 转换为数据层查询对象
        SpuDOListQuery doQuery = SpuBOConvert.convert(pageQuery);
        Long count = spuMapper.count(doQuery);

        Long result = count != null ? count : 0L;
        return result;
    }

    @Override
    public SpuBO getById(Long id) {
        // 构建查询条件
        SpuDOGetQuery query = SpuBOConvert.convertToGetQuery(id);
        SpuDO spuDO = spuMapper.getSpu(query);

        if (spuDO == null) {
            log.debug("未找到SPU信息，ID：{}", id);
            return null;
        }

        // 查询关联的SKU
        List<SkuDO> skus = skuMapper.list(SkuDOListQuery.builder()
                .spuIds(Lists.newArrayList(id))
                .build());

        // 转换为业务对象
        SpuBO spuBO = SpuBOConvert.convertToBO(spuDO);
        if (CollUtil.isNotEmpty(skus)) {
            List<Long> skuIds = skus.stream()
                    .map(SkuDO::getId)
                    .collect(Collectors.toList());
            spuBO.setSkuIds(skuIds);
        }
        return spuBO;

    }

    @Override
    public SpuBO getBySpuName(String spuName) {
        // 构建查询条件
        SpuDOGetQuery query = SpuDOGetQuery.builder()
                .spuName(spuName)
                .build();
        SpuDO spuDO = spuMapper.getSpu(query);

        if (spuDO == null) {
            log.debug("未找到SPU信息，名称：{}", spuName);
            return null;
        }
        // 转换为业务对象
        return SpuBOConvert.convertToBO(spuDO);
    }

    @Override
    public Long create(SpuCreateCommand spuBO) {
        // 转换为数据层请求对象
        SpuAddReq addReq = SpuBOConvert.convertToAddReq(spuBO);
        if (addReq == null) {
            throw new IllegalArgumentException("转换SPU创建请求失败");
        }

        // 执行创建
        int result = spuMapper.insert(addReq);
        if (result <= 0) {
            throw new BusinessException("创建SPU失败，影响行数：" + result);
        }

        // 获取生成的ID
        Long spuId = addReq.getId();
        if (spuId == null) {
            throw new BusinessException("创建SPU失败，未获取到生成的ID");
        }
        return spuId;
    }

    @Override
    public boolean update(SpuBO spuBO) {
        // 检查SPU是否存在
        SpuBO existingSpu = getById(spuBO.getId());
        if (existingSpu == null) {
            throw new IllegalArgumentException("SPU不存在，ID：" + spuBO.getId());
        }
        // 转换为数据层请求对象
        SpuUpdateReq updateReq = SpuBOConvert.convertToUpdateReq(spuBO);
        if (updateReq == null) {
            throw new IllegalArgumentException("转换SPU更新请求失败");
        }
        // 执行更新
        int result = spuMapper.updateById(updateReq);
        boolean success = result > 0;
        return success;
    }
}
