package com.spes.sop.goods.service.sku.model.query;

import com.spes.sop.common.enums.GoodsStatusEnum;
import com.spes.sop.common.page.BasePager;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class SkuBOListQuery extends BasePager {
    /**
     * SKU ID列表
     */
    private List<Long> ids;
    /**
     * SPU ID列表
     */
    private List<Long> spuIds;
    /**
     * SKU编码列表
     */
    private List<String> skuCodes;

    /**
     * SKU名称（模糊查询）
     */
    private String skuNameSearch;

    /**
     * 条码
     */
    private List<String> barCodes;

    /**
     * 备案号
     */
    private List<String> filingNumbers;
    /**
     * 审核状态列表
     */
    private List<GoodsStatusEnum> oaStatuses;

    /**
     * 渠道ID列表
     */
    private List<Long> channelIds;
    /**
     * 规格列表
     */
    private List<Long> specifications;
    /**
     * 分类id
     */
    private List<Long> categoryIds;
    /**
     * 系列列表
     */
    private Long series;
    /**
     * 是否赠品
     */
    private Boolean gift;

    /**
     * 是否为包裹卡
     */
    private Boolean card;
}
