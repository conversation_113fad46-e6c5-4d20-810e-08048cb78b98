package com.spes.sop.goods.service.combination.convert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.spes.sop.common.enums.GoodsStatusEnum;
import com.spes.sop.goods.mapper.model.entity.CombinationDO;
import com.spes.sop.goods.mapper.model.entity.CombinationSkuRelationDO;
import com.spes.sop.goods.mapper.model.query.CombinationDOListQuery;
import com.spes.sop.goods.service.combination.model.bo.CombinationDetailBO;
import com.spes.sop.goods.service.combination.model.bo.CombinationListBO;
import com.spes.sop.goods.service.combination.model.bo.CombinationSkuRelationBO;
import com.spes.sop.goods.service.combination.model.query.CombinationBOPageQuery;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public interface CombinationConvert {
    static CombinationDOListQuery toDOPageQuery(CombinationBOPageQuery query) {
        CombinationDOListQuery result = CombinationDOListQuery.builder()
                .nameSearch(query.getCombinationNameSearch())
                .combinationCodes(ObjectUtil.isNull(query.getCombinationCode()) ?
                        null : Collections.singletonList(query.getCombinationCode()))
                .firstClassifications(query.getFirstClassification())
                .secondClassifications(query.getSecondClassification())
                .thirdClassifications(query.getThirdClassification())
                .seriesIds(query.getSeriesIds())
                .categoryIds(query.getCategoryIds())
                .statuses(CollUtil.isEmpty(query.getStatuses()) ? null :
                        query.getStatuses().stream().map(GoodsStatusEnum::name).collect(Collectors.toList()))
                .gift(query.getGift())
                .packageCard(query.getPackageCard())
                .build();
        if (ObjectUtil.isNotNull(query.getPager())) {
            result.setOffset(query.getPager().getOffset());
            result.setLimit(query.getPager().getLimit());
        }
        return result;
    }

    static List<CombinationListBO> toBOList(List<CombinationDO> combinations,
                                            List<CombinationSkuRelationDO> relations) {
        List<CombinationListBO> result = toBOList(combinations);
        if (CollUtil.isEmpty(relations)) {
            return result;
        }
        Map<Long, List<Long>> relationMap = relations.stream()
                .collect(Collectors.groupingBy(CombinationSkuRelationDO::getCombinationId,
                        Collectors.mapping(CombinationSkuRelationDO::getSkuId, Collectors.toList())));
        result.forEach(combination -> {
            List<Long> skuIds = relationMap.get(combination.getId());
            if (CollUtil.isNotEmpty(skuIds)) {
                combination.setSkuIds(skuIds);
            }
        });
        return result;
    }

    static List<CombinationListBO> toBOList(List<CombinationDO> combinations) {
        if (ObjectUtil.isEmpty(combinations)) {
            return Collections.emptyList();
        }
        return combinations.stream().map(CombinationConvert::toBO).collect(Collectors.toList());
    }

    static CombinationListBO toBO(CombinationDO combination) {
        return CombinationListBO.builder()
                .id(combination.getId())
                .name(combination.getName())
                .combinationCode(combination.getCombinationCode())
                .description(combination.getDescription())
                .brandId(combination.getBrandId())
                .status(GoodsStatusEnum.getByName(combination.getStatus()))
                .oaId(combination.getOaId())
                .modifiedOaStatus(combination.getModifiedOaStatus())
                .modifiedOaId(combination.getModifiedOaId())
                .version(combination.getVersion())
                .firstClassification(combination.getFirstClassification())
                .secondClassification(combination.getSecondClassification())
                .thirdClassification(combination.getThirdClassification())
                .fourthClassification(combination.getFourthClassification())
                .seriesId(combination.getSeriesId())
                .channelId(combination.getChannelId())
                .categoryId(combination.getCategoryId())
                .createTime(combination.getCreateTime())
                .creatorId(combination.getCreatorId())
                .updateTime(combination.getUpdateTime())
                .updaterId(combination.getUpdaterId())
                .build();
    }

    static CombinationDetailBO toDetailBO(CombinationDO combination) {
        return CombinationDetailBO.builder()
                .id(combination.getId())
                .name(combination.getName())
                .combinationCode(combination.getCombinationCode())
                .description(combination.getDescription())
                .brandId(combination.getBrandId())
                .status(GoodsStatusEnum.getByName(combination.getStatus()))
                .oaId(combination.getOaId())
                .modifiedOaStatus(combination.getModifiedOaStatus())
                .modifiedOaId(combination.getModifiedOaId())
                .version(combination.getVersion())
                .firstClassification(combination.getFirstClassification())
                .secondClassification(combination.getSecondClassification())
                .thirdClassification(combination.getThirdClassification())
                .fourthClassification(combination.getFourthClassification())
                .seriesId(combination.getSeriesId())
                .channelId(combination.getChannelId())
                .categoryId(combination.getCategoryId())
                .createTime(combination.getCreateTime())
                .creatorId(combination.getCreatorId())
                .updateTime(combination.getUpdateTime())
                .updaterId(combination.getUpdaterId())
                .build();
    }

    /**
     * 转换SKU关联关系DO为BO
     *
     * @param relationDO SKU关联关系DO
     * @return SKU关联关系BO
     */
    static CombinationSkuRelationBO toSkuRelationBO(CombinationSkuRelationDO relationDO) {
        if (relationDO == null) {
            return null;
        }
        return CombinationSkuRelationBO.builder()
                .id(relationDO.getId())
                .combinationId(relationDO.getCombinationId())
                .combinationVersion(relationDO.getCombinationVersion())
                .skuId(relationDO.getSkuId())
                .amount(relationDO.getAmount())
                .main(relationDO.getMain())
                .gift(relationDO.getGift())
                .effectiveTime(relationDO.getEffectiveTime())
                .expirationTime(relationDO.getExpirationTime())
                .effective(relationDO.getEffective())
                .createTime(relationDO.getCreateTime())
                .creatorId(relationDO.getCreatorId())
                .updateTime(relationDO.getUpdateTime())
                .updaterId(relationDO.getUpdaterId())
                .build();
    }

    /**
     * 批量转换SKU关联关系DO为BO
     *
     * @param relationDOs SKU关联关系DO列表
     * @return SKU关联关系BO列表
     */
    static List<CombinationSkuRelationBO> toSkuRelationBOList(List<CombinationSkuRelationDO> relationDOs) {
        if (CollUtil.isEmpty(relationDOs)) {
            return Collections.emptyList();
        }
        return relationDOs.stream()
                .map(CombinationConvert::toSkuRelationBO)
                .collect(Collectors.toList());
    }
}
