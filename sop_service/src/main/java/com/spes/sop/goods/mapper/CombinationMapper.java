package com.spes.sop.goods.mapper;

import com.spes.sop.goods.mapper.model.entity.CombinationDO;
import com.spes.sop.goods.mapper.model.query.CombinationDOGetQuery;
import com.spes.sop.goods.mapper.model.query.CombinationDOListQuery;
import com.spes.sop.goods.mapper.model.req.CombinationAddReq;
import com.spes.sop.goods.mapper.model.req.CombinationDeleteReq;
import com.spes.sop.goods.mapper.model.req.CombinationUpdateReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 组合品Mapper接口
 */
@Mapper
public interface CombinationMapper {

    /**
     * 分页查询组合品列表
     *
     * @param query 查询条件
     * @return 组合品列表
     */
    List<CombinationDO> list(@Param("query") CombinationDOListQuery query);

    /**
     * 查询组合品总数
     *
     * @param query 查询条件
     * @return 总数
     */
    Long count(@Param("query") CombinationDOListQuery query);

    /**
     * 查询单个组合品
     *
     * @param query 查询条件
     * @return 组合品信息
     */
    CombinationDO getCombination(@Param("query") CombinationDOGetQuery query);

    /**
     * 新增组合品
     *
     * @param req 新增请求
     * @return 影响行数
     */
    int insert(@Param("req") CombinationAddReq req);

    /**
     * 批量新增组合品
     *
     * @param reqList 新增请求列表
     * @return 影响行数
     */
    int insertBatch(@Param("req") List<CombinationAddReq> reqList);

    /**
     * 更新组合品
     *
     * @param req 更新请求
     * @return 影响行数
     */
    int updateById(@Param("req") CombinationUpdateReq req);

    /**
     * 逻辑删除组合品
     *
     * @param req 删除请求
     * @return 影响行数
     */
    int delete(@Param("req") CombinationDeleteReq req);

    /**
     * 根据编码前缀统计组合品数量
     * 用于生成编码时计算序号
     *
     * @param codePrefix 编码前缀（支持LIKE模糊查询）
     * @return 匹配的组合品数量
     */
    int countByCodePrefix(@Param("codePrefix") String codePrefix);
} 
