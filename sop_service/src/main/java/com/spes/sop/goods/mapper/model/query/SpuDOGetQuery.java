package com.spes.sop.goods.mapper.model.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * SPU单个查询参数封装对象 - 数据访问层
 *
 * <AUTHOR>

 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpuDOGetQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * SPU ID
     */
    private Long id;

    /**
     * SPU编码
     */
    private String spuCode;

    /**
     * SPU名称
     */
    private String spuName;
} 
