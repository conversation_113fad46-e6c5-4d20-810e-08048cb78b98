package com.spes.sop.goods.mapper.model.query;

import com.spes.sop.common.enums.GoodsStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 组合品列表查询对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CombinationDOListQuery {

    /**
     * 主键
     */
    private List<Long> ids;

    /**
     * 组合品编码
     */
    private List<String> combinationCodes;
    /**
     * oa_ids
     */
    private List<String> oaIds;

    /**
     * 名称
     */
    private String nameSearch;

    /**
     * 一级分类
     */
    private List<Long> firstClassifications;

    /**
     * 二级分类
     */
    private List<Long> secondClassifications;

    /**
     * 三级分类
     */
    private List<Long> thirdClassifications;
    /**
     * 四级分类
     */
    private List<Long> fourthClassifications;

    /**
     * 系列ID
     */
    private List<Long> seriesIds;

    /**
     * 分类ID
     */
    private List<Long> categoryIds;

    /**
     * 状态
     *
     * @see GoodsStatusEnum#name()
     */
    private List<String> statuses;

    /**
     * 修改审核状态
     */
    private List<String> modifiedOaStatuses;

    /**
     * 是否赠品
     */
    private Boolean gift;
    /**
     * 是否为包裹卡
     */
    private Boolean packageCard;

    /**
     * 分页起始位置
     */
    private Integer offset;

    /**
     * 分页大小
     */
    private Integer limit;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序方向
     */
    private String orderDirection;
} 
