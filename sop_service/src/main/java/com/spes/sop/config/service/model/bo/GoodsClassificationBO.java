package com.spes.sop.config.service.model.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 商品分类业务对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsClassificationBO {
    /**
     * 分类ID
     */
    private Long id;
    /**
     * 分类名称
     */
    private String name;
    /**
     * 级别
     */
    private Integer level;
    /**
     * 创建人
     */
    private Long creatorId;
    /**
     * 创建时间
     */
    private Date createTime;
} 