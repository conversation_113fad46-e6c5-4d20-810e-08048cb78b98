package com.spes.sop.config.service;

import com.spes.sop.common.page.PageResult;
import com.spes.sop.config.service.model.bo.GoodsChannelBO;
import com.spes.sop.config.service.model.command.GoodsChannelCreateCommand;
import com.spes.sop.config.service.model.command.GoodsChannelDeleteCommand;
import com.spes.sop.config.service.model.command.GoodsChannelUpdateCommand;
import com.spes.sop.config.service.model.query.ChannelBOPagerQuery;

import java.util.List;

/**
 * 商品渠道服务接口
 */
public interface GoodsChannelService {

    /**
     * 详情
     */
    GoodsChannelBO getById(Long id);

    GoodsChannelBO getByName(String name);

    /**
     * 列表
     */
    List<GoodsChannelBO> list(ChannelBOPagerQuery query);
    /**
     * 总数
     */
    Long count(ChannelBOPagerQuery query);

    /**
     * 分页
     */
    PageResult<GoodsChannelBO> pager(ChannelBOPagerQuery query);

    /**
     * 创建渠道
     */
    Long createChannel(GoodsChannelCreateCommand command);

    /**
     * 更新渠道
     */
    Boolean updateChannel(GoodsChannelUpdateCommand command);

    /**
     * 删除渠道
     */
    Boolean deleteChannel(GoodsChannelDeleteCommand command);
} 