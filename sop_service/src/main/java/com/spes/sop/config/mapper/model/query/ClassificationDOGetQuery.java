package com.spes.sop.config.mapper.model.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分类单个查询对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClassificationDOGetQuery {

    /**
     * 分类ID
     */
    private Long id;

    /**
     * 类目名称
     */
    private String name;

    /**
     * 父类目ID
     */
    private Long parentId;

    /**
     * 类目层级
     */
    private Integer classLevel;
} 
