package com.spes.sop.config.mapper;

import com.spes.sop.config.mapper.model.entity.GoodsBrandDO;
import com.spes.sop.config.mapper.model.query.GoodsBrandDOGetQuery;
import com.spes.sop.config.mapper.model.query.GoodsBrandDOListQuery;
import com.spes.sop.config.mapper.model.req.GoodsBrandAddReq;
import com.spes.sop.config.mapper.model.req.GoodsBrandDeleteReq;
import com.spes.sop.config.mapper.model.req.GoodsBrandUpdateReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品品牌Mapper接口
 */
@Mapper
public interface GoodsBrandMapper {

    /**
     * 分页查询商品品牌列表
     *
     * @param query 查询条件
     * @return 商品品牌列表
     */
    List<GoodsBrandDO> list(@Param("query") GoodsBrandDOListQuery query);

    /**
     * 查询商品品牌总数
     *
     * @param query 查询条件
     * @return 总数
     */
    Long count(@Param("query") GoodsBrandDOListQuery query);

    /**
     * 查询单个商品品牌
     *
     * @param query 查询条件
     * @return 商品品牌信息
     */
    GoodsBrandDO getGoodsBrand(@Param("query") GoodsBrandDOGetQuery query);

    /**
     * 新增商品品牌
     *
     * @param req 新增请求
     * @return 影响行数
     */
    int insert(@Param("req") GoodsBrandAddReq req);

    /**
     * 更新商品品牌
     *
     * @param req 更新请求
     * @return 影响行数
     */
    int updateById(@Param("req") GoodsBrandUpdateReq req);

    /**
     * 逻辑删除商品品牌
     *
     * @param req 删除请求
     * @return 影响行数
     */
    int delete(@Param("req") GoodsBrandDeleteReq req);
}
