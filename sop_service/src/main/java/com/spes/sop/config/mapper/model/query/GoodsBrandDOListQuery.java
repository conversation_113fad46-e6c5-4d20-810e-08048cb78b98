package com.spes.sop.config.mapper.model.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 商品品牌列表查询对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsBrandDOListQuery {

    /**
     * 主键ID
     */
    private List<Long> ids;

    /**
     * 品牌名称
     */
    private List<String> names;

    /**
     * 品牌名称模糊搜索
     */
    private String nameSearch;

    /**
     * 品牌状态
     */
    private List<String> statuses;

    /**
     * 分页起始位置
     */
    private Integer offset;

    /**
     * 分页大小
     */
    private Integer limit;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序方向
     */
    private String orderDirection;
} 