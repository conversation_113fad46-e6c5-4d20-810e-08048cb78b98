package com.spes.sop.config.mapper.model.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商品品牌新增请求对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsBrandAddReq {

    /**
     * 品牌主键ID（数据库自动生成）
     */
    private Long id;

    /**
     * 品牌名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 更新人ID
     */
    private Long updaterId;
} 