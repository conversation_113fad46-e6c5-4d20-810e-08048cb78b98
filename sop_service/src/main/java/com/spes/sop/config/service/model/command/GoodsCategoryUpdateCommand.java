package com.spes.sop.config.service.model.command;

import com.spes.sop.config.enums.ConfigStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 商品分类更新命令
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsCategoryUpdateCommand {

    /**
     * 分类ID
     */
    @NotNull(message = "分类ID不能为空")
    private Long id;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;
    /**
     * 状态
     */
    private ConfigStatusEnum status;

    /**
     * 更新人ID
     */
    @NotNull(message = "更新人ID不能为空")
    private Long updaterId;
} 