package com.spes.sop.config.service.model.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 商品分类删除命令
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsCategoryDeleteCommand {

    /**
     * 分类ID
     */
    @NotNull(message = "分类ID不能为空")
    private Long id;

    /**
     * 删除人ID
     */
    @NotNull(message = "删除人ID不能为空")
    private Long deleterId;
} 