package com.spes.sop.config.mapper;

import com.spes.sop.config.mapper.model.entity.GoodsSeriesDO;
import com.spes.sop.config.mapper.model.query.GoodsSeriesDOGetQuery;
import com.spes.sop.config.mapper.model.query.GoodsSeriesDOListQuery;
import com.spes.sop.config.mapper.model.req.GoodsSeriesAddReq;
import com.spes.sop.config.mapper.model.req.GoodsSeriesDeleteReq;
import com.spes.sop.config.mapper.model.req.GoodsSeriesUpdateReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品系列Mapper接口
 */
@Mapper
public interface GoodsSeriesMapper {

    /**
     * 分页查询商品系列列表
     *
     * @param query 查询条件
     * @return 商品系列列表
     */
    List<GoodsSeriesDO> list(@Param("query") GoodsSeriesDOListQuery query);

    /**
     * 查询商品系列总数
     *
     * @param query 查询条件
     * @return 总数
     */
    Long count(@Param("query") GoodsSeriesDOListQuery query);

    /**
     * 查询单个商品系列
     *
     * @param query 查询条件
     * @return 商品系列信息
     */
    GoodsSeriesDO getGoodsSeries(@Param("query") GoodsSeriesDOGetQuery query);

    /**
     * 新增商品系列
     *
     * @param req 新增请求
     * @return 影响行数
     */
    int insert(@Param("req") GoodsSeriesAddReq req);


    /**
     * 更新商品系列
     *
     * @param req 更新请求
     * @return 影响行数
     */
    int updateById(@Param("req") GoodsSeriesUpdateReq req);

    /**
     * 逻辑删除商品系列
     *
     * @param req 删除请求
     * @return 影响行数
     */
    int delete(@Param("req") GoodsSeriesDeleteReq req);
} 
