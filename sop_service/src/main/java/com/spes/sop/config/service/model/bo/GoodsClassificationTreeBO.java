package com.spes.sop.config.service.model.bo;

import com.spes.sop.config.enums.ConfigStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 商品分类树形业务对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsClassificationTreeBO {

    /**
     * 分类ID
     */
    private Long id;

    /**
     * 分类名称
     */
    private String name;
    /**
     * 级别
     */
    private Integer level;
    /**
     * 状态
     */
    private ConfigStatusEnum status;

    /**
     * 子分类列表
     */
    private List<GoodsClassificationTreeBO> children;
    /**
     * 创建人
     */
    private Long creatorId;
    /**
     * 创建时间
     */
    private Date createTime;
} 