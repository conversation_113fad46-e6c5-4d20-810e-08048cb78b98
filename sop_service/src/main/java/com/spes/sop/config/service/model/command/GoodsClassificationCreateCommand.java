package com.spes.sop.config.service.model.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 商品分类创建命令
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsClassificationCreateCommand {

    /**
     * 分类名称
     */
    @NotBlank(message = "分类名称不能为空")
    private String name;

    /**
     * 父级分类ID
     */
    private Long parentId;

    /**
     * 级别（1-一级，2-二级，3-三级）
     */
    @NotNull(message = "级别不能为空")
    private Integer level;

    /**
     * 创建人ID
     */
    @NotNull(message = "创建人ID不能为空")
    private Long creatorId;
} 