package com.spes.sop.config.service.model.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 商品品牌删除命令
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsBrandDeleteCommand {

    /**
     * 品牌ID
     */
    @NotNull(message = "品牌ID不能为空")
    private Long id;

    /**
     * 删除人ID
     */
    @NotNull(message = "删除人ID不能为空")
    private Long deleterId;
} 