package com.spes.sop.config.service.convert;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.spes.sop.config.enums.ConfigStatusEnum;
import com.spes.sop.config.mapper.model.entity.GoodsSeriesDO;
import com.spes.sop.config.service.model.bo.GoodsSeriesBO;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 商品系列转换器
 */
public class GoodsSeriesConvert {

    /**
     * 系列DO转换为系列BO
     *
     * @param result 系列DO列表
     * @return 系列BO列表
     */
    public static List<GoodsSeriesBO> convert(List<GoodsSeriesDO> result) {
        if (CollUtil.isEmpty(result)) {
            return Lists.newArrayList();
        }

        return result.stream()
                .filter(Objects::nonNull)
                .map(seriesDO -> GoodsSeriesBO.builder()
                        .id(seriesDO.getId())
                        .name(seriesDO.getName())
                        .description(seriesDO.getDescription())
                        .status(ConfigStatusEnum.valueOf(seriesDO.getStatus()))
                        .creatorId(seriesDO.getCreatorId())
                        .createTime(seriesDO.getCreateTime())
                        .build())
                .collect(Collectors.toList());
    }
} 