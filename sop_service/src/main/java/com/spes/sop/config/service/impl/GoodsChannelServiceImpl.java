package com.spes.sop.config.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.spes.sop.common.exception.BusinessException;
import com.spes.sop.common.page.PageResult;
import com.spes.sop.common.util.IdWorker;
import com.spes.sop.config.enums.ConfigStatusEnum;
import com.spes.sop.config.mapper.GoodsChannelMapper;
import com.spes.sop.config.mapper.model.entity.GoodsChannelDO;
import com.spes.sop.config.mapper.model.query.GoodsChannelDOGetQuery;
import com.spes.sop.config.mapper.model.query.GoodsChannelDOListQuery;
import com.spes.sop.config.mapper.model.req.GoodsChannelAddReq;
import com.spes.sop.config.mapper.model.req.GoodsChannelDeleteReq;
import com.spes.sop.config.mapper.model.req.GoodsChannelUpdateReq;
import com.spes.sop.config.service.GoodsChannelService;
import com.spes.sop.config.service.convert.GoodsChannelConvert;
import com.spes.sop.config.service.model.bo.GoodsChannelBO;
import com.spes.sop.config.service.model.command.GoodsChannelCreateCommand;
import com.spes.sop.config.service.model.command.GoodsChannelDeleteCommand;
import com.spes.sop.config.service.model.command.GoodsChannelUpdateCommand;
import com.spes.sop.config.service.model.query.ChannelBOPagerQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class GoodsChannelServiceImpl implements GoodsChannelService {

    private final GoodsChannelMapper goodsChannelMapper;

    @Override
    public GoodsChannelBO getById(Long id) {
        if (ObjectUtil.isNull(id)) {
            return null;
        }
        GoodsChannelDO goodsChannel = goodsChannelMapper.getGoodsChannel(GoodsChannelDOGetQuery.builder()
                .id(id)
                .build());
        return GoodsChannelConvert.convert(goodsChannel);
    }

    @Override
    public GoodsChannelBO getByName(String name) {
        if (StrUtil.isBlank(name)) {
            return null;
        }
        return GoodsChannelConvert.convert(goodsChannelMapper.getGoodsChannel(GoodsChannelDOGetQuery.builder()
                .name(name)
                .build()
        ));
    }

    @Override
    public List<GoodsChannelBO> list(ChannelBOPagerQuery query) {
        if (ObjectUtil.isNull(query)) {
            return Lists.newArrayList();
        }

        GoodsChannelDOListQuery queryDO = GoodsChannelDOListQuery.builder()
                .ids(CollUtil.isEmpty(query.getIds()) ? null : Lists.newArrayList(query.getIds()))
                .names(CollUtil.isEmpty(query.getNames()) ? null : Lists.newArrayList(query.getNames()))
                .nameSearch(StrUtil.isBlank(query.getNameSearch()) ? null : query.getNameSearch().trim())
                .statuses(CollUtil.isEmpty(query.getStatuses()) ? null : Lists.newArrayList(query.getStatuses()))
                .offset(ObjectUtil.isNotNull(query.getPager()) ? query.getPager().getOffset() : 0)
                .limit(ObjectUtil.isNotNull(query.getPager()) ? query.getPager().getLimit() : 10)
                .build();
        List<GoodsChannelDO> result = goodsChannelMapper.list(queryDO);
        return GoodsChannelConvert.convert(result);
    }

    @Override
    public Long count(ChannelBOPagerQuery query) {
        if (ObjectUtil.isNull(query)) {
            return 0L;
        }
        GoodsChannelDOListQuery queryDO = GoodsChannelDOListQuery.builder()
                .ids(CollUtil.isEmpty(query.getIds()) ? null : Lists.newArrayList(query.getIds()))
                .names(CollUtil.isEmpty(query.getNames()) ? null : Lists.newArrayList(query.getNames()))
                .nameSearch(StrUtil.isBlank(query.getNameSearch()) ? null : query.getNameSearch().trim())
                .statuses(CollUtil.isEmpty(query.getStatuses()) ? null : Lists.newArrayList(query.getStatuses()))
                .build();
        return goodsChannelMapper.count(queryDO);
    }

    @Override
    public PageResult<GoodsChannelBO> pager(ChannelBOPagerQuery query) {
        if (ObjectUtil.isNull(query.getPager())) {
            throw new BusinessException("分页参数错误");
        }
        Long count = count(query);
        // 大于 0 进行列表查询
        if (count <= 0) {
            return PageResult.of(Collections.emptyList(), 0L, query.getPager().getPageNum(),
                    query.getPager().getPageSize());
        }
        return PageResult.of(list(query), count, query.getPager().getPageNum(), query.getPager().getPageSize()
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createChannel(GoodsChannelCreateCommand command) {
        log.info("创建渠道，命令参数：{}", command);

        Long id = IdWorker.generateId();

        GoodsChannelAddReq addReq = GoodsChannelAddReq.builder()
                .id(id)
                .name(command.getName())
                .description(command.getDescription())
                .status(ConfigStatusEnum.DISABLE.name())
                .creatorId(command.getCreatorId())
                .updaterId(command.getCreatorId())
                .build();

        int result = goodsChannelMapper.insert(addReq);
        if (result <= 0) {
            throw new BusinessException("创建渠道失败");
        }

        log.info("创建渠道成功，ID：{}", id);
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateChannel(GoodsChannelUpdateCommand command) {
        log.info("更新渠道，命令参数：{}", command);

        GoodsChannelUpdateReq updateReq = GoodsChannelUpdateReq.builder()
                .id(command.getId())
                .name(command.getName())
                .description(command.getDescription())
                .status(ObjectUtil.isNull(command.getStatus()) ? null : command.getStatus().name())
                .updaterId(command.getUpdaterId())
                .build();

        int result = goodsChannelMapper.updateById(updateReq);
        if (result <= 0) {
            throw new BusinessException("更新渠道失败，可能渠道不存在");
        }

        log.info("更新渠道成功，ID：{}", command.getId());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteChannel(GoodsChannelDeleteCommand command) {
        log.info("删除渠道，命令参数：{}", command);

        GoodsChannelDeleteReq deleteReq = GoodsChannelDeleteReq.builder()
                .id(command.getId())
                .updaterId(command.getDeleterId())
                .build();

        int result = goodsChannelMapper.delete(deleteReq);
        if (result <= 0) {
            throw new BusinessException("删除渠道失败，可能渠道不存在");
        }

        log.info("删除渠道成功，ID：{}", command.getId());
        return true;
    }
} 