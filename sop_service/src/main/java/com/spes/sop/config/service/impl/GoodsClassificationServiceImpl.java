package com.spes.sop.config.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.spes.sop.common.exception.BusinessException;
import com.spes.sop.common.util.IdWorker;
import com.spes.sop.config.enums.ConfigStatusEnum;
import com.spes.sop.config.mapper.GoodsClassificationMapper;
import com.spes.sop.config.mapper.model.entity.GoodsClassificationDO;
import com.spes.sop.config.mapper.model.query.GoodsClassificationDOListQuery;
import com.spes.sop.config.mapper.model.req.GoodsClassificationAddReq;
import com.spes.sop.config.mapper.model.req.GoodsClassificationDeleteReq;
import com.spes.sop.config.mapper.model.req.GoodsClassificationUpdateReq;
import com.spes.sop.config.service.GoodsClassificationService;
import com.spes.sop.config.service.convert.GoodsClassificationConvert;
import com.spes.sop.config.service.model.bo.GoodsClassificationBO;
import com.spes.sop.config.service.model.bo.GoodsClassificationTreeBO;
import com.spes.sop.config.service.model.command.GoodsClassificationCreateCommand;
import com.spes.sop.config.service.model.command.GoodsClassificationDeleteCommand;
import com.spes.sop.config.service.model.command.GoodsClassificationUpdateCommand;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class GoodsClassificationServiceImpl implements GoodsClassificationService {

    private final GoodsClassificationMapper goodsClassificationMapper;

    @Override
    public List<GoodsClassificationTreeBO> getClassificationTree() {
        List<GoodsClassificationDO> classifications =
                goodsClassificationMapper.list(GoodsClassificationDOListQuery.builder().build());
        //列表转成树
        if (CollUtil.isEmpty(classifications)) {
            return Lists.newArrayList();
        }

        // 将列表根据 parentId 转换为多层级的树形结构
        Map<Long, List<GoodsClassificationDO>> parentMap =
                classifications.stream()
                        .collect(Collectors.groupingBy(GoodsClassificationDO::getParentId));

        // 递归构建树形结构
        return GoodsClassificationConvert.buildTree(0L, parentMap);
    }

    @Override
    public List<GoodsClassificationBO> getClassification(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        List<GoodsClassificationDO> classifications =
                goodsClassificationMapper.list(GoodsClassificationDOListQuery.builder()
                        .ids(ids).build());
        return GoodsClassificationConvert.convert(classifications);
    }

    @Override
    public List<GoodsClassificationBO> getClassificationByName(List<String> names) {
        if (CollUtil.isEmpty(names)) {
            return Lists.newArrayList();
        }
        List<GoodsClassificationDO> classifications =
                goodsClassificationMapper.list(GoodsClassificationDOListQuery.builder()
                        .names(names).build());
        return GoodsClassificationConvert.convert(classifications);
    }

    @Override
    public List<GoodsClassificationBO> listClassificationChildren(List<Long> parentIds) {
        if (CollUtil.isEmpty(parentIds)) {
            return Lists.newArrayList();
        }
        List<GoodsClassificationDO> classifications =
                goodsClassificationMapper.list(GoodsClassificationDOListQuery.builder()
                        .parentIds(parentIds).build());
        return GoodsClassificationConvert.convert(classifications);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createClassification(GoodsClassificationCreateCommand command) {
        log.info("创建商品分类，命令参数：{}", command);

        Long id = IdWorker.generateId();

        GoodsClassificationAddReq addReq = GoodsClassificationAddReq.builder()
                .id(id)
                .name(command.getName())
                .parentId(command.getParentId() != null ? command.getParentId() : 0L)
                .classLevel(command.getLevel())
                .status(ConfigStatusEnum.DISABLE.name())
                .creatorId(command.getCreatorId())
                .updaterId(command.getCreatorId())
                .build();

        int result = goodsClassificationMapper.insert(addReq);
        if (result <= 0) {
            throw new BusinessException("创建商品分类失败");
        }

        log.info("创建商品分类成功，ID：{}", id);
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateClassification(GoodsClassificationUpdateCommand command) {
        log.info("更新商品分类，命令参数：{}", command);

        GoodsClassificationUpdateReq updateReq = GoodsClassificationUpdateReq.builder()
                .id(command.getId())
                .name(command.getName())
                .parentId(command.getParentId())
                .classLevel(command.getLevel())
                .status(ObjectUtil.isNull(command.getStatus()) ? null : command.getStatus().name())
                .updaterId(command.getUpdaterId())
                .build();

        int result = goodsClassificationMapper.updateById(updateReq);
        if (result <= 0) {
            throw new BusinessException("更新商品分类失败，可能分类不存在");
        }

        log.info("更新商品分类成功，ID：{}", command.getId());
        return true;
    }

    @Override
    public Boolean updateClassificationStatus(List<Long> ids, ConfigStatusEnum status, Long updaterId) {
        if (CollUtil.isEmpty(ids)) {
            return true;
        }
        int result = goodsClassificationMapper.updateStatus(ids, status.name(), updaterId);
        if (result <= 0) {
            throw new BusinessException("更新商品分类状态失败，可能分类不存在");
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteClassification(GoodsClassificationDeleteCommand command) {
        log.info("删除商品分类，命令参数：{}", command);

        GoodsClassificationDeleteReq deleteReq = GoodsClassificationDeleteReq.builder()
                .ids(command.getId())
                .updaterId(command.getDeleterId())
                .build();

        int result = goodsClassificationMapper.delete(deleteReq);
        if (result <= 0) {
            throw new BusinessException("删除商品分类失败，可能分类不存在");
        }
        
        log.info("删除商品分类成功，ID：{}", command.getId());
        return true;
    }
} 