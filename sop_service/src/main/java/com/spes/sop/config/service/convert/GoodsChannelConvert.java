package com.spes.sop.config.service.convert;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.spes.sop.config.enums.ConfigStatusEnum;
import com.spes.sop.config.mapper.model.entity.GoodsChannelDO;
import com.spes.sop.config.service.model.bo.GoodsChannelBO;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 商品渠道转换器
 */
public class GoodsChannelConvert {

    /**
     * 渠道DO转换为渠道BO
     *
     * @param result 渠道DO列表
     * @return 渠道BO列表
     */
    public static List<GoodsChannelBO> convert(List<GoodsChannelDO> result) {
        if (CollUtil.isEmpty(result)) {
            return Lists.newArrayList();
        }

        return result.stream()
                .filter(Objects::nonNull)
                .map(a -> convert(a))
                .collect(Collectors.toList());
    }

    /**
     * 渠道DO转换为渠道BO
     *
     * @param result 渠道DO
     * @return 渠道BO
     */
    public static GoodsChannelBO convert(GoodsChannelDO result) {
        if (result == null) {
            return null;
        }
        return GoodsChannelBO.builder()
                .id(result.getId())
                .name(result.getName())
                .description(result.getDescription())
                .status(ConfigStatusEnum.getByStatus(result.getStatus()))
                .creatorId(result.getCreatorId())
                .createTime(result.getCreateTime())
                .build();
    }
} 