package com.spes.sop.config.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.spes.sop.common.exception.BusinessException;
import com.spes.sop.common.page.PageResult;
import com.spes.sop.common.util.IdWorker;
import com.spes.sop.config.enums.ConfigStatusEnum;
import com.spes.sop.config.mapper.GoodsCategoryMapper;
import com.spes.sop.config.mapper.model.entity.GoodsCategoryDO;
import com.spes.sop.config.mapper.model.query.GoodsCategoryDOGetQuery;
import com.spes.sop.config.mapper.model.query.GoodsCategoryDOListQuery;
import com.spes.sop.config.mapper.model.req.GoodsCategoryAddReq;
import com.spes.sop.config.mapper.model.req.GoodsCategoryDeleteReq;
import com.spes.sop.config.mapper.model.req.GoodsCategoryUpdateReq;
import com.spes.sop.config.service.GoodsCategoryService;
import com.spes.sop.config.service.convert.GoodsCategoryConvert;
import com.spes.sop.config.service.model.bo.GoodsCategoryBO;
import com.spes.sop.config.service.model.command.GoodsCategoryCreateCommand;
import com.spes.sop.config.service.model.command.GoodsCategoryDeleteCommand;
import com.spes.sop.config.service.model.command.GoodsCategoryUpdateCommand;
import com.spes.sop.config.service.model.query.CategoryBOPagerQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class GoodsCategoryServiceImpl implements GoodsCategoryService {

    private final GoodsCategoryMapper goodsCategoryMapper;


    @Override
    public GoodsCategoryBO getById(Long id) {
        if (ObjectUtil.isNull(id)) {
            return null;
        }
        GoodsCategoryDO category = goodsCategoryMapper.getGoodsCategory(GoodsCategoryDOGetQuery.builder()
                .id(id)
                .build());
        return GoodsCategoryConvert.convert(category);
    }

    @Override
    public GoodsCategoryBO getByName(String name) {
        if (StrUtil.isBlank(name)) {
            return null;
        }
        GoodsCategoryDO category = goodsCategoryMapper.getGoodsCategory(GoodsCategoryDOGetQuery.builder()
                .name(name)
                .build());
        return GoodsCategoryConvert.convert(category);
    }

    @Override
    public List<GoodsCategoryBO> list(CategoryBOPagerQuery query) {
        if (ObjectUtil.isNull(query)) {
            return Lists.newArrayList();
        }
        GoodsCategoryDOListQuery doQuery = GoodsCategoryDOListQuery.builder()
                .nameSearch(StrUtil.isBlank(query.getNameSearch()) ? null : query.getNameSearch().trim())
                .statuses(query.getStatuses())
                .ids(query.getIds())
                .names(query.getNames())
                .offset(ObjectUtil.isNotNull(query.getPager()) ? query.getPager().getOffset() : 0)
                .limit(ObjectUtil.isNotNull(query.getPager()) ? query.getPager().getLimit() : 10)
                .build();
        return GoodsCategoryConvert.convert(goodsCategoryMapper.list(doQuery));
    }

    @Override
    public Long count(CategoryBOPagerQuery query) {
        if (ObjectUtil.isNull(query)) {
            return 0L;
        }
        GoodsCategoryDOListQuery doQuery = GoodsCategoryDOListQuery.builder()
                .nameSearch(StrUtil.isBlank(query.getNameSearch()) ? null : query.getNameSearch().trim())
                .statuses(query.getStatuses())
                .ids(query.getIds())
                .names(query.getNames())
                .build();
        return goodsCategoryMapper.count(doQuery);
    }

    @Override
    public PageResult<GoodsCategoryBO> pager(CategoryBOPagerQuery query) {
        if (ObjectUtil.isNull(query.getPager())) {
            throw new BusinessException("分页参数错误");
        }
        Long count = count(query);
        // 大于 0 进行列表查询
        if (count <= 0) {
            return PageResult.of(Collections.emptyList(), 0L, query.getPager().getPageNum(),
                    query.getPager().getPageSize());
        }
        return PageResult.of(list(query), count, query.getPager().getPageNum(), query.getPager().getPageSize());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createCategory(GoodsCategoryCreateCommand command) {
        log.info("创建分类，命令参数：{}", command);

        Long id = IdWorker.generateId();

        GoodsCategoryAddReq addReq = GoodsCategoryAddReq.builder()
                .id(id)
                .name(command.getName())
                .description(command.getDescription())
                .status(ConfigStatusEnum.DISABLE.name())
                .creatorId(command.getCreatorId())
                .updaterId(command.getCreatorId())
                .build();

        int result = goodsCategoryMapper.insert(addReq);
        if (result <= 0) {
            throw new BusinessException("创建分类失败");
        }

        log.info("创建分类成功，ID：{}", id);
        return id;
    }

    @Override
    public Boolean updateCategory(GoodsCategoryUpdateCommand command) {
        log.info("更新分类，命令参数：{}", command);

        GoodsCategoryUpdateReq updateReq = GoodsCategoryUpdateReq.builder()
                .id(command.getId())
                .name(command.getName())
                .description(command.getDescription())
                .status(ObjectUtil.isNull(command.getStatus()) ? null : command.getStatus().name())
                .updaterId(command.getUpdaterId())
                .build();

        int result = goodsCategoryMapper.updateById(updateReq);
        if (result <= 0) {
            throw new BusinessException("更新分类失败，可能分类不存在");
        }

        log.info("更新分类成功，ID：{}", command.getId());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteCategory(GoodsCategoryDeleteCommand command) {
        log.info("删除分类，命令参数：{}", command);

        GoodsCategoryDeleteReq deleteReq = GoodsCategoryDeleteReq.builder()
                .id(command.getId())
                .updaterId(command.getDeleterId())
                .build();

        int result = goodsCategoryMapper.delete(deleteReq);
        if (result <= 0) {
            throw new BusinessException("删除分类失败，可能分类不存在");
        }

        log.info("删除分类成功，ID：{}", command.getId());
        return true;
    }
} 