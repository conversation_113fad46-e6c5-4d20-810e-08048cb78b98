package com.spes.sop.config.service.model.bo;

import com.spes.sop.config.enums.ConfigStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 商品系列业务对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsSeriesBO {
    /**
     * 系列ID
     */
    private Long id;
    /**
     * 系列名称
     */
    private String name;
    /**
     * 描述
     */
    private String description;
    /**
     * 状态
     */
    private ConfigStatusEnum status;
    /**
     * 创建人
     */
    private Long creatorId;
    /**
     * 创建时间
     */
    private Date createTime;
} 