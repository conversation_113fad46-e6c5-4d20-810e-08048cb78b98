package com.spes.sop.config.service.convert;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.spes.sop.config.enums.ConfigStatusEnum;
import com.spes.sop.config.mapper.model.entity.GoodsClassificationDO;
import com.spes.sop.config.service.model.bo.GoodsClassificationBO;
import com.spes.sop.config.service.model.bo.GoodsClassificationTreeBO;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 商品分类转换器
 */
public class GoodsClassificationConvert {

    /**
     * 分类DO转换为分类BO
     *
     * @param result 分类DO列表
     * @return 分类BO列表
     */
    public static List<GoodsClassificationBO> convert(List<GoodsClassificationDO> result) {
        if (CollUtil.isEmpty(result)) {
            return Lists.newArrayList();
        }

        return result.stream()
                .filter(Objects::nonNull)
                .map(classificationDO -> GoodsClassificationBO.builder()
                        .id(classificationDO.getId())
                        .name(classificationDO.getName())
                        .level(classificationDO.getClassLevel())
                        .creatorId(classificationDO.getCreatorId())
                        .createTime(classificationDO.getCreateTime())
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 构建分类树形结构
     *
     * @param parentId  父级ID
     * @param parentMap 父级映射关系
     * @return 树形结构列表
     */
    public static List<GoodsClassificationTreeBO> buildTree(long parentId,
                                                            Map<Long, List<GoodsClassificationDO>> parentMap) {
        //结束条件
        if (!parentMap.containsKey(parentId)) {
            return null;
        }
        // 递归构建树形结构
        return parentMap.getOrDefault(parentId, Collections.emptyList()).stream()
                .map(classification -> GoodsClassificationTreeBO.builder()
                        .id(classification.getId())
                        .name(classification.getName())
                        .level(classification.getClassLevel())
                        .status(ConfigStatusEnum.getByStatus(classification.getStatus()))
                        .children(buildTree(classification.getId(), parentMap))
                        .creatorId(classification.getCreatorId())
                        .createTime(classification.getCreateTime())
                        .build())
                .collect(Collectors.toList());
    }
} 