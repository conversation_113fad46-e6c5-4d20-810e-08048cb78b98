package com.spes.sop.config.mapper.model.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商品品牌更新请求对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsBrandUpdateReq {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 品牌名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态
     */
    private String status;

    /**
     * 更新人ID
     */
    private Long updaterId;
} 