package com.spes.sop.config.service.model.query;

import com.spes.sop.common.page.BasePager;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;


@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChannelBOPagerQuery extends BasePager {

    /**
     * 主键ID
     */
    private List<Long> ids;

    /**
     * 品牌名称
     */
    private List<String> names;

    /**
     * 品牌名称模糊搜索
     */
    private String nameSearch;

    /**
     * 品牌状态
     */
    private List<String> statuses;
}
