package com.spes.sop.config.service;

import com.spes.sop.common.page.PageResult;
import com.spes.sop.config.service.model.bo.GoodsSeriesBO;
import com.spes.sop.config.service.model.command.GoodsSeriesCreateCommand;
import com.spes.sop.config.service.model.command.GoodsSeriesDeleteCommand;
import com.spes.sop.config.service.model.command.GoodsSeriesUpdateCommand;
import com.spes.sop.config.service.model.query.SeriesBOPagerQuery;

import java.util.List;

/**
 * 商品系列服务接口
 */
public interface GoodsSeriesService {
    /**
     * 列表
     */
    List<GoodsSeriesBO> list(SeriesBOPagerQuery query);
    /**
     * 总数
     */
    Long count(SeriesBOPagerQuery query);
    /**
     * 分页
     */
    PageResult<GoodsSeriesBO> pager(SeriesBOPagerQuery query);
    /**
     * 创建系列
     */
    Long createSeries(GoodsSeriesCreateCommand command);

    /**
     * 更新系列
     */
    Boolean updateSeries(GoodsSeriesUpdateCommand command);

    /**
     * 删除系列
     */
    Boolean deleteSeries(GoodsSeriesDeleteCommand command);
} 