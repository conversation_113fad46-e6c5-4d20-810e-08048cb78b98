package com.spes.sop.config.service.model.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 商品渠道创建命令
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsChannelCreateCommand {

    /**
     * 渠道名称
     */
    @NotBlank(message = "渠道名称不能为空")
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建人ID
     */
    @NotNull(message = "创建人ID不能为空")
    private Long creatorId;
} 