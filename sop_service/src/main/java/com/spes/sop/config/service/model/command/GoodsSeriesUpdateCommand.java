package com.spes.sop.config.service.model.command;

import com.spes.sop.config.enums.ConfigStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 商品系列更新命令
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsSeriesUpdateCommand {

    /**
     * 系列ID
     */
    @NotNull(message = "系列ID不能为空")
    private Long id;

    /**
     * 系列名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态
     */
    private ConfigStatusEnum status;

    /**
     * 更新人ID
     */
    @NotNull(message = "更新人ID不能为空")
    private Long updaterId;
} 