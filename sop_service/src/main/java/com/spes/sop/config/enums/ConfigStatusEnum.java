package com.spes.sop.config.enums;

public enum ConfigStatusEnum {

    ENABLE("启用"),
    DISABLE("禁用");

    private String statusDesc;

    ConfigStatusEnum(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public static ConfigStatusEnum getByStatus(String status) {
        for (ConfigStatusEnum value : ConfigStatusEnum.values()) {
            if (value.name().equals(status)) {
                return value;
            }
        }
        return null;
    }

    public static String getStatusDesc(String status) {
        for (ConfigStatusEnum value : ConfigStatusEnum.values()) {
            if (value.name().equals(status)) {
                return value.getStatusDesc();
            }
        }
        return null;
    }
}
