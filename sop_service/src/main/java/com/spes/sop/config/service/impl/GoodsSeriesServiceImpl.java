package com.spes.sop.config.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.spes.sop.common.exception.BusinessException;
import com.spes.sop.common.page.PageResult;
import com.spes.sop.common.util.IdWorker;
import com.spes.sop.config.enums.ConfigStatusEnum;
import com.spes.sop.config.mapper.GoodsSeriesMapper;
import com.spes.sop.config.mapper.model.query.GoodsSeriesDOListQuery;
import com.spes.sop.config.mapper.model.req.GoodsSeriesAddReq;
import com.spes.sop.config.mapper.model.req.GoodsSeriesDeleteReq;
import com.spes.sop.config.mapper.model.req.GoodsSeriesUpdateReq;
import com.spes.sop.config.service.GoodsSeriesService;
import com.spes.sop.config.service.convert.GoodsSeriesConvert;
import com.spes.sop.config.service.model.bo.GoodsSeriesBO;
import com.spes.sop.config.service.model.command.GoodsSeriesCreateCommand;
import com.spes.sop.config.service.model.command.GoodsSeriesDeleteCommand;
import com.spes.sop.config.service.model.command.GoodsSeriesUpdateCommand;
import com.spes.sop.config.service.model.query.SeriesBOPagerQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class GoodsSeriesServiceImpl implements GoodsSeriesService {

    private final GoodsSeriesMapper goodsSeriesMapper;

    @Override
    public List<GoodsSeriesBO> list(SeriesBOPagerQuery query) {
        if (ObjectUtil.isNull(query)) {
            return Lists.newArrayList();
        }
        GoodsSeriesDOListQuery queryDO = GoodsSeriesDOListQuery.builder()
                .ids(CollUtil.isEmpty(query.getIds()) ? null : Lists.newArrayList(query.getIds()))
                .names(CollUtil.isEmpty(query.getNames()) ? null : Lists.newArrayList(query.getNames()))
                .nameSearch(StrUtil.isBlank(query.getNameSearch()) ? null : query.getNameSearch().trim())
                .statuses(CollUtil.isEmpty(query.getStatuses()) ? null : Lists.newArrayList(
                        query.getStatuses()
                ))
                .offset(ObjectUtil.isNotNull(query.getPager()) ? query.getPager().getOffset() : 0)
                .limit(ObjectUtil.isNotNull(query.getPager()) ? query.getPager().getLimit() : 10)
                .build();
        return GoodsSeriesConvert.convert(goodsSeriesMapper.list(queryDO));
    }

    @Override
    public Long count(SeriesBOPagerQuery query) {
        if (ObjectUtil.isNull(query)) {
            return 0L;
        }
        GoodsSeriesDOListQuery queryDO = GoodsSeriesDOListQuery.builder()
                .ids(CollUtil.isEmpty(query.getIds()) ? null : Lists.newArrayList(query.getIds()))
                .names(CollUtil.isEmpty(query.getNames()) ? null : Lists.newArrayList(query.getNames()))
                .nameSearch(StrUtil.isBlank(query.getNameSearch()) ? null : query.getNameSearch().trim())
                .statuses(CollUtil.isEmpty(query.getStatuses()) ? null : Lists.newArrayList(
                        query.getStatuses()
                ))
                .offset(ObjectUtil.isNotNull(query.getPager()) ? query.getPager().getOffset() : 0)
                .limit(ObjectUtil.isNotNull(query.getPager()) ? query.getPager().getLimit() : 10)
                .build();
        return goodsSeriesMapper.count(queryDO);
    }

    @Override
    public PageResult<GoodsSeriesBO> pager(SeriesBOPagerQuery query) {
        if (ObjectUtil.isNull(query.getPager())) {
            throw new BusinessException("分页参数错误");
        }
        //首先判断数量
        Long count = count(query);
        // 大于 0 进行列表查询
        if (count <= 0) {
            return PageResult.of(Collections.emptyList(), 0L, query.getPager().getPageNum(),
                    query.getPager().getPageSize());
        }
        List<GoodsSeriesBO> list = list(query);
        return PageResult.of(list, count, query.getPager().getPageNum(), query.getPager().getPageSize());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createSeries(GoodsSeriesCreateCommand command) {
        log.info("创建系列，命令参数：{}", command);

        Long id = IdWorker.generateId();

        GoodsSeriesAddReq addReq = GoodsSeriesAddReq.builder()
                .id(id)
                .name(command.getName())
                .description(command.getDescription())
                .status(ConfigStatusEnum.DISABLE.name())
                .creatorId(command.getCreatorId())
                .updaterId(command.getCreatorId())
                .build();

        int result = goodsSeriesMapper.insert(addReq);
        if (result <= 0) {
            throw new BusinessException("创建系列失败");
        }

        log.info("创建系列成功，ID：{}", id);
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateSeries(GoodsSeriesUpdateCommand command) {
        log.info("更新系列，命令参数：{}", command);

        GoodsSeriesUpdateReq updateReq = GoodsSeriesUpdateReq.builder()
                .id(command.getId())
                .name(command.getName())
                .description(command.getDescription())
                .status(ObjectUtil.isNotNull(command.getStatus()) ? command.getStatus().name() : null)
                .updaterId(command.getUpdaterId())
                .build();

        int result = goodsSeriesMapper.updateById(updateReq);
        if (result <= 0) {
            throw new BusinessException("更新系列失败，可能系列不存在");
        }

        log.info("更新系列成功，ID：{}", command.getId());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteSeries(GoodsSeriesDeleteCommand command) {
        log.info("删除系列，命令参数：{}", command);

        GoodsSeriesDeleteReq deleteReq = GoodsSeriesDeleteReq.builder()
                .id(command.getId())
                .updaterId(command.getDeleterId())
                .build();

        int result = goodsSeriesMapper.delete(deleteReq);
        if (result <= 0) {
            throw new BusinessException("删除系列失败，可能系列不存在");
        }

        log.info("删除系列成功，ID：{}", command.getId());
        return true;
    }
}