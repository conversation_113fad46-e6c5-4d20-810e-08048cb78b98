package com.spes.sop.config.service.model.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 商品渠道删除命令
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsChannelDeleteCommand {

    /**
     * 渠道ID
     */
    @NotNull(message = "渠道ID不能为空")
    private Long id;

    /**
     * 删除人ID
     */
    @NotNull(message = "删除人ID不能为空")
    private Long deleterId;
} 