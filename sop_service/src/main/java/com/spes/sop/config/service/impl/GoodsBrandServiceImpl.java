package com.spes.sop.config.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.spes.sop.common.exception.BusinessException;
import com.spes.sop.common.page.PageResult;
import com.spes.sop.common.util.IdWorker;
import com.spes.sop.config.enums.ConfigStatusEnum;
import com.spes.sop.config.mapper.GoodsBrandMapper;
import com.spes.sop.config.mapper.model.query.GoodsBrandDOGetQuery;
import com.spes.sop.config.mapper.model.query.GoodsBrandDOListQuery;
import com.spes.sop.config.mapper.model.req.GoodsBrandAddReq;
import com.spes.sop.config.mapper.model.req.GoodsBrandDeleteReq;
import com.spes.sop.config.mapper.model.req.GoodsBrandUpdateReq;
import com.spes.sop.config.service.GoodsBrandService;
import com.spes.sop.config.service.convert.GoodsBrandConvert;
import com.spes.sop.config.service.model.bo.GoodsBrandBO;
import com.spes.sop.config.service.model.command.GoodsBrandCreateCommand;
import com.spes.sop.config.service.model.command.GoodsBrandDeleteCommand;
import com.spes.sop.config.service.model.command.GoodsBrandUpdateCommand;
import com.spes.sop.config.service.model.query.BrandBOPagerQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class GoodsBrandServiceImpl implements GoodsBrandService {

    private final GoodsBrandMapper goodsBrandMapper;

    @Override
    public GoodsBrandBO getBrand(Long id) {
        if (ObjectUtil.isNull(id)) {
            return null;
        }
        return GoodsBrandConvert.convert(goodsBrandMapper.getGoodsBrand(GoodsBrandDOGetQuery.builder().id(id).build()));
    }

    @Override
    public GoodsBrandBO getBrandByName(String name) {
        if (StrUtil.isBlank(name)) {
            return null;
        }
        return GoodsBrandConvert.convert(goodsBrandMapper.getGoodsBrand(GoodsBrandDOGetQuery.builder().name(name).build()));
    }

    @Override
    public List<GoodsBrandBO> listBrands(BrandBOPagerQuery query) {
        if (ObjectUtil.isNull(query)) {
            return Lists.newArrayList();
        }
        GoodsBrandDOListQuery doQuery = GoodsBrandDOListQuery.builder()
                .nameSearch(StrUtil.isBlank(query.getNameSearch()) ? null : query.getNameSearch().trim())
                .statuses(query.getStatuses())
                .ids(query.getIds())
                .names(query.getNames())
                .offset(ObjectUtil.isNotNull(query.getPager()) ? query.getPager().getOffset() : 0)
                .limit(ObjectUtil.isNotNull(query.getPager()) ? query.getPager().getLimit() : 10)
                .build();
        return GoodsBrandConvert.convert(goodsBrandMapper.list(doQuery));
    }

    @Override
    public Long countBrands(BrandBOPagerQuery query) {
        if (ObjectUtil.isNull(query)) {
            return 0L;
        }
        GoodsBrandDOListQuery doQuery = GoodsBrandDOListQuery.builder()
                .nameSearch(StrUtil.isBlank(query.getNameSearch()) ? null : query.getNameSearch().trim())
                .statuses(query.getStatuses())
                .ids(query.getIds())
                .names(query.getNames())
                .offset(ObjectUtil.isNotNull(query.getPager()) ? query.getPager().getOffset() : 0)
                .limit(ObjectUtil.isNotNull(query.getPager()) ? query.getPager().getLimit() : 10)
                .build();
        return goodsBrandMapper.count(doQuery);
    }

    @Override
    public PageResult<GoodsBrandBO> pagerBrands(BrandBOPagerQuery query) {
        if (ObjectUtil.isNull(query.getPager())) {
            throw new BusinessException("分页参数错误");
        }
        //首先判断数量
        Long count = countBrands(query);
        // 大于 0 进行列表查询
        if (count <= 0) {
            return PageResult.of(Collections.emptyList(), 0L, query.getPager().getPageNum(),
                    query.getPager().getPageSize());
        }
        List<GoodsBrandBO> list = listBrands(query);
        return PageResult.of(list, count, query.getPager().getPageNum(), query.getPager().getPageSize());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createBrand(GoodsBrandCreateCommand command) {
        log.info("创建品牌，命令参数：{}", command);

        Long id = IdWorker.generateId();

        GoodsBrandAddReq addReq = GoodsBrandAddReq.builder()
                .id(id)
                .name(command.getName())
                .description(command.getDescription())
                .status(ConfigStatusEnum.DISABLE.name())
                .creatorId(command.getCreatorId())
                .updaterId(command.getCreatorId())
                .build();

        int result = goodsBrandMapper.insert(addReq);
        if (result <= 0) {
            throw new BusinessException("创建品牌失败");
        }

        log.info("创建品牌成功，ID：{}", id);
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateBrand(GoodsBrandUpdateCommand command) {
        log.info("更新品牌，命令参数：{}", command);

        GoodsBrandUpdateReq updateReq = GoodsBrandUpdateReq.builder()
                .id(command.getId())
                .name(command.getName())
                .description(command.getDescription())
                .status(ObjectUtil.isNull(command.getStatus()) ? null : command.getStatus().name())
                .updaterId(command.getUpdaterId())
                .build();

        int result = goodsBrandMapper.updateById(updateReq);
        if (result <= 0) {
            throw new BusinessException("更新品牌失败，可能品牌不存在");
        }

        log.info("更新品牌成功，ID：{}", command.getId());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteBrand(GoodsBrandDeleteCommand command) {
        log.info("删除品牌，命令参数：{}", command);

        GoodsBrandDeleteReq deleteReq = GoodsBrandDeleteReq.builder()
                .id(command.getId())
                .updaterId(command.getDeleterId())
                .build();

        int result = goodsBrandMapper.delete(deleteReq);
        if (result <= 0) {
            throw new BusinessException("删除品牌失败，可能品牌不存在");
        }

        log.info("删除品牌成功，ID：{}", command.getId());
        return true;
    }
}