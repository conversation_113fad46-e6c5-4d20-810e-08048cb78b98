package com.spes.sop.config.mapper.model.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商品分类单个查询对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsClassificationDOGetQuery {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 父级分类ID
     */
    private Long parentId;

    /**
     * 分类层级
     */
    private Integer classLevel;
} 
