package com.spes.sop.config.mapper;

import com.spes.sop.config.mapper.model.entity.GoodsCategoryDO;
import com.spes.sop.config.mapper.model.query.GoodsCategoryDOGetQuery;
import com.spes.sop.config.mapper.model.query.GoodsCategoryDOListQuery;
import com.spes.sop.config.mapper.model.req.GoodsCategoryAddReq;
import com.spes.sop.config.mapper.model.req.GoodsCategoryDeleteReq;
import com.spes.sop.config.mapper.model.req.GoodsCategoryUpdateReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品分类Mapper接口
 */
@Mapper
public interface GoodsCategoryMapper {

    /**
     * 分页查询商品分类列表
     *
     * @param query 查询条件
     * @return 商品分类列表
     */
    List<GoodsCategoryDO> list(@Param("query") GoodsCategoryDOListQuery query);

    /**
     * 查询商品分类总数
     *
     * @param query 查询条件
     * @return 总数
     */
    Long count(@Param("query") GoodsCategoryDOListQuery query);

    /**
     * 查询单个商品分类
     *
     * @param query 查询条件
     * @return 商品分类信息
     */
    GoodsCategoryDO getGoodsCategory(@Param("query") GoodsCategoryDOGetQuery query);

    /**
     * 新增商品分类
     *
     * @param req 新增请求
     * @return 影响行数
     */
    int insert(@Param("req") GoodsCategoryAddReq req);

    /**
     * 更新商品分类
     *
     * @param req 更新请求
     * @return 影响行数
     */
    int updateById(@Param("req") GoodsCategoryUpdateReq req);

    /**
     * 逻辑删除商品分类
     *
     * @param req 删除请求
     * @return 影响行数
     */
    int delete(@Param("req") GoodsCategoryDeleteReq req);
} 
