package com.spes.sop.config.mapper.model.req;

import com.spes.sop.config.enums.ConfigStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商品分类更新请求对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsClassificationUpdateReq {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 父级分类ID
     */
    private Long parentId;

    /**
     * 分类层级
     */
    private Integer classLevel;

    /**
     * 状态
     *
     * @see ConfigStatusEnum#name()
     */
    private String status;

    /**
     * 更新人ID
     */
    private Long updaterId;
} 
