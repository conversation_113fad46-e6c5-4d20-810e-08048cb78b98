package com.spes.sop.log.service.impl;

import com.spes.sop.log.mapper.OperationLogMapper;
import com.spes.sop.log.mapper.model.entity.OperationLogDO;
import com.spes.sop.log.service.OperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 操作日志服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class OperationLogServiceImpl implements OperationLogService {

    @Autowired
    private OperationLogMapper operationLogMapper;

    @Override
    @Async("operationLogExecutor")
    public void saveOperationLog(OperationLogDO operationLog) {
        try {
            operationLogMapper.insert(operationLog);
        } catch (Exception e) {
            log.error("保存操作日志失败", e);
        }
    }
} 