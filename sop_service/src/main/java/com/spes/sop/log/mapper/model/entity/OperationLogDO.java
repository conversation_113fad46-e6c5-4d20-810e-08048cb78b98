package com.spes.sop.log.mapper.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 操作日志数据对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OperationLogDO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 操作用户ID
     */
    private Long userId;

    /**
     * 操作用户名
     */
    private String username;

    /**
     * 请求IP地址
     */
    private String ipAddress;

    /**
     * 请求方法（GET、POST等）
     */
    private String requestMethod;

    /**
     * 请求URL
     */
    private String requestUrl;

    /**
     * 请求路径（不含域名和参数）
     */
    private String requestPath;

    /**
     * 类名
     */
    private String className;

    /**
     * 方法名
     */
    private String methodName;

    /**
     * 请求参数（JSON格式）
     */
    private String requestParams;

    /**
     * 响应结果（JSON格式）
     */
    private String responseResult;

    /**
     * 请求开始时间
     */
    private Date requestTime;

    /**
     * 响应时间
     */
    private Date responseTime;

    /**
     * 执行耗时（毫秒）
     */
    private Long executionTime;

    /**
     * 是否成功（0-失败，1-成功）
     */
    private Integer success;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 操作描述
     */
    private String operationDesc;

    /**
     * User-Agent
     */
    private String userAgent;

    /**
     * 创建时间
     */
    private Date createTime;
} 