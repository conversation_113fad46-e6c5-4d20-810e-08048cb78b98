package com.spes.sop.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.spes.sop.common.enums.GoodsStatusEnum;
import com.spes.sop.goods.service.combination.CombinationService;
import com.spes.sop.goods.service.combination.model.bo.CombinationDetailBO;
import com.spes.sop.goods.service.combination.model.bo.CombinationListBO;
import com.spes.sop.goods.service.combination.model.bo.CombinationSkuRelationBO;
import com.spes.sop.goods.service.combination.model.command.CombinationUpdateCommand;
import com.spes.sop.goods.service.combination.model.query.CombinationBOPageQuery;
import com.spes.sop.goods.service.sku.SkuService;
import com.spes.sop.goods.service.sku.model.bo.SkuListBO;
import com.spes.sop.goods.service.sku.model.command.SkuUpdateCommand;
import com.spes.sop.goods.service.sku.model.query.SkuBOListQuery;
import com.spes.sop.goods.service.spu.SpuService;
import com.spes.sop.goods.service.spu.model.bo.SpuBO;
import com.spes.sop.task.convert.ThirdConvert;
import com.spes.sop.third.jackyun.service.JackYunService;
import com.spes.sop.third.kingdee.model.base.RequestResult;
import com.spes.sop.third.kingdee.service.KingdeeCloudService;
import com.spes.sop.third.weaver.constants.WeaverWorkflowConstant;
import com.spes.sop.third.weaver.model.response.TargetFieldInfo;
import com.spes.sop.third.weaver.model.response.WorkflowResult;
import com.spes.sop.third.weaver.service.WeaverWorkflowService;
import com.spes.sop.user.service.user.UserService;
import com.spes.sop.user.service.user.model.bo.UserBO;
import com.spes.sop.user.service.user.model.query.UserPageQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 泛微工作流定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WeaverWorkflowScheduledTask {

    private final WeaverWorkflowService weaverWorkflowService;
    private final SkuService skuService;
    private final SpuService spuService;
    private final CombinationService combinationService;
    private final JackYunService jackYunService;
    private final TransactionTemplate transactionTemplate;
    private final KingdeeCloudService kingdeeCloudService;
    private final UserService userService;

    /**
     * 每 5 分钟执行一次，查询目标工作流的流程
     * 使用常量定义的cron表达式和工作流ID
     */
    @Scheduled(cron = WeaverWorkflowConstant.Schedule.FIVE_MIN_CRON)
    public void queryCombinationWorkflow() {
        log.info("开始查询组合装审批流程");
        List<CombinationListBO> approvingCombinations = combinationService.list(CombinationBOPageQuery.builder()
                .statuses(Collections.singletonList(GoodsStatusEnum.APPROVING)).build());
        if (CollUtil.isEmpty(approvingCombinations)) {
            return;
        }
        List<Long> userIds = approvingCombinations.stream().map(a -> a.getCreatorId()).collect(Collectors.toList());
        List<UserBO> users = userService.getUserPage(UserPageQuery.builder().ids(userIds).build());
        Map<Long, UserBO> userMap = users.stream().collect(Collectors.toMap(UserBO::getId, user -> user));
        transactionTemplate.execute((status) -> {
            for (CombinationListBO combination : approvingCombinations) {
                CombinationDetailBO detail = combinationService.getById(combination.getId());
                List<Long> skuIds =
                        detail.getSkuRelations().stream().map(CombinationSkuRelationBO::getSkuId).collect(Collectors.toList());
                List<SkuListBO> skus = skuService.list(SkuBOListQuery.builder().ids(skuIds).build());
                getCombinationApprove(detail, skus, userMap.get(combination.getCreatorId()));
            }
            return null;
        });
        log.info("结束查询组合装审批流程");
    }

    /**
     * 每 5 分钟执行一次，查询目标工作流的流程
     * 使用常量定义的cron表达式和工作流ID
     */
    @Scheduled(cron = WeaverWorkflowConstant.Schedule.FIVE_MIN_CRON)
    public void querySkuWorkflow() {
        log.info("开始查询SKU审批流程");
        List<SkuListBO> approvingSkus = skuService.list(SkuBOListQuery.builder()
                .oaStatuses(Lists.newArrayList(GoodsStatusEnum.APPROVING, GoodsStatusEnum.WEAVER_SYNC_FAILED))
                .build());

        if (CollUtil.isEmpty(approvingSkus)) {
            return;
        }
        List<Long> userIds = approvingSkus.stream().map(a -> a.getCreatorId()).collect(Collectors.toList());
        List<UserBO> users = userService.getUserPage(UserPageQuery.builder().ids(userIds).build());
        Map<Long, UserBO> userMap = users.stream().collect(Collectors.toMap(UserBO::getId, user -> user));
        for (SkuListBO sku : approvingSkus) {
            UserBO user = userMap.get(sku.getCreatorId());
            // 获取 sku 审批流程
            WorkflowResult workflowDetail = getSkuApprove(sku, user);
            if (ObjectUtil.isNull(workflowDetail)) {
                continue;
            }
            // 判断是否到归档节点
            if (WeaverWorkflowConstant.EndNodeName.END_NODE_NAME.equals(workflowDetail.getCurrentNodeName())) {
                Map<String, TargetFieldInfo> productTableField = ThirdConvert.getProductTableField(sku,
                        workflowDetail.getWorkflowDetailTableInfos());
                if (CollUtil.isEmpty(productTableField)) {
                    continue;
                }
                TargetFieldInfo spuCode = productTableField.get("spuCode");
                if (ObjectUtil.isNull(spuCode)) {
                    continue;
                }
                SpuBO spu = spuService.getBySpuName(spuCode.getFieldValue());
                SkuUpdateCommand skuUpdateCommand = ThirdConvert.convertSkuUpdateCommand(workflowDetail, sku,
                        spu, productTableField);
                if (ObjectUtil.isNull(skuUpdateCommand)) {
                    continue;
                }
                // 更新库表中的数据
                skuService.update(skuUpdateCommand);
            }
        }
        log.info("结束查询SKU审批流程");
    }


    /**
     * 每五分钟执行一次， 同步金蝶
     */
    @Scheduled(cron = WeaverWorkflowConstant.Schedule.FIVE_MIN_CRON)
    public void syncKingdee() {
        log.info("开始同步金蝶流程");
        List<SkuListBO> syncKingdeeSkus = skuService.list(SkuBOListQuery.builder()
                .oaStatuses(Lists.newArrayList(GoodsStatusEnum.APPROVED, GoodsStatusEnum.KINGDEE_SAVE_FAILED,
                        GoodsStatusEnum.KINGDEE_SUBMIT_FAILED, GoodsStatusEnum.KINGDEE_AUDIT_FAILED))
                .build());
        if (CollUtil.isEmpty(syncKingdeeSkus)) {
            log.info("没有需要同步金蝶的SKU");
            return;
        }
        for (SkuListBO sku : syncKingdeeSkus) {
            syncKingdee(sku);
        }
        log.info("结束同步金蝶流程");
    }

    private void syncKingdee(SkuListBO sku) {
        if (GoodsStatusEnum.APPROVED.equals(sku.getStatus()) || GoodsStatusEnum.KINGDEE_SAVE_FAILED.equals(sku.getStatus())) {
            RequestResult saveResult = kingdeeCloudService.saveMaterial(ThirdConvert.buildKingdeeMaterialPayload(sku));
            if (!saveResult.getSuccess()) {
                skuService.update(SkuUpdateCommand.builder()
                        .id(sku.getId())
                        .status(GoodsStatusEnum.KINGDEE_SAVE_FAILED)
                        .syncFail(saveResult.getErrorMessage())
                        .build());
                return;
            }
        }
        if (GoodsStatusEnum.APPROVED.equals(sku.getStatus()) || GoodsStatusEnum.KINGDEE_SUBMIT_FAILED.equals(sku.getStatus())
                || GoodsStatusEnum.KINGDEE_SAVE_FAILED.equals(sku.getStatus())) {
            RequestResult submitResult = kingdeeCloudService.submitBill(sku.getSkuCode());
            if (!submitResult.getSuccess()) {
                skuService.update(SkuUpdateCommand.builder()
                        .id(sku.getId())
                        .status(GoodsStatusEnum.KINGDEE_SUBMIT_FAILED)
                        .syncFail(submitResult.getErrorMessage())
                        .build());
                return;
            }
        }
        RequestResult auditResult = kingdeeCloudService.auditBill(sku.getSkuCode());
        if (!auditResult.getSuccess()) {
            skuService.update(SkuUpdateCommand.builder()
                    .id(sku.getId())
                    .status(GoodsStatusEnum.KINGDEE_AUDIT_FAILED)
                    .syncFail(auditResult.getErrorMessage())
                    .build());
            return;
        }

        skuService.update(SkuUpdateCommand.builder()
                .id(sku.getId())
                .status(GoodsStatusEnum.SUCCESS)
                .build());
    }

    /**
     * 组合装审批流程获取
     */
    private void getCombinationApprove(CombinationDetailBO combination, List<SkuListBO> skus, UserBO user) {
        Long workflowId = combination.getOaId();
        if (workflowId == null) {
            return;
        }

        WorkflowResult workflowDetail = weaverWorkflowService.getWorkflowDetailData(String.valueOf(workflowId),
                String.valueOf(user.getWeaverId()));

        if (ObjectUtil.isNull(workflowDetail) || !WeaverWorkflowConstant.EndNodeName.END_NODE_NAME.equals(workflowDetail.getCurrentNodeName())) {
            return;
        }
        log.info("组合装审批流程通过，组合ID：{}", combination.getId());
        transactionTemplate.execute((status) -> {

            // 流程通过，更新库表中的数据
            combinationService.updateOaStatus(CombinationUpdateCommand.builder()
                    .id(combination.getId())
                    .status(GoodsStatusEnum.APPROVED)
                    .build());
            combinationService.enableSku(combination.getId(), combination.getVersion(), combination.getUpdaterId());
            // 同步至吉客云
            jackYunService.createCombination(ThirdConvert.buildCombinationCreateReq(combination, skus));
            return null;
        });
    }




    /**
     * 获取 sku 泛微流程审批结果
     */
    private WorkflowResult getSkuApprove(SkuListBO sku, UserBO user) {
        Long workflowId = sku.getOaId();
        if (workflowId == null) {
            return null;
        }
        return weaverWorkflowService.getWorkflowDetailData(String.valueOf(workflowId),
                String.valueOf(user.getWeaverId()));
    }

}
