package com.spes.sop.third.kingdee.model.business.enums;

import lombok.Getter;

public enum KingdeeClassificationEnum {
    A0101001("干发喷雾"),
    A0101002("黄胖子"),
    <PERSON>0101003("灰胖子"),
    <PERSON>0101004("绿胖子"),
    <PERSON>0101005("红胖子"),
    <PERSON><PERSON>01006("紫胖子"),
    <PERSON>0101007("蓝胖子"),
    <PERSON>0101008("黑松露洗头有"),
    <PERSON>0101009("鱼子酱"),
    <PERSON><PERSON>01010("红参洗发水"),
    <PERSON>0101011("线下前研系列-绿胖子"),
    <PERSON>0101012("线下前研系列-红胖子"),
    A0101013("线下前研系列-蛋白桑顺"),
    <PERSON>0101014("线下前研系列-黄胖子"),
    A0101015("线下前研系列-海盐控油"),
    A0101016("线下前研系列-干发喷雾"),
    A0101017("线下前研系列-免洗洗发水"),
    A0101018("线下前研系列-组合装"),
    A0101019("线下前研系列-姜茸人参"),
    A0101020("胶原洗护"),
    A0101021("双萃"),
    A0101022("浴场定制"),
    A0101023("免洗洗发水"),
    A0101024("其他洗护"),
    A0101025("组合装"),
    A0101026("赠品-黄胖子"),
    A0101027("赠品-红胖子"),
    A0101028("赠品-线下前研系列-绿胖子"),
    A0101029("赠品-灰胖子"),
    A0101030("赠品-蓝胖子"),
    A0101031("赠品-绿胖子"),
    A0101032("赠品-组合装"),
    A0101033("赠品-线下前研系列-红胖子"),
    A0101034("赠品-线下前研系列-海盐控油"),
    A0101035("赠品-黑松露洗头膏"),
    A0101036("赠品-鱼子酱"),
    A0101037("赠品-免洗洗发水"),
    A0101038("赠品-紫胖子"),
    A0101039("赠品-其他洗护"),
    A0101040("赠品-红参洗发水"),
    A0102001("海盐洁发膏"),
    A0102002("线下前研系列-海盐洁发膏"),
    A0102003("海盐头皮素"),
    A0102004("赠品-海盐洁发膏"),
    A0102005("赠品-线下前研系列-海盐洁发膏"),
    A0102006("赠品-海盐头皮素"),
    A0103001("发膜"),
    A0103002("护发精油"),
    A0103003("修护膏"),
    A0103004("线下前研系列-发膜"),
    A0103005("赠品-发膜"),
    A0103006("赠品-修护膏"),
    A0104001("防脱精华"),
    A0105001("定型喷雾"),
    A0105002("假发片"),
    A0105003("蓬蓬水"),
    A0107001("沐浴露"),
    A0107002("护手霜"),
    A0107003("身体线"),
    A0107004("唇部护理"),
    A0107005("线下前研系列-沐浴露"),
    A0107007("浴场定制"),
    A0110001("赠品"),
    A0110002("包材"),
    A0110003("虚拟产品");

    @Getter
    private final String desc;

    KingdeeClassificationEnum(String desc) {
        this.desc = desc;
    }

    public static KingdeeClassificationEnum getByDesc(String desc) {
        for (KingdeeClassificationEnum value : values()) {
            if (value.getDesc().equals(desc)) {
                return value;
            }
        }
        return null;
    }


}
