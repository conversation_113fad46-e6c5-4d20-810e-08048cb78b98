package com.spes.sop.third.kingdee.model.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class BillSubmit<T> {
    private String formId;
    ;
    private Integer createOrgId;

    private Integer useOrgId;

    private List<T> numbers;

    public BillSubmit(String formId) {
        this.formId = formId;
    }

} 