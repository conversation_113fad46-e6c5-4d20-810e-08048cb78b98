package com.spes.sop.third.weaver.convert;

import cn.hutool.json.JSONUtil;
import com.spes.sop.third.weaver.model.response.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工作流详情转换工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class WorkflowDetailConverter {

    /**
     * 将JSON字符串转换为WorkflowDetailResponse对象
     *
     * @param jsonString JSON字符串
     * @return 工作流详情响应对象
     */
    public static WorkflowResult fromJson(String jsonString) {
        // 解析JSON响应
        OriginalApiResponse apiResponse = JSONUtil.toBean(jsonString, OriginalApiResponse.class);
        if (apiResponse == null || apiResponse.getData() == null) {
            return null;
        }

        DataPayload sourceData = apiResponse.getData();
        WorkflowResult targetResult = new WorkflowResult();

        targetResult.setCurrentNodeId(sourceData.getCurrentNodeId());
        targetResult.setCurrentNodeName(sourceData.getCurrentNodeName());

        // 2. 处理主表信息 (逻辑修改)
        Map<String, TargetFieldInfo> mainTableMap = new HashMap<>();
        if (sourceData.getWorkflowMainTableInfo() != null && sourceData.getWorkflowMainTableInfo().getRequestRecords() != null) {
            for (FieldData field :
                    sourceData.getWorkflowMainTableInfo().getRequestRecords().get(0).getWorkflowRequestTableFields()) {
                if (!StringUtils.hasText(field.getFieldValue()) && !StringUtils.hasText(field.getFieldShowValue())) {
                    continue;
                }
                // 创建包含 fieldValue 和 fieldShowValue 的对象
                TargetFieldInfo targetField = new TargetFieldInfo();
                targetField.setFieldHtmlType(field.getFieldHtmlType());
                targetField.setFieldValue(field.getFieldValue());
                targetField.setFieldShowValue(field.getFieldShowValue());
                if ("5".equals(field.getFieldHtmlType())) {
                    HashMap<String, String> elements = new HashMap<>();
                    for (int i = 0; i < field.getSelectvalues().size(); i++) {
                        elements.put(field.getSelectvalues().get(i), field.getSelectnames().get(i));
                    }
                    targetField.setElements(elements);
                }
                mainTableMap.put(field.getFieldName(), targetField);
            }
        }
        targetResult.setWorkflowMainTableInfo(mainTableMap);

        // 3. 处理明细表信息 (逻辑修改)
        List<DetailTableInfo> detailTables = new ArrayList<>();
        if (sourceData.getWorkflowDetailTableInfos() != null) {
            for (DetailTablePayload sourceDetail : sourceData.getWorkflowDetailTableInfos()) {
                DetailTableInfo targetDetail = new DetailTableInfo();
                targetDetail.setTablename(sourceDetail.getTableDBName());

                List<Map<String, TargetFieldInfo>> records = new ArrayList<>();
                if (sourceDetail.getWorkflowRequestTableRecords() != null) {
                    for (RequestRecord sourceRecord : sourceDetail.getWorkflowRequestTableRecords()) {
                        Map<String, TargetFieldInfo> recordMap = new HashMap<>();
                        for (FieldData field : sourceRecord.getWorkflowRequestTableFields()) {
                            if (!StringUtils.hasText(field.getFieldValue()) && !StringUtils.hasText(field.getFieldShowValue())) {
                                continue;
                            }
                            // 创建包含 fieldValue 和 fieldShowValue 的对象
                            TargetFieldInfo targetField = new TargetFieldInfo();
                            targetField.setFieldHtmlType(field.getFieldHtmlType());
                            targetField.setFieldValue(field.getFieldValue());
                            targetField.setFieldShowValue(field.getFieldShowValue());
                            if ("5".equals(field.getFieldHtmlType())) {
                                HashMap<String, String> elements = new HashMap<>();
                                for (int i = 0; i < field.getSelectvalues().size(); i++) {
                                    elements.put(field.getSelectvalues().get(i), field.getSelectnames().get(i));
                                }
                                targetField.setElements(elements);
                            }
                            recordMap.put(field.getFieldName(), targetField);
                        }
                        records.add(recordMap);
                    }
                }
                targetDetail.setValue(records);
                detailTables.add(targetDetail);
            }
        }
        targetResult.setWorkflowDetailTableInfos(detailTables);

        return targetResult;
    }
} 