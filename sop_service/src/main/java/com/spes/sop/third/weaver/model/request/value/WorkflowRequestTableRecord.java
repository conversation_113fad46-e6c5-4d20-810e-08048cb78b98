package com.spes.sop.third.weaver.model.request.value;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 工作流请求表记录
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowRequestTableRecord {

    /**
     * 记录顺序
     */
    private Integer recordOrder;

    /**
     * 工作流请求表字段列表
     */
    private List<WorkflowRequestTableField> workflowRequestTableFields;
} 