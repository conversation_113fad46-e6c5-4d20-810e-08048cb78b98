/*
 * Copyright(C) 2024 Spes Co., Ltd. All rights reserved.
 */

package com.spes.sop.third.jackyun.client;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.spes.sop.common.exception.BusinessException;
import com.spes.sop.third.jackyun.config.JackyunOpenApiConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

/**
 * 吉客云开放平台HTTP工具类
 * 提供与吉客云开放平台交互的核心功能，包括请求签名、参数构建和HTTP通信
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class JackYunOpenHttpClient {

    // region 依赖注入

    private static JackyunOpenApiConfig config;

    @Autowired
    public void setConfig(JackyunOpenApiConfig config) {
        JackYunOpenHttpClient.config = config;
    }

    /**
     * 获取配置实例，提供空指针安全和默认值处理
     *
     * @return 配置实例
     */
    private static JackyunOpenApiConfig getConfig() {
        if (config == null) {
            log.warn("配置未正确注入，使用默认配置");
            // 创建默认配置以避免空指针异常
            JackyunOpenApiConfig defaultConfig = new JackyunOpenApiConfig();
            setDefaultValues(defaultConfig);
            return defaultConfig;
        }

        // 确保配置中的关键字段有默认值
        ensureDefaultValues(config);
        return config;
    }

    /**
     * 设置默认配置值（用于配置注入失败时的后备方案）
     *
     * @param config 配置对象
     */
    private static void setDefaultValues(JackyunOpenApiConfig config) {
        config.setServerUrl("https://open.jackyun.com/open/openapi/do");
        config.setApiVersion("1.0");
        config.setAppKey("73914332");
        config.setAppSecret("a664417f04db4f0086a77fb0a16c99d1");
        config.setConnectTimeout(30000);
        config.setReadTimeout(30000);
        config.setEncoding("UTF-8");
        config.setDebugEnabled(false);
        config.setRetryCount(3);
    }

    /**
     * 确保配置对象中的非关键字段都有合理的默认值
     * 关键字段（appKey, appSecret, serverUrl）应该从配置文件获取，不提供默认值
     *
     * @param config 配置对象
     */
    private static void ensureDefaultValues(JackyunOpenApiConfig config) {
        if (StrUtil.isBlank(config.getApiVersion())) {
            config.setApiVersion("1.0");
        }
        if (config.getConnectTimeout() == null || config.getConnectTimeout() <= 0) {
            config.setConnectTimeout(30000);
        }
        if (config.getReadTimeout() == null || config.getReadTimeout() <= 0) {
            config.setReadTimeout(30000);
        }
        if (StrUtil.isBlank(config.getEncoding())) {
            config.setEncoding("UTF-8");
        }
        if (config.getDebugEnabled() == null) {
            config.setDebugEnabled(false);
        }
        if (config.getRetryCount() == null || config.getRetryCount() < 0) {
            config.setRetryCount(3);
        }
    }

    /**
     * 时间格式化器
     */
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * MD5算法名称
     */
    private static final String MD5_ALGORITHM = "MD5";

    // endregion

    // region 公共接口

    /**
     * 请求吉客云开放平台
     *
     * @param method  开放接口方法名
     * @param version 开放接口版本号（null表示默认）
     * @param token   ISV客户授权token
     * @param bizData 业务请求数据
     * @return 响应结果字符串
     */
    public static String post(String method, String version, String token, Object bizData) {

        // 参数校验
        validateParameters(method, bizData);

        try {
            // 构造请求参数
            SortedMap<String, String> sortedParams = buildRequestParams(method, version, bizData);

            // 生成签名
            String sign = generateSign(sortedParams);
            sortedParams.put("sign", sign);

            // 添加token（token不参与签名）
            if (StrUtil.isNotBlank(token)) {
                sortedParams.put("token", token);
            }

            // 构造POST数据
            String postData = buildPostData(sortedParams);

            log.info("请求吉客云开放平台 - 方法: {}, 版本: {}, 数据长度: {}", method, version, postData.length());

            // 发送HTTP请求
            String response = postData(getConfig().getServerUrl(), postData);

            log.info("吉客云开放平台响应成功 - 方法: {}, 响应长度: {}", method,
                    (response != null ? response : "").length());

            return response;

        } catch (Exception e) {
            log.error("请求吉客云开放平台失败 - 方法: {}, 错误: {}", method, e.getMessage(), e);
            throw new BusinessException("请求吉客云开放平台失败: " + e.getMessage(), e);
        }
    }

    // endregion

    // region 私有方法

    /**
     * 校验输入参数
     *
     * @param method  方法名
     * @param bizData 业务数据
     */
    private static void validateParameters(String method, Object bizData) {
        if (StrUtil.isBlank(method)) {
            throw new BusinessException("方法名不能为空");
        }
        if (bizData == null) {
            throw new BusinessException("业务数据不能为空");
        }
    }

    /**
     * 构建请求参数
     *
     * @param method  方法名
     * @param version 版本号
     * @param bizData 业务数据
     * @return 排序后的参数Map
     */
    private static SortedMap<String, String> buildRequestParams(String method, String version, Object bizData) {
        SortedMap<String, String> sortedParams = new TreeMap<>();

        sortedParams.put("method", method);
        sortedParams.put("appkey", getConfig().getAppKey());
        sortedParams.put("version", version != null ? version : getConfig().getApiVersion());
        sortedParams.put("contenttype", "json");
        sortedParams.put("timestamp", DATETIME_FORMATTER.format(LocalDateTime.now()));
        sortedParams.put("bizcontent", JSONUtil.toJsonStr(bizData));

        return sortedParams;
    }

    /**
     * 生成签名
     *
     * @param sortedParams 排序后的参数
     * @return 签名字符串
     * @throws NoSuchAlgorithmException MD5算法异常
     */
    private static String generateSign(SortedMap<String, String> sortedParams) throws NoSuchAlgorithmException {
        // 构建待签名字符串
        StringBuilder signBuilder = new StringBuilder(getConfig().getAppSecret());

        for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
            signBuilder.append(entry.getKey()).append(entry.getValue());
        }
        signBuilder.append(getConfig().getAppSecret());

        // 生成MD5签名
        return md5Encrypt(signBuilder.toString().toLowerCase());
    }

    /**
     * 构造POST数据
     *
     * @param sortedParams 参数Map
     * @return POST数据字符串
     */
    private static String buildPostData(SortedMap<String, String> sortedParams) {
        StringBuilder postDataBuilder = new StringBuilder();

        for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
            if (postDataBuilder.length() > 0) {
                postDataBuilder.append("&");
            }

            String key = entry.getKey();
            String value = entry.getValue();

            // 对业务参数进行URL编码
            if ("bizcontent".equalsIgnoreCase(key)) {
                try {
                    postDataBuilder.append(key).append("=")
                            .append(URLEncoder.encode(value, getConfig().getEncoding()));
                } catch (Exception e) {
                    // 编码失败时使用原值
                    postDataBuilder.append(key).append("=").append(value);
                }
            } else {
                postDataBuilder.append(key).append("=").append(value);
            }
        }

        return postDataBuilder.toString();
    }

    /**
     * MD5加密
     *
     * @param text 待加密文本
     * @return 加密后的十六进制字符串
     * @throws NoSuchAlgorithmException MD5算法异常
     */
    private static String md5Encrypt(String text) throws NoSuchAlgorithmException {
        if (StrUtil.isBlank(text)) {
            throw new IllegalArgumentException("待加密文本不能为空");
        }

        MessageDigest md5 = MessageDigest.getInstance(MD5_ALGORITHM);
        byte[] md5Bytes = md5.digest(text.getBytes(StandardCharsets.UTF_8));

        StringBuilder hexBuilder = new StringBuilder();
        for (byte b : md5Bytes) {
            int val = (b) & 0xff;
            if (val < 16) {
                hexBuilder.append("0");
            }
            hexBuilder.append(Integer.toHexString(val));
        }

        return hexBuilder.toString();
    }

    /**
     * 发送POST请求
     *
     * @param url      请求URL
     * @param postData POST数据
     * @return 响应结果
     * @throws IOException IO异常
     */
    private static String postData(String url, String postData) throws IOException {
        return postData(url, postData, getConfig().getEncoding(), getConfig().getReadTimeout());
    }

    /**
     * 发送POST请求（完整版本）
     *
     * @param url      请求URL
     * @param postData POST数据
     * @param encoding 字符编码
     * @param timeout  超时时间
     * @return 响应结果
     * @throws IOException IO异常
     */
    private static String postData(String url, String postData, String encoding, int timeout) throws IOException {
        // 参数校验和默认值设置
        if (StrUtil.isBlank(url)) {
            throw new IllegalArgumentException("请求URL不能为空");
        }
        if (StrUtil.isBlank(encoding)) {
            encoding = getConfig().getEncoding();
        }
        if (timeout <= 0) {
            timeout = getConfig().getReadTimeout();
        }

        PrintWriter printWriter = null;
        BufferedReader bufferedReader = null;

        try {
            // 创建连接
            URL postUrl = new URL(url);
            URLConnection urlConnection = postUrl.openConnection();

            // 设置请求属性
            urlConnection.setRequestProperty("accept", "*/*");
            urlConnection.setRequestProperty("connection", "Keep-Alive");
            urlConnection.setRequestProperty("user-agent",
                    "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            urlConnection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            urlConnection.setRequestProperty("Charset", encoding);

            urlConnection.setConnectTimeout(timeout);
            urlConnection.setReadTimeout(timeout);
            urlConnection.setDoOutput(true);
            urlConnection.setDoInput(true);

            // 发送POST数据
            printWriter = new PrintWriter(urlConnection.getOutputStream());
            printWriter.print(postData);
            printWriter.flush();

            // 读取响应
            bufferedReader = new BufferedReader(new InputStreamReader(urlConnection.getInputStream(), encoding));
            StringBuilder responseBuilder = new StringBuilder();
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                responseBuilder.append(line);
            }

            return responseBuilder.toString();

        } catch (IOException e) {
            log.error("HTTP请求失败 - URL: {}, 错误: {}", url, e.getMessage());
            throw e;
        } finally {
            // 确保资源正确关闭
            if (bufferedReader != null) {
                try {
                    bufferedReader.close();
                } catch (IOException e) {
                    log.warn("关闭BufferedReader失败: {}", e.getMessage());
                }
            }
            if (printWriter != null) {
                try {
                    printWriter.close();
                } catch (Exception e) {
                    log.warn("关闭PrintWriter失败: {}", e.getMessage());
                }
            }
        }
    }
} 