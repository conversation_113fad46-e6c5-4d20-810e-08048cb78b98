package com.spes.sop.third.kingdee.service.base;


import com.spes.sop.third.kingdee.model.base.Authentication;
import com.spes.sop.third.kingdee.model.base.RequestService;

import java.util.List;

/**
 * k3cloud接口服务
 *
 * @param <P> 参数
 * @param <R> 返回值
 * <AUTHOR>
 */
public interface ApiService<P, R> {

    String getEndPoint();

    RequestService getService();

    Authentication getAuth();

    R execute(List<P> params);

}
