package com.spes.sop.third.kingdee.model.business.request;

import com.spes.sop.third.kingdee.model.business.enums.KingdeeClassificationEnum;
import com.spes.sop.third.kingdee.model.business.enums.KingdeeInventoryTypeEnum;
import com.spes.sop.third.kingdee.model.business.enums.KingdeeMaterialAttributeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 物料保存参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialSaveReq {

    /**
     * 物料分组
     */
    private KingdeeClassificationEnum materialGroup;

    /**
     * 编码
     */
    private String skuCode;

    /**
     * 名称
     */
    private String skuName;

    /**
     * 规格型号
     */
    private String specification;

    /**
     * 条码
     */
    private String barCode;

    /**
     * 物料属性
     * 1:外购 -3: 委外
     */
    private KingdeeMaterialAttributeEnum materialAttribute;

    /**
     * 存货类别
     */
    private KingdeeInventoryTypeEnum inventoryType;

}
