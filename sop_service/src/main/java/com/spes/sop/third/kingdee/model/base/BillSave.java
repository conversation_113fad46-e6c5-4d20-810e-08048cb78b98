package com.spes.sop.third.kingdee.model.base;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 单据保存
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class BillSave {
    private String formId;

    /**
     * 对应接口文档中的model
     */
    private Map<String, Object> model;

    public BillSave(String formId) {
        this.formId = formId;
    }

} 