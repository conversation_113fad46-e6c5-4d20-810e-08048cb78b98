package com.spes.sop.third.kingdee.client;

import com.spes.sop.third.kingdee.model.base.BillAudit;
import com.spes.sop.third.kingdee.model.base.BillQuery;
import com.spes.sop.third.kingdee.model.base.BillSave;
import com.spes.sop.third.kingdee.model.base.BillSubmit;

import java.util.List;

public interface K3CloudOperations {

    <T> T executeBillQuery(List<BillQuery> params, Class<T> type);

    <T> List<T> executeBillQuery(BillQuery billQuery, Class<T> type);

    <T> T executeBillSave(BillSave billSave, Class<T> type);

    <T, N> T executeBillSubmit(BillSubmit<N> billSubmit, Class<T> type);

    <T, N> T executeBillAudit(BillAudit<N> billAudit, Class<T> type);

} 