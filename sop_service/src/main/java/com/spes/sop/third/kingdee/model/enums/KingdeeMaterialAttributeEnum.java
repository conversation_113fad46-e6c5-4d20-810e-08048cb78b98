package com.spes.sop.third.kingdee.model.enums;

import lombok.Getter;

public enum KingdeeMaterialAttributeEnum {
    //1:外购 -3: 委外
    EXTERNAL_PURCHASE(1, "外购", false),
    OUTSOURCING(3, "委外", true);


    @Getter
    private final Integer code;
    @Getter
    private final String desc;
    @Getter
    private final Boolean subContract;

    KingdeeMaterialAttributeEnum(Integer code, String desc, Boolean SubContract) {
        this.code = code;
        this.desc = desc;
        this.subContract = SubContract;
    }

    public static KingdeeMaterialAttributeEnum getByDesc(String desc) {
        for (KingdeeMaterialAttributeEnum value : values()) {
            if (value.getDesc().equals(desc)) {
                return value;
            }
        }
        return null;
    }
}
