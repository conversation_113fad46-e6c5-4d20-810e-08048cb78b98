package com.spes.sop.third.kingdee.model.base;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class BillQuery {

    private String fieldKeys;

    private String filterString;

    private String orderString;
    private String formId;


    // 最多允许查询的数量
    private int topRowCount = 0;

    // 分页取数开始行索引
    private int startRow = 0;

    // 无限制
    private int limit = 0;

    public BillQuery(String formId, String fieldKeys, String filterString) {
        this(fieldKeys, filterString, null, formId, 0, 0, 0);
    }

    public String[] getFields() {
        return StringUtils.isEmpty(fieldKeys) ? new String[]{} : fieldKeys.split(",");
    }

} 