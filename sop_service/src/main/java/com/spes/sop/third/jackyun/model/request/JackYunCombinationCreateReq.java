package com.spes.sop.third.jackyun.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JackYunCombinationCreateReq {
    /**
     * 组合装名称
     */
    private String goodsName;
    /**
     * 货品编号
     */
    private String goodsNo;
    /**
     * 计量单位
     */
    private String unitName;
    /**
     * 组合装的子件列表
     */
    private List<JkyCombinationSku> packageSkuList;


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class JkyCombinationSku {
        /**
         * 条码
         */
        private String skuBarcode;
        /**
         * 数量
         */
        private Integer goodsAmount;
        /**
         * 分摊价格
         */
        private BigDecimal sharePrice;
    }
}
