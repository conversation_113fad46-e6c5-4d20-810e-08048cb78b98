package com.spes.sop.third.weaver.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserListPayload {
    private Integer totalSize;
    private List<UserInfo> dataList;
    private Integer pageSize;
    private Integer page;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UserInfo {
        private String id;
        private String workcode;
        private String departmentname;
    }
}
