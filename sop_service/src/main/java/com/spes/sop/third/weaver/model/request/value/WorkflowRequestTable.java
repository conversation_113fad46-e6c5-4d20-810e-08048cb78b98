package com.spes.sop.third.weaver.model.request.value;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 工作流请求记录DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowRequestTable {

    /**
     * 请求记录列表
     */
    private List<WorkflowRequestTableRecord> workflowRequestTableRecords;

    /**
     * 表数据库名称
     */
    private String tableDBName;
} 