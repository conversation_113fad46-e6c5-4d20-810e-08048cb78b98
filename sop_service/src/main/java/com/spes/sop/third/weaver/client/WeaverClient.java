/*
 * Copyright(C) 2024 Spes Co., Ltd. All rights reserved.
 */

package com.spes.sop.third.weaver.client;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.spes.sop.common.exception.BusinessException;
import com.spes.sop.third.weaver.config.WeaverConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.Map;

/**
 * 泛微Token认证工具类
 * 提供泛微系统的RSA认证和API调用功能
 * 每次根据userId动态生成密钥对，不使用缓存
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WeaverClient {

    /**
     * 泛微配置
     */
    private static WeaverConfig weaverConfig;

    @Autowired
    public void setWeaverConfig(WeaverConfig weaverConfig) {
        WeaverClient.weaverConfig = weaverConfig;
    }

    /**
     * RSA算法相关常量
     */
    private static final String RSA_ALGORITHM = "RSA";
    private static final int RSA_KEY_SIZE = 2048;

    /**
     * 网络超时配置
     */
    private static final int CONNECT_TIMEOUT = 10000;

    /**
     * API路径常量
     */
    public static final String API_REGISTER = "/api/ec/dev/auth/regist";
    public static final String API_APPLY_TOKEN = "/api/ec/dev/auth/applytoken";
    public static final String API_CREATE_WORKFLOW_REQUEST = "/api/workflow/paService/doCreateRequest";
    public static final String API_GET_WORKFLOW_REQUEST = "/api/workflow/paService/getWorkflowRequest";
    public static final String USER_SEARCH_CONDITION = "/api/hrm/resful/getHrmUserInfoWithPage";

    /**
     * 认证信息数据类
     */
    private static class AuthInfo {
        String localPrivateKey;
        String localPublicKey;
        String serverPublicKey;
        String serverSecret;
        String serverToken;
        long tokenExpireTime;
    }

    /**
     * 第一步：向ecology系统注册，获取服务端公钥和Secret
     *
     * @param userId 用户ID
     * @return 认证信息
     * @throws BusinessException 注册异常
     */
    private static AuthInfo register(String userId) {
        String address = getServerUrl();
        
        try {
            // 为当前用户生成本地RSA密钥对
            KeyPair keyPair = generateRSAKeyPair();
            String localPrivateKey = Base64.encode(keyPair.getPrivate().getEncoded());
            String localPublicKey = Base64.encode(keyPair.getPublic().getEncoded());

            // 格式化公钥
            String formattedPublicKey = formatKey(localPublicKey);

            // 构建注册请求
            String registUrl = address + API_REGISTER;
            log.info("开始向泛微系统注册，用户ID: {}, URL: {}", userId, registUrl);

            HttpResponse response = HttpRequest.post(registUrl)
                    .header("appid", weaverConfig.getAppId())
                    .header("cpk", formattedPublicKey)
                    .timeout(CONNECT_TIMEOUT)
                    .execute();

            if (!response.isOk()) {
                throw new BusinessException("注册请求失败，HTTP状态码: " + response.getStatus());
            }

            String responseBody = response.body();
            log.info("注册响应: {}", responseBody);

            JSONObject result = JSONUtil.parseObj(responseBody);

            // 创建认证信息对象
            AuthInfo authInfo = new AuthInfo();
            authInfo.localPrivateKey = localPrivateKey;
            authInfo.localPublicKey = localPublicKey;
            
            if (result.containsKey("spk")) {
                authInfo.serverPublicKey = result.getStr("spk");
            }
            if (result.containsKey("secret")) {
                authInfo.serverSecret = result.getStr("secret");
            }

            log.info("泛微系统注册成功，用户ID: {}", userId);
            return authInfo;

        } catch (Exception e) {
            log.error("泛微系统注册失败，用户ID: {}", userId, e);
            throw new BusinessException("泛微系统注册失败: " + e.getMessage(), e);
        }
    }

    /**
     * 第二步：通过注册返回的信息获取Token
     *
     * @param authInfo 认证信息
     * @param userId 用户ID
     * @return 更新后的认证信息
     * @throws BusinessException Token获取异常
     */
    private static AuthInfo getToken(AuthInfo authInfo, String userId) {
        String address = getServerUrl();
        
        try {
            if (StrUtil.isBlank(authInfo.serverPublicKey) || StrUtil.isBlank(authInfo.serverSecret)) {
                throw new BusinessException("认证信息不完整，无法获取Token");
            }

            // 使用服务端公钥加密Secret
            String encryptedSecret = encryptWithRSA(authInfo.serverSecret, authInfo.serverPublicKey);

            // 构建Token获取请求
            String tokenUrl = address + API_APPLY_TOKEN;
            log.info("开始获取Token，用户ID: {}, URL: {}", userId, tokenUrl);

            HttpResponse response = HttpRequest.post(tokenUrl)
                    .header("appid", weaverConfig.getAppId())
                    .header("secret", encryptedSecret)
                    .header("time", String.valueOf(weaverConfig.getTokenExpireTime())) // 使用配置的过期时间
                    .timeout(CONNECT_TIMEOUT)
                    .execute();

            if (!response.isOk()) {
                throw new BusinessException("Token获取请求失败，HTTP状态码: " + response.getStatus());
            }

            String responseBody = response.body();
            log.info("Token获取响应: {}", responseBody);

            JSONObject result = JSONUtil.parseObj(responseBody);

            // 更新认证信息
            if (result.containsKey("token")) {
                authInfo.serverToken = result.getStr("token");
                // 设置过期时间（提前5分钟过期，避免临界点问题）
                authInfo.tokenExpireTime =
                        System.currentTimeMillis() + (weaverConfig.getTokenExpireTime() - 300) * 1000L;
                log.info("Token获取成功，用户ID: {}, 过期时间: {}", userId, new java.util.Date(authInfo.tokenExpireTime));
            }

            return authInfo;

        } catch (Exception e) {
            log.error("Token获取失败，用户ID: {}", userId, e);
            throw new BusinessException("Token获取失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取完整的认证信息
     *
     * @param userId 用户ID
     * @return 认证信息
     */
    private static AuthInfo getAuthInfo(String userId) {
        // 每次都重新注册和获取Token，不使用缓存
        log.info("为用户ID: {} 动态生成认证信息", userId);
        AuthInfo authInfo = register(userId);
        return getToken(authInfo, userId);
    }

    /**
     * GET方式调用泛微接口
     *
     * @param api    REST API接口路径
     * @param params 请求参数
     * @param userId 用户ID
     * @return 接口响应结果
     * @throws BusinessException 接口调用异常
     */
    public static String get(String api, Map<String, Object> params, String userId) {
        return callRestApi(api, Method.GET, params, userId);
    }

    /**
     * GET方式调用泛微接口（使用默认用户ID）
     *
     * @param api    REST API接口路径
     * @param params 请求参数
     * @return 接口响应结果
     * @throws BusinessException 接口调用异常
     */
    public static String get(String api, Map<String, Object> params) {
        return get(api, params, weaverConfig.getDefaultUserId());
    }

    /**
     * POST方式调用泛微接口
     *
     * @param api    REST API接口路径
     * @param params 请求参数
     * @param userId 用户ID
     * @return 接口响应结果
     * @throws BusinessException 接口调用异常
     */
    public static String post(String api, Map<String, Object> params, String userId) {
        return callRestApi(api, Method.POST, params, userId);
    }

    /**
     * POST方式调用泛微接口（使用默认用户ID）
     *
     * @param api    REST API接口路径
     * @param params 请求参数
     * @return 接口响应结果
     * @throws BusinessException 接口调用异常
     */
    public static String post(String api, Map<String, Object> params) {
        return post(api, params, weaverConfig.getDefaultUserId());
    }

    /**
     * 内部方法：调用ecology系统的REST接口
     *
     * @param api    REST API接口路径
     * @param method HTTP请求方法（GET/POST）
     * @param params 请求参数
     * @param userId 用户ID
     * @return 接口响应结果
     * @throws BusinessException 接口调用异常
     */
    private static String callRestApi(String api, Method method, Map<String, Object> params, String userId) {
        String address = getServerUrl();
        if (StrUtil.isBlank(api)) {
            throw new BusinessException("API路径不能为空");
        }
        if (StrUtil.isBlank(userId)) {
            throw new BusinessException("用户ID不能为空");
        }

        try {
            // 为当前用户获取认证信息（每次都重新生成）
            AuthInfo authInfo = getAuthInfo(userId);

            if (StrUtil.isBlank(authInfo.serverToken)) {
                throw new BusinessException("Token获取失败");
            }

            // 使用服务端公钥加密用户ID
            String encryptedUserId = encryptWithRSA(userId, authInfo.serverPublicKey);

            // 构建REST API请求
            String apiUrl = address + api;

            HttpRequest request;
            if (method == Method.GET) {
                request = HttpRequest.get(apiUrl);
                // GET请求参数添加到URL
                if (params != null && !params.isEmpty()) {
                    request.form(params);
                }
            } else {
                request = HttpRequest.post(apiUrl);
                request.header("Content-Type", "application/x-www-form-urlencoded; charset=utf-8");
                // POST请求参数添加到body
                if (params != null && !params.isEmpty()) {
                    request.form(params);
                }
            }

            request.header("appid", weaverConfig.getAppId())
                    .header("token", authInfo.serverToken)
                    .header("userid", encryptedUserId)
                    .timeout(CONNECT_TIMEOUT);

            log.info("开始调用REST API，用户ID: {}, URL: {}", userId, apiUrl);

            HttpResponse response = request.execute();

            if (!response.isOk()) {
                throw new BusinessException("API调用失败，HTTP状态码: " + response.getStatus());
            }

            String responseBody = response.body();
            log.info("API调用成功，用户ID: {}, 响应长度: {}", userId, responseBody.length());

            return responseBody;

        } catch (Exception e) {
            log.error("API调用失败，用户ID: {}", userId, e);
            throw new BusinessException("API调用失败: " + e.getMessage(), e);
        }
    }

    /**
     * 清除缓存（由于不再使用缓存，此方法保留为兼容性方法）
     */
    public static void clearCache() {
        log.info("WeaverClient已改为不使用缓存模式，clearCache方法无操作");
    }

    /**
     * 检查Token是否有效（由于不再使用缓存，此方法保留为兼容性方法）
     *
     * @return 始终返回false，因为不再缓存Token
     */
    public static boolean isTokenValid() {
        return false; // 由于不再缓存Token，始终返回false
    }

    /**
     * 获取当前缓存的Token（由于不再使用缓存，此方法保留为兼容性方法）
     *
     * @return 始终返回null，因为不再缓存Token
     */
    public static String getCurrentToken() {
        return null; // 由于不再缓存Token，始终返回null
    }

    /**
     * 获取缓存信息（由于不再使用缓存，此方法保留为兼容性方法）
     *
     * @return 空的缓存信息
     */
    public static Map<String, String> getCacheInfo() {
        Map<String, String> info = new java.util.HashMap<>();
        info.put("cacheMode", "disabled");
        info.put("description", "WeaverClient已改为动态生成密钥模式，不使用缓存");
        return info;
    }

    /**
     * 格式化密钥，移除换行符和空格
     *
     * @param key 原始密钥字符串
     * @return 格式化后的密钥
     */
    private static String formatKey(String key) {
        if (StrUtil.isBlank(key)) {
            return "";
        }
        return key.replace("\n", "").replace(" ", "");
    }

    /**
     * 获取ecology系统地址
     *
     * @return ecology系统地址
     * @throws BusinessException 配置未初始化异常
     */
    private static String getServerUrl() {
        if (weaverConfig == null) {
            throw new BusinessException("WeaverConfig配置未初始化，请确保Spring容器已启动");
        }
        String serverUrl = weaverConfig.getServerUrl();
        if (StrUtil.isBlank(serverUrl)) {
            throw new BusinessException("泛微服务器地址未配置，请在配置文件中设置weaver.serverUrl");
        }
        return serverUrl;
    }

    /**
     * 生成RSA密钥对
     *
     * @return 密钥对
     * @throws BusinessException 密钥生成异常
     */
    private static KeyPair generateRSAKeyPair() {
        try {
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(RSA_ALGORITHM);
            keyPairGenerator.initialize(RSA_KEY_SIZE);
            return keyPairGenerator.generateKeyPair();
        } catch (Exception e) {
            log.error("生成RSA密钥对失败", e);
            throw new BusinessException("生成RSA密钥对失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用RSA公钥加密数据
     *
     * @param data            待加密的数据
     * @param publicKeyBase64 Base64格式的公钥
     * @return Base64编码的加密结果
     * @throws BusinessException 加密异常
     */
    private static String encryptWithRSA(String data, String publicKeyBase64) {
        if (StrUtil.isBlank(data) || StrUtil.isBlank(publicKeyBase64)) {
            throw new BusinessException("加密数据和公钥不能为空");
        }

        try {
            // 将Base64公钥转换为PublicKey对象
            byte[] keyBytes = Base64.decode(publicKeyBase64);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
            PublicKey publicKey = keyFactory.generatePublic(keySpec);

            // 使用公钥加密
            Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            byte[] encryptedBytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));

            return Base64.encode(encryptedBytes);
        } catch (Exception e) {
            log.error("RSA加密失败", e);
            throw new BusinessException("RSA加密失败: " + e.getMessage(), e);
        }
    }
} 