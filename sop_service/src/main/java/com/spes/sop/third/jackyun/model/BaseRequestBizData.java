/*
 * Copyright(C) 2024 Spes Co., Ltd. All rights reserved.
 */

package com.spes.sop.third.jackyun.model;

import lombok.Data;

/**
 * 吉客云开放平台业务请求数据基类
 * 所有业务请求数据都应继承此类
 *
 * <AUTHOR>
 */
@Data
public abstract class BaseRequestBizData {

    /**
     * 请求标识，用于链路追踪
     */
    private String requestId;

    /**
     * 请求时间戳
     */
    private Long timestamp;

    /**
     * 构造函数，设置默认值
     */
    protected BaseRequestBizData() {
        this.timestamp = System.currentTimeMillis();
        this.requestId = generateRequestId();
    }

    /**
     * 生成请求ID
     *
     * @return 请求ID
     */
    private String generateRequestId() {
        return "REQ_" + System.currentTimeMillis() + "_" +
                Thread.currentThread().getId();
    }
} 