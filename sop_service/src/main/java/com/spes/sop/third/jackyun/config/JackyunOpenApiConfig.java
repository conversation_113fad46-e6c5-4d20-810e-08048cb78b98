/*
 * Copyright(C) 2024 Spes Co., Ltd. All rights reserved.
 */

package com.spes.sop.third.jackyun.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 吉客云开放平台配置类
 * 从配置文件中读取相关配置参数
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "jackyun")
public class JackyunOpenApiConfig {

    /**
     * 服务器地址（对应yml中的server-url）
     */
    private String serverUrl;

    /**
     * API版本（对应yml中的api-version）
     */
    private String apiVersion;

    /**
     * 应用Key（对应yml中的app-key）
     */
    private String appKey;

    /**
     * 应用Secret（对应yml中的app-secret）
     */
    private String appSecret;

    /**
     * 连接超时时间（毫秒）
     */
    private Integer connectTimeout;

    /**
     * 读取超时时间（毫秒）
     */
    private Integer readTimeout;

    /**
     * 字符编码
     */
    private String encoding;

    /**
     * 是否启用调试模式
     */
    private Boolean debugEnabled;

    /**
     * 重试次数
     */
    private Integer retryCount;
} 