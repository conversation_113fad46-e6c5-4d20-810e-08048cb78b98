/*
 * Copyright(C) 2024 Spes Co., Ltd. All rights reserved.
 */

package com.spes.sop.third.weaver.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 泛微ecology系统配置类
 * 从配置文件中读取泛微ecology系统相关配置参数
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "weaver")
public class WeaverConfig {

    /**
     * 泛微ecology系统地址
     */
    private String serverUrl;

    /**
     * 应用ID（APPID）
     */
    private String appId;

    /**
     * 默认用户ID
     */
    private String defaultUserId;

    /**
     * Token有效期（秒）
     */
    private Integer tokenExpireTime = 600;

    /**
     * 连接超时时间（毫秒）
     */
    private Integer connectTimeout = 10000;

    /**
     * 读取超时时间（毫秒）
     */
    private Integer readTimeout = 30000;

    /**
     * 是否启用缓存
     */
    private Boolean enableCache = true;
} 