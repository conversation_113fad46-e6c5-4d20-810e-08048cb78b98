package com.spes.sop.third.weaver.model.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataPayload {
    private String currentNodeId;
    private String currentNodeName;
    private MainTableInfo workflowMainTableInfo;
    private List<DetailTablePayload> workflowDetailTableInfos;
}

