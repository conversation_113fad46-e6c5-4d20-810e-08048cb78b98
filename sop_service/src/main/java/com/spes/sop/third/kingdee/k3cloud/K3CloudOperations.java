package com.spes.sop.third.kingdee.k3cloud;


import com.spes.sop.third.kingdee.model.request.BillQuery;
import com.spes.sop.third.kingdee.model.request.BillSave;
import com.spes.sop.third.kingdee.model.request.BillSubmit;

import java.util.List;

public interface K3CloudOperations {

    <T> T executeBillQuery(List<BillQuery> params, Class<T> type);

    <T> List<T> executeBillQuery(BillQuery billQuery, Class<T> type);

    <T> T executeBillSave(BillSave billSave, Class<T> type);

    <T, N> T executeBillSubmit(BillSubmit<N> billSubmit, Class<T> type);

}
