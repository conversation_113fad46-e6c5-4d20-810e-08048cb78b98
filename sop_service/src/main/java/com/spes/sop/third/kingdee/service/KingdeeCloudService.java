package com.spes.sop.third.kingdee.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.spes.sop.third.kingdee.client.K3CloudTemplate;
import com.spes.sop.third.kingdee.config.KingdeeConfig;
import com.spes.sop.third.kingdee.model.base.*;
import com.spes.sop.third.kingdee.model.business.convert.BusinessKingdeeConvert;
import com.spes.sop.third.kingdee.model.business.entity.KingdeeCustomerBO;
import com.spes.sop.third.kingdee.model.business.request.MaterialSaveReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

@Slf4j
@Service
public class KingdeeCloudService {

    private final Authentication auth;
    private final K3CloudTemplate template;

    /**
     * 金蝶物料
     */
    private static final String MATERIAL_FORM_ID = "BD_MATERIAL";

    public KingdeeCloudService(KingdeeConfig kingdeeConfig) {
        this.auth = new Authentication(kingdeeConfig.getAcctId(),
                kingdeeConfig.getUsername(), kingdeeConfig.getPassword());
        this.template = new K3CloudTemplate(kingdeeConfig.getServerUrl(), auth);
    }

    /**
     * 保存金蝶物料
     *
     * @param req 物料保存请求
     * <AUTHOR>
     */
    public RequestResult saveMaterial(MaterialSaveReq req) {
        if (ObjectUtil.isNull(req)) {
            return RequestResult.builder().success(false).errorMessage("请求参数为空").build();
        }
        log.info("开始保存金蝶物料，请求参数: {}", JSONUtil.toJsonStr(req));
        BillSave billSave = new BillSave(MATERIAL_FORM_ID);
        billSave.setModel(BusinessKingdeeConvert.buildKingdeeMaterialPayload(req));

        KingdeeResult result = template.executeBillSave(billSave, KingdeeResult.class);

        if (result != null && result.getResult() != null && result.getResult().getResponseStatus() != null) {
            ResultStatus status = result.getResult().getResponseStatus();
            if (Boolean.TRUE.equals(status.getIsSuccess())) {
                log.info("金蝶物料保存成功，物料编号: {}", result.getResult().getNumber());
                return RequestResult.builder().success(true).build();
            } else {

                log.error("金蝶物料保存失败，错误码: {}, 错误信息: {}",
                        status.getErrorCode(),
                        status.getErrors() != null ? JSONUtil.toJsonStr(status.getErrors()) : "无错误详情");
                return RequestResult.builder()
                        .success(false)
                        .errorMessage(status.getErrors().get(0).getMessage()).build();
            }
        } else {
            log.warn("金蝶物料保存响应结果为空或格式异常");
            return RequestResult.builder().success(false).errorMessage("金蝶物料保存响应结果为空或格式异常").build();
        }
    }

    public List<KingdeeCustomerBO> getCustomerInfo(String name) {
        // 查询字段
        String[] fieldKeys = {
                "FNumber", "FName"
        };
        // 过滤条件, 具体格式可以参考金蝶文档, 比如:"FBOOKID=100453"
        String filter = "FName='" + name + "'";
        BillQuery query = new BillQuery("bd_customer", StringUtils.arrayToDelimitedString(fieldKeys, ","), filter);
        query.setStartRow(0);
        // 0代表无限制
        query.setLimit(100);
        List<KingdeeCustomerBO> dataList = template.executeBillQuery(query, KingdeeCustomerBO.class);
        log.info("获取客户信息 = {}", JSONUtil.toJsonStr(dataList));
        return dataList;
    }

    /**
     * 提交金蝶单据
     *
     * @param code 单据编号
     * <AUTHOR>
     */
    public RequestResult submitBill(String code) {
        if (!StringUtils.hasText(code)) {
            log.warn("单据编号为空，跳过提交操作");
            return RequestResult.builder().success(false).errorMessage("单据编号为空").build();
        }

        log.info("开始提交金蝶单据，单据编号: {}", code);
        BillSubmit<String> billSubmit = new BillSubmit<>(MATERIAL_FORM_ID);
        billSubmit.setNumbers(Lists.newArrayList(code));
        KingdeeResult result = template.executeBillSubmit(billSubmit, KingdeeResult.class);

        if (result != null && result.getResult() != null && result.getResult().getResponseStatus() != null) {
            ResultStatus status = result.getResult().getResponseStatus();
            if (Boolean.TRUE.equals(status.getIsSuccess())) {
                log.info("金蝶单据提交成功");
                return RequestResult.builder().success(true).build();
            }
            log.error("金蝶单据提交失败，错误码: {}, 错误信息: {}",
                    status.getErrorCode(),
                    status.getErrors() != null ? JSONUtil.toJsonStr(status.getErrors()) : "无错误详情");
            return RequestResult.builder().success(false).errorMessage(status.getErrors().get(0).getMessage()).build();
        }
        return RequestResult.builder().success(false).errorMessage("金蝶单据提交响应结果为空或格式异常").build();
    }

    /**
     * 审核金蝶单据
     *
     * @param code 单据编号
     * <AUTHOR>
     */
    public RequestResult auditBill(String code) {
        if (!StringUtils.hasText(code)) {
            log.warn("单据编号为空，跳过审核操作");
            return RequestResult.builder().success(false).errorMessage("单据编号为空").build();
        }

        log.info("开始审核金蝶单据，单据编号: {}", code);
        BillAudit<String> billAudit = new BillAudit<>(MATERIAL_FORM_ID);
        billAudit.setNumbers(Lists.newArrayList(code));
        KingdeeResult result = template.executeBillAudit(billAudit, KingdeeResult.class);
        if (result != null && result.getResult() != null && result.getResult().getResponseStatus() != null) {
            ResultStatus status = result.getResult().getResponseStatus();
            if (Boolean.TRUE.equals(status.getIsSuccess())) {
                log.info("金蝶单据审核成功");
                return
                        RequestResult.builder().success(true).build();
            }
            log.error("金蝶单据审核失败，错误码: {}, 错误信息: {}",
                    status.getErrorCode(),
                    status.getErrors() != null ? JSONUtil.toJsonStr(status.getErrors()) : "无错误详情");
            return RequestResult.builder().success(false).errorMessage(status.getErrors().get(0).getMessage()).build();
        }
        return RequestResult.builder().success(false).errorMessage("金蝶单据审核响应结果为空或格式异常").build();
    }

    /**
     * 金蝶返回结果
     * <AUTHOR>
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class KingdeeResult {
        @JsonProperty("Result")
        private Result result;
    }

    /**
     * 金蝶返回结果详情
     *
     * <AUTHOR>
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Result {
        @JsonProperty("ResponseStatus")
        private ResultStatus responseStatus;

        @JsonProperty("Id")
        private String id;

        @JsonProperty("Number")
        private String number;

        @JsonProperty("NeedReturnData")
        private java.util.List<Object> needReturnData;
    }

    /**
     * 金蝶响应状态
     *
     * <AUTHOR>
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ResultStatus {
        @JsonProperty("ErrorCode")
        private Integer errorCode;

        @JsonProperty("IsSuccess")
        private Boolean isSuccess;

        @JsonProperty("Errors")
        private List<KingdeeError> errors;

        @JsonProperty("SuccessEntitys")
        private List<Object> successEntitys;

        @JsonProperty("SuccessMessages")
        private List<String> successMessages;

        @JsonProperty("MsgCode")
        private Integer msgCode;
    }

    /**
     * 金蝶错误信息
     *
     * <AUTHOR>
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class KingdeeError {
        @JsonProperty("FieldName")
        private String fieldName;

        @JsonProperty("Message")
        private String message;

        @JsonProperty("DIndex")
        private Integer dIndex;
    }
} 