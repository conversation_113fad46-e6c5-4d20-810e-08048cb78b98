package com.spes.sop.third.kingdee.k3cloud;


import com.spes.sop.third.kingdee.model.entity.Authentication;
import com.spes.sop.third.kingdee.model.request.RequestService;

import java.util.List;

/**
 * k3cloud接口服务
 *
 * @param <P> 参数
 * @param <R> 返回值
 * <AUTHOR>
 */
public interface ApiService<P, R> {

    String getEndPoint();

    RequestService getService();

    Authentication getAuth();

    R execute(List<P> params);

}
