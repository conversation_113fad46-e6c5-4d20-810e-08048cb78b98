package com.spes.sop.third.weaver.model.request.value;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 工作流请求表字段DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowRequestTableField {

    /**
     * 字段ID
     */
    private String fieldId;

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 字段显示名称
     */
    private String fieldShowName;

    /**
     * 字段显示值
     */
    private String fieldShowValue;

    /**
     * 字段类型
     */
    private String fieldType;

    /**
     * 字段值
     */
    private String fieldValue;

    public WorkflowRequestTableField(String fieldName, String fieldValue) {
        this.fieldName = fieldName;
        this.fieldValue = fieldValue;
    }
}