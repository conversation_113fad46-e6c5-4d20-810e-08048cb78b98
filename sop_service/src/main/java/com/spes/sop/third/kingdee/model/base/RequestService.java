package com.spes.sop.third.kingdee.model.base;

public enum RequestService {

    SERVICE_AUTH("Kingdee.BOS.WebApi.ServicesStub.AuthService.ValidateUser.common.kdsvc", "登陆验证服务"),
    SERVICE_SAVE("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Save.common.kdsvc", "保存表单数据服务"),
    SERVICE_AUDIT("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Audit.common.kdsvc", "审核表单数据服务"),
    SERVICE_SUBMIT("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Submit.common.kdsvc", "提交表单数据服务"),
    SERVICE_EXECUTE_BILL_QUERY("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery.common.kdsvc",
            "表单数据查询服务");

    private String name;

    private String comment;

    RequestService(String name, String comment) {
        this.name = name;
        this.comment = comment;
    }

    public String getName() {
        return name;
    }

    public String getComment() {
        return comment;
    }

} 