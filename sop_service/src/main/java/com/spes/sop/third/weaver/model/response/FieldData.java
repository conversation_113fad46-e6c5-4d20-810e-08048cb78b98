package com.spes.sop.third.weaver.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FieldData {
    /**
     * 字段类型
     */
    private String fieldHtmlType;
    /**
     * 字段名
     */
    private String fieldName;
    /**
     * 字段值
     */
    private String fieldValue;
    /**
     * 展示字段
     */
    private String fieldShowValue;
    /**
     * 枚举值
     */
    private List<String> selectvalues;
    /**
     * 枚举显示值
     */
    private List<String> selectnames;
}

