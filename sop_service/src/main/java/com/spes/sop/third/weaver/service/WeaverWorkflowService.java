package com.spes.sop.third.weaver.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aliyuncs.utils.StringUtils;
import com.spes.sop.common.exception.BusinessException;
import com.spes.sop.common.util.JsonUtils;
import com.spes.sop.third.weaver.client.WeaverClient;
import com.spes.sop.third.weaver.convert.WorkflowDetailConverter;
import com.spes.sop.third.weaver.model.response.UserListPayload;
import com.spes.sop.third.weaver.model.response.UserListResponse;
import com.spes.sop.third.weaver.model.response.WorkflowResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 泛微工作流服务类
 * 提供泛微系统工作流相关的业务方法
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class WeaverWorkflowService {

    /**
     * 获取工作流详情并转换为结构化对象
     *
     * @param requestId 请求ID
     * @param userId 用户ID
     * @return 工作流详情响应对象
     * @throws RuntimeException 调用或转换异常
     */
    private WorkflowResult getWorkflowDetail(String requestId, String userId) {
        if (requestId == null || requestId.trim().isEmpty()) {
            throw new IllegalArgumentException("请求ID不能为空");
        }

        try {
            log.info("开始获取工作流详情，请求ID: {}, 用户ID: {}", requestId, userId);

            // 获取原始JSON数据
            String jsonResponse = getWorkflowRequestDetail(requestId, userId);

            if (StringUtils.isEmpty(jsonResponse)) {
                log.warn("获取的工作流详情数据为空，请求ID: {}, 用户ID: {}", requestId, userId);
                return null;
            }
            log.info("获取工作流详情成功，请求ID: {}, 用户ID: {}", requestId, userId);
            log.info("获取工作流详情响应: {}", jsonResponse);

            // 转换为结构化对象
            WorkflowResult detailResponse = WorkflowDetailConverter.fromJson(jsonResponse);

            if (ObjectUtil.isNotNull(detailResponse)) {
                log.info("工作流详情转换成功，请求ID: {}, 用户ID: {}, 工作流名称: {}",
                        requestId,
                        userId,
                        detailResponse.getWorkflowMainTableInfo().get("requestname"));
            } else {
                log.warn("工作流详情转换失败或数据为空，请求ID: {}, 用户ID: {}", requestId, userId);
            }

            return detailResponse;

        } catch (Exception e) {
            log.error("获取工作流详情失败，请求ID: {}, 用户ID: {}", requestId, userId, e);
            throw new BusinessException("获取工作流详情失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取工作流详情数据（仅返回data部分）
     *
     * @param requestId 请求ID
     * @param userId 用户ID
     * @return 工作流详情数据对象
     * @throws RuntimeException 调用或转换异常
     */
    public WorkflowResult getWorkflowDetailData(String requestId, String userId) {
        WorkflowResult response = getWorkflowDetail(requestId, userId);
        if (ObjectUtil.isNotNull(response)) {
            log.info("获取工作流详情数据成功: {}", JSONUtil.toJsonStr(response));
            return response;
        }
        return null;
    }

    /**
     * 获取工作流详情数据（使用默认用户ID）
     *
     * @param requestId 请求ID
     * @return 工作流详情数据对象
     * @throws RuntimeException 调用或转换异常
     */
    public WorkflowResult getWorkflowDetailData(String requestId) {
        return getWorkflowDetailData(requestId, null); // 使用null会让WeaverClient使用默认用户ID
    }

    /**
     * 创建工作流请求
     *
     * @param params 请求参数
     * @param userId 用户ID
     * @return 请求ID
     */
    public String createWorkflowRequest(Map<String, Object> params, String userId) {
        try {
            log.info("创建工作流请求，用户ID: {}", userId);

            String response;
            if (userId != null) {
                response = WeaverClient.post(
                        WeaverClient.API_CREATE_WORKFLOW_REQUEST, params, userId
                );
            } else {
                response = WeaverClient.post(
                        WeaverClient.API_CREATE_WORKFLOW_REQUEST, params
                );
            }
            if (StringUtils.isEmpty(response)) {
                log.error("创建工作流请求失败，响应为空，用户ID: {}", userId);
                return null;
            }
            JSONObject jsonObject = JSONUtil.parseObj(response);
            if (!"SUCCESS".equals(jsonObject.getStr("code"))) {
                log.error("创建工作流请求失败，用户ID: {}, 响应码: {}, 错误信息：{}", userId, jsonObject.getStr("code"), jsonObject);
                return null;
            }
            JSONObject data = jsonObject.getJSONObject("data");
            if (data == null) {
                log.error("创建工作流请求失败，响应数据为空，用户ID: {}", userId);
                return null;
            }

            String requestId = data.getStr("requestid");
            log.info("创建工作流请求成功，用户ID: {}, 请求ID: {}", userId, requestId);
            return requestId;

        } catch (Exception e) {
            log.error("创建工作流请求失败，用户ID: {}", userId, e);
            throw new BusinessException("创建工作流请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建工作流请求（使用默认用户ID）
     *
     * @param params 请求参数
     * @return 请求ID
     */
    public String createWorkflowRequest(Map<String, Object> params) {
        return createWorkflowRequest(params, null); // 使用null会让WeaverClient使用默认用户ID
    }

    /**
     * 获取流程详细信息
     *
     * @param requestId 请求ID
     * @param userId 用户ID
     * @return 流程详细信息JSON字符串
     * @throws RuntimeException 调用异常
     */
    private String getWorkflowRequestDetail(String requestId, String userId) {
        if (requestId == null || requestId.trim().isEmpty()) {
            throw new IllegalArgumentException("请求ID不能为空");
        }

        try {
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("requestId", requestId);

            log.info("获取流程详细信息，请求ID: {}, 用户ID: {}", requestId, userId);

            String response;
            if (userId != null) {
                response = WeaverClient.get(
                        WeaverClient.API_GET_WORKFLOW_REQUEST,
                        params,
                        userId
                );
            } else {
                response = WeaverClient.get(
                        WeaverClient.API_GET_WORKFLOW_REQUEST,
                        params
                );
            }

            log.info("获取流程详细信息成功，请求ID: {}, 用户ID: {}", requestId, userId);
            return response;

        } catch (Exception e) {
            log.error("获取流程详细信息失败，请求ID: {}, 用户ID: {}", requestId, userId, e);
            throw new BusinessException("获取流程详细信息失败: " + e.getMessage(), e);
        }
    }

    public UserListPayload.UserInfo getWeaverUser(String workCode) {
        if (StringUtils.isEmpty(workCode)) {
            throw new IllegalArgumentException("workCode不能为空");
        }

        try {
            // 构建请求参数
            Map<String, Object> data = new HashMap<>();
            data.put("pagesize", 1000);
            data.put("workcode", workCode);

            Map<String, Object> params = new HashMap<>();
            params.put("params", JsonUtils.toJsonString(data));

            log.info("获取流程详细信息");

            String response = WeaverClient.post(
                    WeaverClient.USER_SEARCH_CONDITION,
                    params);
            if (StringUtils.isEmpty(response)) {
                log.error("获取流程详细信息失败，响应为空");
                return null;
            }
            UserListResponse bean = JSONUtil.toBean(response, UserListResponse.class);
            if (!"1".equals(bean.getCode()) || bean.getData() == null || CollUtil.isEmpty(bean.getData().getDataList())) {
                log.error("获取流程详细信息失败，响应码: {}", bean.getCode());
                return null;
            }
            log.info("获取流程详细信息成功，响应: {}", bean.getData());
            return bean.getData().getDataList().get(0);
        } catch (Exception e) {
            throw new BusinessException("获取流程详细信息失败: " + e.getMessage(), e);
        }
    }
} 