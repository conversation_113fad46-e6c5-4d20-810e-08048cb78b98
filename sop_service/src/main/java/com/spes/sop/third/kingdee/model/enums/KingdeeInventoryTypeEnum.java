package com.spes.sop.third.kingdee.model.enums;

import lombok.Getter;

public enum KingdeeInventoryTypeEnum {

    RAW_MATERIAL("CHLB01_SYS", "原材料"),
    SEMI_PRODUCT("CHLB03_SYS", "自制半成品"),
    OUTSOURCING("CHLB04_SYS", "委外半成品"),
    PRODUCT("CHLB05_SYS", "产成品"),
    SERVICE("CHLB06_SYS", "服务"),
    ASSET("CHLB07_SYS", "资产"),
    PACKAGING_MATERIAL("CHLB08_SYS", "包装材料");
    @Getter
    private final String code;
    @Getter
    private final String desc;

    KingdeeInventoryTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static KingdeeInventoryTypeEnum getByDesc(String desc) {
        for (KingdeeInventoryTypeEnum value : values()) {
            if (value.getDesc().equals(desc)) {
                return value;
            }
        }
        return null;
    }
}
