package com.spes.sop.user.service.user.converter;

import com.spes.sop.common.enums.UserRoleEnum;
import com.spes.sop.common.util.IdWorker;
import com.spes.sop.user.mapper.model.entity.UserDO;
import com.spes.sop.user.mapper.model.query.UserDOGetQuery;
import com.spes.sop.user.mapper.model.query.UserDOListQuery;
import com.spes.sop.user.mapper.model.req.UserAddReq;
import com.spes.sop.user.mapper.model.req.UserDeleteReq;
import com.spes.sop.user.mapper.model.req.UserUpdateReq;
import com.spes.sop.user.service.user.model.bo.UserBO;
import com.spes.sop.user.service.user.model.command.UserCreateCommand;
import com.spes.sop.user.service.user.model.command.UserDeleteCommand;
import com.spes.sop.user.service.user.model.command.UserUpdateCommand;
import com.spes.sop.user.service.user.model.query.UserGetQuery;
import com.spes.sop.user.service.user.model.query.UserPageQuery;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用户转换器
 * 负责Service层模型与Mapper层模型之间的转换
 *
 * <AUTHOR>
 */
public class UserConverter {


    /**
     * UserDO转换为UserBO
     *
     * @param userDO 用户数据对象
     * @return 用户业务对象
     */
    public static UserBO toUserBO(UserDO userDO) {
        if (userDO == null) {
            return null;
        }

        return UserBO.builder()
                .id(userDO.getId())
                .employeeId(userDO.getEmployeeId())
                .username(userDO.getUsername())
                .role(UserRoleEnum.getByName(userDO.getRole()))
                .department(userDO.getDepartment())
                .weaverId(userDO.getWeaverId())
                .password(userDO.getPassword())
                .creatorId(userDO.getCreatorId())
                .updaterId(userDO.getUpdaterId())
                .deleted(userDO.getDeleted())
                .createTime(userDO.getCreateTime())
                .updateTime(userDO.getUpdateTime())
                .build();
    }

    /**
     * UserDO列表转换为UserBO列表
     *
     * @param userDOList 用户数据对象列表
     * @return 用户业务对象列表
     */
    public static List<UserBO> toUserBOList(List<UserDO> userDOList) {
        if (CollectionUtils.isEmpty(userDOList)) {
            return Collections.emptyList();
        }

        return userDOList.stream()
                .filter(Objects::nonNull)
                .map(UserConverter::toUserBO)
                .collect(Collectors.toList());
    }

    /**
     * UserPageQuery转换为UserDOListQuery
     *
     * @param pageQuery 分页查询对象
     * @return Mapper层分页查询对象
     */
    public static UserDOListQuery toUserDOListQuery(UserPageQuery pageQuery) {
        if (pageQuery == null) {
            return null;
        }

        return UserDOListQuery.builder()
                .ids(pageQuery.getIds())
                .username(pageQuery.getUsername())
                .roles(pageQuery.getRoles())
                .employeeIds(pageQuery.getEmployeeIds())
                .orderBy(pageQuery.getOrderBy())
                .orderDirection(pageQuery.getOrderDirection())
                .offset(calculateOffset(pageQuery.getPageNum(), pageQuery.getPageSize()))
                .limit(pageQuery.getPageSize() != null ? pageQuery.getPageSize().longValue() : null)
                .build();
    }

    /**
     * UserGetQuery转换为UserDOGetQuery
     *
     * @param getQuery 单个查询对象
     * @return Mapper层单个查询对象
     */
    public static UserDOGetQuery toUserDOGetQuery(UserGetQuery getQuery) {
        if (getQuery == null) {
            return null;
        }

        return UserDOGetQuery.builder()
                .id(getQuery.getId())
                .employeeId(getQuery.getEmployeeId())
                .build();
    }

    /**
     * UserCreateCommand转换为UserAddReq
     *
     * @param createCommand   创建命令对象
     * @param encodedPassword 加密后的密码
     * @return Mapper层新增请求对象
     */
    public static UserAddReq toUserAddReq(UserCreateCommand createCommand, String encodedPassword) {
        if (createCommand == null) {
            return null;
        }

        return UserAddReq.builder()
                .id(IdWorker.generateId())
                .employeeId(createCommand.getEmployeeId())
                .username(createCommand.getUsername())
                .role(createCommand.getRole())
                .department(createCommand.getDepartment())
                .weaverId(createCommand.getWeaverId())
                .password(encodedPassword)
                .operatorId(createCommand.getOperatorId())
                .build();
    }

    /**
     * UserUpdateCommand转换为UserUpdateReq
     *
     * @param updateCommand 更新命令对象
     * @return Mapper层更新请求对象
     */
    public static UserUpdateReq toUserUpdateReq(UserUpdateCommand updateCommand) {
        if (updateCommand == null) {
            return null;
        }

        return UserUpdateReq.builder()
                .id(updateCommand.getId())
                .employeeId(updateCommand.getEmployeeId())
                .username(updateCommand.getUsername())
                .role(updateCommand.getRole())
                .weaverId(updateCommand.getWeaverId())
                .operatorId(updateCommand.getOperatorId())
                .build();
    }

    /**
     * 密码更新转换为UserUpdateReq
     *
     * @param userId          用户ID
     * @param encodedPassword 加密后的密码
     * @param operatorId      操作人ID
     * @return Mapper层更新请求对象
     */
    public static UserUpdateReq toPasswordUpdateReq(Long userId, String encodedPassword, Long operatorId) {
        return UserUpdateReq.builder()
                .id(userId)
                .password(encodedPassword)
                .operatorId(operatorId)
                .build();
    }

    /**
     * UserDeleteCommand转换为UserDeleteReq列表
     *
     * @param deleteCommand 删除命令对象
     * @return Mapper层删除请求对象列表
     */
    public static List<UserDeleteReq> toUserDeleteReqList(UserDeleteCommand deleteCommand) {
        if (deleteCommand == null || CollectionUtils.isEmpty(deleteCommand.getIds())) {
            return Collections.emptyList();
        }

        return deleteCommand.getIds().stream()
                .filter(Objects::nonNull)
                .map(userId -> UserDeleteReq.builder()
                        .userId(userId)
                        .operatorId(deleteCommand.getOperatorId())
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 计算分页偏移量
     *
     * @param pageNum  页码（从1开始）
     * @param pageSize 每页大小
     * @return 偏移量
     */
    private static Long calculateOffset(Integer pageNum, Integer pageSize) {
        if (pageNum == null || pageSize == null || pageNum < 1) {
            return null;
        }
        return (long) (pageNum - 1) * pageSize;
    }
} 