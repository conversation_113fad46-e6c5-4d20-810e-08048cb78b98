package com.spes.sop.user.service.user.impl;

import com.spes.sop.common.exception.BusinessException;
import com.spes.sop.user.mapper.UserMapper;
import com.spes.sop.user.mapper.model.entity.UserDO;
import com.spes.sop.user.mapper.model.query.UserDOGetQuery;
import com.spes.sop.user.mapper.model.query.UserDOListQuery;
import com.spes.sop.user.mapper.model.req.UserAddReq;
import com.spes.sop.user.mapper.model.req.UserDeleteReq;
import com.spes.sop.user.mapper.model.req.UserUpdateReq;
import com.spes.sop.user.service.user.UserService;
import com.spes.sop.user.service.user.converter.UserConverter;
import com.spes.sop.user.service.user.model.bo.UserBO;
import com.spes.sop.user.service.user.model.command.UserCreateCommand;
import com.spes.sop.user.service.user.model.command.UserDeleteCommand;
import com.spes.sop.user.service.user.model.command.UserPasswordUpdateCommand;
import com.spes.sop.user.service.user.model.command.UserUpdateCommand;
import com.spes.sop.user.service.user.model.query.UserGetQuery;
import com.spes.sop.user.service.user.model.query.UserPageQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用户信息服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    @Override
    public List<UserBO> getUserPage(UserPageQuery pageQuery) {
        Objects.requireNonNull(pageQuery, "分页查询条件不能为空");

        log.debug("分页查询用户列表，查询条件：{}", pageQuery);

        try {
            UserDOListQuery listQuery = UserConverter.toUserDOListQuery(pageQuery);
            List<UserDO> userDOList = userMapper.list(listQuery);

            List<UserBO> result = UserConverter.toUserBOList(userDOList);

            log.debug("分页查询用户列表成功，返回{}条记录", result.size());
            return result;

        } catch (Exception e) {
            log.error("分页查询用户列表失败，查询条件：{}", pageQuery, e);
            throw new BusinessException("分页查询用户列表失败", e);
        }
    }

    @Override
    public Long getUserCount(UserPageQuery pageQuery) {
        Objects.requireNonNull(pageQuery, "查询条件不能为空");

        log.debug("查询用户总数，查询条件：{}", pageQuery);

        try {
            UserDOListQuery listQuery = UserConverter.toUserDOListQuery(pageQuery);
            Long count = userMapper.count(listQuery);

            log.debug("查询用户总数成功，总数：{}", count);
            return count != null ? count : 0L;

        } catch (Exception e) {
            log.error("查询用户总数失败，查询条件：{}", pageQuery, e);
            throw new BusinessException("查询用户总数失败", e);
        }
    }

    @Override
    public UserBO getUser(UserGetQuery getQuery) {
        Objects.requireNonNull(getQuery, "查询条件不能为空");

        log.debug("根据条件查询用户，查询条件：{}", getQuery);

        try {
            UserDOGetQuery doGetQuery = UserConverter.toUserDOGetQuery(getQuery);
            UserDO userDO = userMapper.getUser(doGetQuery);

            UserBO result = UserConverter.toUserBO(userDO);

            log.debug("根据条件查询用户成功，用户：{}", result != null ? result.getUsername() : "null");
            return result;

        } catch (Exception e) {
            log.error("根据条件查询用户失败，查询条件：{}", getQuery, e);
            throw new BusinessException("根据条件查询用户失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserBO createUser(UserCreateCommand createCommand) {

        log.info("创建用户，用户名：{}，工号：{}", createCommand.getUsername(), createCommand.getEmployeeId());

        try {
            // 检查工号是否已存在
            if (isEmployeeIdExists(createCommand.getEmployeeId())) {
                throw new IllegalArgumentException("工号已存在：" + createCommand.getEmployeeId());
            }

            // 加密密码
            String encodedPassword = passwordEncoder.encode(createCommand.getPassword());

            // 转换为Mapper层请求对象
            UserAddReq addReq = UserConverter.toUserAddReq(createCommand, encodedPassword);

            // 执行创建
            int result = userMapper.insert(addReq);
            if (result <= 0) {
                throw new BusinessException("创建用户失败，影响行数：" + result);
            }

            // 查询创建的用户
            UserBO createdUser = getUser(UserGetQuery.builder()
                    .employeeId(createCommand.getEmployeeId()).build());

            log.info("创建用户成功，用户ID：{}，用户名：{}",
                    createdUser != null ? createdUser.getId() : "null", createCommand.getUsername());

            return createdUser;

        } catch (Exception e) {
            log.error("创建用户失败，用户名：{}", createCommand.getUsername(), e);
            throw new BusinessException("创建用户失败：" + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int createUserBatch(List<UserCreateCommand> createCommands) {
        if (CollectionUtils.isEmpty(createCommands)) {
            log.warn("批量创建用户命令列表为空");
            return 0;
        }

        log.info("批量创建用户，数量：{}", createCommands.size());

        try {
            // 转换为Mapper层请求对象列表
            List<UserAddReq> addReqList = createCommands.stream()
                    .filter(Objects::nonNull)
                    .map(command -> {
                        // 加密密码
                        String encodedPassword = passwordEncoder.encode(command.getPassword());
                        return UserConverter.toUserAddReq(command, encodedPassword);
                    })
                    .collect(Collectors.toList());

            // 执行批量创建
            int result = userMapper.insertBatch(addReqList);

            log.info("批量创建用户成功，创建数量：{}", result);
            return result;

        } catch (Exception e) {
            log.error("批量创建用户失败", e);
            throw new BusinessException("批量创建用户失败：" + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUser(UserUpdateCommand updateCommand) {
        Objects.requireNonNull(updateCommand, "更新命令不能为空");
        Objects.requireNonNull(updateCommand.getId(), "用户ID不能为空");

        log.info("更新用户信息，用户ID：{}", updateCommand.getId());

        try {
            // 检查用户是否存在
            UserBO existingUser = getUser(UserGetQuery.builder().id(updateCommand.getId()).build());
            if (existingUser == null) {
                throw new IllegalArgumentException("用户不存在，用户ID：" + updateCommand.getId());
            }

            // 检查工号是否重复（排除当前用户）
            if (StringUtils.hasText(updateCommand.getEmployeeId()) &&
                    isEmployeeIdExists(updateCommand.getEmployeeId(), updateCommand.getId())) {
                throw new IllegalArgumentException("工号已存在：" + updateCommand.getEmployeeId());
            }

            // 转换为Mapper层请求对象
            UserUpdateReq updateReq = UserConverter.toUserUpdateReq(updateCommand);

            // 执行更新
            int result = userMapper.updateById(updateReq);
            boolean success = result > 0;

            log.info("更新用户信息{}，用户ID：{}", success ? "成功" : "失败", updateCommand.getId());
            return success;

        } catch (Exception e) {
            log.error("更新用户信息失败，用户ID：{}", updateCommand.getId(), e);
            throw new BusinessException("更新用户信息失败：" + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserPassword(UserPasswordUpdateCommand passwordUpdateCommand) {
        Objects.requireNonNull(passwordUpdateCommand, "密码更新命令不能为空");
        Objects.requireNonNull(passwordUpdateCommand.getUserId(), "用户ID不能为空");
        Objects.requireNonNull(passwordUpdateCommand.getNewPassword(), "新密码不能为空");

        log.info("更新用户密码，用户ID：{}", passwordUpdateCommand.getUserId());

        try {
            // 检查用户是否存在
            UserBO existingUser = getUser(UserGetQuery.builder().id(passwordUpdateCommand.getUserId()).build());
            if (existingUser == null) {
                throw new IllegalArgumentException("用户不存在，用户ID：" + passwordUpdateCommand.getUserId());
            }

            // 验证旧密码（如果提供了旧密码）
            if (StringUtils.hasText(passwordUpdateCommand.getOldPassword())) {
                if (!passwordEncoder.matches(passwordUpdateCommand.getOldPassword(), existingUser.getPassword())) {
                    throw new IllegalArgumentException("旧密码不正确");
                }
            }

            // 加密新密码
            String encodedNewPassword = passwordEncoder.encode(passwordUpdateCommand.getNewPassword());

            // 构建更新请求
            UserUpdateReq updateReq = UserConverter.toPasswordUpdateReq(
                    passwordUpdateCommand.getUserId(),
                    encodedNewPassword,
                    passwordUpdateCommand.getOperatorId());

            // 执行更新
            int result = userMapper.updateById(updateReq);
            boolean success = result > 0;

            log.info("更新用户密码{}，用户ID：{}", success ? "成功" : "失败", passwordUpdateCommand.getUserId());
            return success;

        } catch (Exception e) {
            log.error("更新用户密码失败，用户ID：{}", passwordUpdateCommand.getUserId(), e);
            throw new BusinessException("更新用户密码失败：" + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetUserPassword(Long userId, String newPassword, Long operatorId) {
        Objects.requireNonNull(userId, "用户ID不能为空");
        Objects.requireNonNull(newPassword, "新密码不能为空");

        log.info("重置用户密码，用户ID：{}", userId);

        try {
            // 检查用户是否存在
            UserBO existingUser = getUser(UserGetQuery.builder().id(userId).build());
            if (existingUser == null) {
                throw new IllegalArgumentException("用户不存在，用户ID：" + userId);
            }

            // 加密新密码
            String encodedNewPassword = passwordEncoder.encode(newPassword);

            // 构建更新请求
            UserUpdateReq updateReq = UserConverter.toPasswordUpdateReq(userId, encodedNewPassword, operatorId);

            // 执行更新
            int result = userMapper.updateById(updateReq);
            boolean success = result > 0;

            log.info("重置用户密码{}，用户ID：{}", success ? "成功" : "失败", userId);
            return success;

        } catch (Exception e) {
            log.error("重置用户密码失败，用户ID：{}", userId, e);
            throw new BusinessException("重置用户密码失败：" + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUser(UserDeleteCommand deleteCommand) {
        Objects.requireNonNull(deleteCommand, "删除命令不能为空");

        if (CollectionUtils.isEmpty(deleteCommand.getIds())) {
            log.warn("删除用户ID列表为空");
            return 0;
        }

        log.info("删除用户，用户ID列表：{}", deleteCommand.getIds());

        try {
            // 转换为Mapper层请求对象列表
            List<UserDeleteReq> deleteReqList = UserConverter.toUserDeleteReqList(deleteCommand);

            // 执行删除
            int totalDeleted = 0;
            for (UserDeleteReq deleteReq : deleteReqList) {
                int result = userMapper.delete(deleteReq);
                totalDeleted += result;
            }

            log.info("删除用户成功，删除数量：{}", totalDeleted);
            return totalDeleted;

        } catch (Exception e) {
            log.error("删除用户失败，用户ID列表：{}", deleteCommand.getIds(), e);
            throw new BusinessException("删除用户失败：" + e.getMessage(), e);
        }
    }


    @Override
    public boolean isEmployeeIdExists(String employeeId) {
        if (!StringUtils.hasText(employeeId)) {
            return false;
        }

        UserBO user = getUser(UserGetQuery.builder().employeeId(employeeId).build());
        return user != null;
    }


    @Override
    public boolean isEmployeeIdExists(String employeeId, Long excludeUserId) {
        if (!StringUtils.hasText(employeeId)) {
            return false;
        }

        UserBO user = getUser(UserGetQuery.builder().employeeId(employeeId).build());
        return user != null && !Objects.equals(user.getId(), excludeUserId);
    }

    @Override
    public boolean validateUserPassword(String employeeId, String rawPassword) {
        Objects.requireNonNull(employeeId, "工号 ID 不能为空");

        if (!StringUtils.hasText(rawPassword)) {
            return false;
        }

        log.debug("验证用户密码，工号ID：{}", employeeId);

        try {
            UserBO user = getUser(UserGetQuery.builder().employeeId(employeeId).build());
            if (user == null) {
                log.warn("用户不存在，工号ID：{}", employeeId);
                return false;
            }

            boolean matches = passwordEncoder.matches(rawPassword, user.getPassword());

            log.debug("验证用户密码{}，工号：{}", matches ? "成功" : "失败", employeeId);
            return matches;

        } catch (Exception e) {
            log.error("验证用户密码失败，工号：{}", employeeId, e);
            return false;
        }
    }
}
