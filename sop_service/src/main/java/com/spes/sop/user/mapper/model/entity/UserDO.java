package com.spes.sop.user.mapper.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 用户信息数据对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDO {


    /**
     * 用户ID
     */
    private Long id;

    /**
     * 工号
     */
    private String employeeId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 角色
     */
    private String role;

    /**
     * 密码
     */
    private String password;

    /**
     * 部门
     */
    private String department;

    /**
     * 织工ID
     */
    private Long weaverId;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 逻辑删除标识（0-未删除，1-已删除）
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
} 
