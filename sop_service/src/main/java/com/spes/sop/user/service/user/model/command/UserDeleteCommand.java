package com.spes.sop.user.service.user.model.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 用户删除命令对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDeleteCommand {

    /**
     * 用户ID列表
     */
    private List<Long> ids;

    /**
     * 操作人ID
     */
    private Long operatorId;
} 