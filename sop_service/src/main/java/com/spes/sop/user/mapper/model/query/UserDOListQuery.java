package com.spes.sop.user.mapper.model.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 用户查询参数封装对象 - 数据访问层
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDOListQuery {

    /**
     * 用户ID
     */
    private List<Long> ids;
    /**
     * 用户名（模糊查询）
     */
    private String username;

    /**
     * 角色
     */
    private List<String> roles;

    /**
     * 工号
     */
    private List<String> employeeIds;

    /**
     * 部门
     */
    private List<String> departments;

    /**
     * 排序字段
     */
    private String orderBy = "create_time";

    /**
     * 排序方向：ASC/DESC
     */
    private String orderDirection = "DESC";

    /**
     * 分页参数
     */
    private Long offset;
    private Long limit;
} 
