package com.spes.sop.user.service.user.model.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户密码更新命令对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserPasswordUpdateCommand {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 旧密码（明文，可选）
     */
    private String oldPassword;

    /**
     * 新密码（明文）
     */
    private String newPassword;

    /**
     * 操作人ID
     */
    private Long operatorId;
} 