package com.spes.sop.user.mapper.model.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserUpdateReq {
    /**
     * 用户ID
     */
    private Long id;
    /**
     * 工号
     */
    private String employeeId;
    /**
     * 用户名
     */
    private String username;
    /**
     * 角色
     */
    private String role;
    /**
     * 密码
     */
    private String password;
    /**
     * 部门
     */
    private String department;
    /**
     * 织工ID
     */
    private Long weaverId;
    /**
     * 操作人
     */
    private Long operatorId;
}
