package com.spes.sop.user.mapper;

import com.spes.sop.user.mapper.model.entity.UserDO;
import com.spes.sop.user.mapper.model.query.UserDOGetQuery;
import com.spes.sop.user.mapper.model.query.UserDOListQuery;
import com.spes.sop.user.mapper.model.req.UserAddReq;
import com.spes.sop.user.mapper.model.req.UserDeleteReq;
import com.spes.sop.user.mapper.model.req.UserUpdateReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户信息Mapper接口 - 分页批量设计
 */
@Mapper
public interface UserMapper {

    /**
     * 根据条件分页查询用户列表
     *
     * @param query 查询条件（支持批量条件）
     * @return 用户列表
     */
    List<UserDO> list(@Param("query") UserDOListQuery query);

    /**
     * 根据条件查询总数
     *
     * @param query 查询条件（支持批量条件）
     * @return 总数
     */
    Long count(@Param("query") UserDOListQuery query);

    /**
     * 根据条件查询单个记录（取第一条）
     *
     * @param query 查询条件
     * @return 用户信息
     */
    UserDO getUser(@Param("query") UserDOGetQuery query);

    /**
     * 新增单个用户
     *
     * @return 影响行数
     */
    int insert(@Param("req") UserAddReq req);

    /**
     * 批量新增用户
     * @param reqs 用户列表
     * @return 影响行数
     */
    int insertBatch(@Param("users") List<UserAddReq> reqs);

    /**
     * 根据ID更新用户（选择性更新）
     * 
     * @param user 用户信息
     * @return 影响行数
     */
    int updateById(UserUpdateReq user);

    /**
     * 根据条件删除（逻辑删）
     *
     * @param query 删除条件（支持批量条件）
     * @return 影响行数
     */
    int delete(@Param("query") UserDeleteReq query);
} 
