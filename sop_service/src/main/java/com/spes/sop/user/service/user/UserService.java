package com.spes.sop.user.service.user;

import com.spes.sop.user.service.user.model.bo.UserBO;
import com.spes.sop.user.service.user.model.command.UserCreateCommand;
import com.spes.sop.user.service.user.model.command.UserDeleteCommand;
import com.spes.sop.user.service.user.model.command.UserPasswordUpdateCommand;
import com.spes.sop.user.service.user.model.command.UserUpdateCommand;
import com.spes.sop.user.service.user.model.query.UserGetQuery;
import com.spes.sop.user.service.user.model.query.UserPageQuery;

import java.util.List;

/**
 * 用户信息服务接口
 *
 * <AUTHOR>
 */
public interface UserService {

    /**
     * 分页查询用户列表
     *
     * @param pageQuery 分页查询条件
     * @return 用户列表
     */
    List<UserBO> getUserPage(UserPageQuery pageQuery);

    /**
     * 查询用户总数
     *
     * @param pageQuery 查询条件
     * @return 总数
     */
    Long getUserCount(UserPageQuery pageQuery);

    /**
     * 根据条件查询单个用户
     *
     * @param getQuery 查询条件
     * @return 用户信息，不存在返回null
     */
    UserBO getUser(UserGetQuery getQuery);

    /**
     * 创建用户
     *
     * @param createCommand 创建命令
     * @return 创建的用户信息
     */
    UserBO createUser(UserCreateCommand createCommand);

    /**
     * 批量创建用户
     *
     * @param createCommands 创建命令列表
     * @return 创建成功的数量
     */
    int createUserBatch(List<UserCreateCommand> createCommands);

    /**
     * 更新用户信息
     *
     * @param updateCommand 更新命令
     * @return 是否更新成功
     */
    boolean updateUser(UserUpdateCommand updateCommand);

    /**
     * 更新用户密码
     *
     * @param passwordUpdateCommand 密码更新命令
     * @return 是否更新成功
     */
    boolean updateUserPassword(UserPasswordUpdateCommand passwordUpdateCommand);

    /**
     * 重置用户密码
     *
     * @param userId      用户ID
     * @param newPassword 新密码（明文）
     * @param operatorId  操作人ID
     * @return 是否重置成功
     */
    boolean resetUserPassword(Long userId, String newPassword, Long operatorId);

    /**
     * 删除用户（逻辑删除）
     *
     * @param deleteCommand 删除命令
     * @return 删除成功的数量
     */
    int deleteUser(UserDeleteCommand deleteCommand);


    /**
     * 检查工号是否存在
     *
     * @param employeeId 工号
     * @return 是否存在
     */
    boolean isEmployeeIdExists(String employeeId);


    /**
     * 检查工号是否存在（排除指定用户ID）
     *
     * @param employeeId    工号
     * @param excludeUserId 排除的用户ID
     * @return 是否存在
     */
    boolean isEmployeeIdExists(String employeeId, Long excludeUserId);

    /**
     * 验证用户密码
     *
     * @param employeeId  工号
     * @param rawPassword 原始密码（明文）
     * @return 是否验证通过
     */
    boolean validateUserPassword(String employeeId, String rawPassword);
} 
