package com.spes.sop.user.service.user.model.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户更新命令对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserUpdateCommand {

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 工号
     */
    private String employeeId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 角色
     */
    private String role;

    /**
     * 织工ID
     */
    private Long weaverId;

    /**
     * 操作人ID
     */
    private Long operatorId;
} 