package com.spes.sop.user.service.user.model.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 用户分页查询对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserPageQuery {

    /**
     * 用户ID列表
     */
    private List<Long> ids;

    /**
     * 用户名（模糊查询）
     */
    private String username;

    /**
     * 角色列表
     */
    private List<String> roles;

    /**
     * 工号列表
     */
    private List<String> employeeIds;
    /**
     * 部门列表
     */
    private List<String> departments;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序方向：ASC/DESC
     */
    private String orderDirection;

    /**
     * 页码（从1开始）
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;
} 