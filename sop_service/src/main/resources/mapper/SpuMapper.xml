<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.spes.sop.goods.mapper.SpuMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.spes.sop.goods.mapper.model.entity.SpuDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="spu_code" property="spuCode" jdbcType="VARCHAR"/>
        <result column="spu_name" property="spuName" jdbcType="LONGVARCHAR"/>
        <result column="description" property="description" jdbcType="LONGVARCHAR"/>
        <result column="brand_id" property="brandId" jdbcType="BIGINT"/>
        <result column="first_classification" property="firstClassification" jdbcType="BIGINT"/>
        <result column="second_classification" property="secondClassification" jdbcType="BIGINT"/>
        <result column="third_classification" property="thirdClassification" jdbcType="BIGINT"/>
        <result column="fourth_classification" property="fourthClassification" jdbcType="BIGINT"/>
        <result column="channel_id" property="channelId" jdbcType="BIGINT"/>
        <result column="series_id" property="seriesId" jdbcType="BIGINT"/>
        <result column="category_id" property="categoryId" jdbcType="BIGINT"/>
        <result column="creator_id" property="creatorId" jdbcType="BIGINT"/>
        <result column="updater_id" property="updaterId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id
        , spu_code, spu_name, description, brand_id, first_classification, second_classification,
        third_classification, fourth_classification, channel_id, series_id, category_id, creator_id, updater_id, create_time, update_time, deleted
    </sql>

    <!-- 列表查询通用条件 -->
    <sql id="List_Where_Clause">
        <where>
            <if test="query.ids != null and query.ids.size() > 0">
                AND id IN
                <foreach collection="query.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="query.spuCodes != null and query.spuCodes.size() > 0">
                AND spu_code IN
                <foreach collection="query.spuCodes" item="spuCode" open="(" separator="," close=")">
                    #{spuCode}
                </foreach>
            </if>
            <if test="query.spuName != null and query.spuName != ''">
                AND spu_name LIKE CONCAT('%', #{query.spuName}, '%')
            </if>
            <if test="query.firstClassifications != null and query.firstClassifications.size() > 0">
                AND first_classification IN
                <foreach collection="query.firstClassifications" item="firstClassification" open="(" separator=","
                         close=")">
                    #{firstClassification}
                </foreach>
            </if>
            <if test="query.secondClassifications != null and query.secondClassifications.size() > 0">
                AND second_classification IN
                <foreach collection="query.secondClassifications" item="secondClassification" open="(" separator=","
                         close=")">
                    #{secondClassification}
                </foreach>
            </if>
            <if test="query.thirdClassifications != null and query.thirdClassifications.size() > 0">
                AND third_classification IN
                <foreach collection="query.thirdClassifications" item="thirdClassification" open="(" separator=","
                         close=")">
                    #{thirdClassification}
                </foreach>
            </if>
            <if test="query.brandIds != null and query.brandIds.size() > 0">
                AND brand_id IN
                <foreach collection="query.brandIds" item="brandId" open="(" separator="," close=")">
                    #{brandId}
                </foreach>
            </if>
            <if test="query.fourthClassifications != null and query.fourthClassifications.size() > 0">
                AND fourth_classification IN
                <foreach collection="query.fourthClassifications" item="fourthClassification" open="(" separator=","
                         close=")">
                    #{fourthClassification}
                </foreach>
            </if>
            <if test="query.channelIds != null and query.channelIds.size() > 0">
                AND channel_id IN
                <foreach collection="query.channelIds" item="channelId" open="(" separator="," close=")">
                    #{channelId}
                </foreach>
            </if>
            <if test="query.seriesIds != null and query.seriesIds.size() > 0">
                AND series_id IN
                <foreach collection="query.seriesIds" item="seriesId" open="(" separator="," close=")">
                    #{seriesId}
                </foreach>
            </if>
            <if test="query.categoryIds != null and query.categoryIds.size() > 0">
                AND category_id IN
                <foreach collection="query.categoryIds" item="categoryId" open="(" separator="," close=")">
                    #{categoryId}
                </foreach>
            </if>
            AND deleted = 0
        </where>
    </sql>

    <!-- 单个查询通用条件 -->
    <sql id="Get_Where_Clause">
        <where>
            <if test="query.id != null">
                AND id = #{query.id}
            </if>
            <if test="query.spuCode != null and query.spuCode != ''">
                AND spu_code = #{query.spuCode}
            </if>
            <if test="query.spuName != null and query.spuName != ''">
                AND spu_name = #{query.spuName}
            </if>
            AND deleted = 0
        </where>
    </sql>

    <!-- 通用排序 -->
    <sql id="Common_Order_By">
        <choose>
            <when test="query.orderBy != null and query.orderBy != ''">
                ORDER BY ${query.orderBy}
                <if test="query.orderDirection != null and query.orderDirection != ''">
                    ${query.orderDirection}
                </if>
            </when>
            <otherwise>
                ORDER BY create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 分页查询SPU列表 -->
    <select id="list" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sop_spu_info
        <include refid="List_Where_Clause"/>
        <include refid="Common_Order_By"/>
        <if test="query.limit != null and query.limit > 0">
            LIMIT #{query.offset}, #{query.limit}
        </if>
    </select>

    <!-- 查询SPU总数 -->
    <select id="count" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM sop_spu_info
        <include refid="List_Where_Clause"/>
    </select>

    <!-- 查询单个SPU -->
    <select id="getSpu" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sop_spu_info
        <include refid="Get_Where_Clause"/>
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 新增单个SPU -->
    <insert id="insert" parameterType="com.spes.sop.goods.mapper.model.req.SpuAddReq" useGeneratedKeys="true"
            keyProperty="req.id">
        INSERT INTO sop_spu_info (id,
                                  spu_code,
                                  spu_name,
                                  description,
                                  brand_id,
                                  first_classification,
                                  second_classification,
                                  third_classification,
                                  fourth_classification,
                                  channel_id,
                                  series_id,
                                  category_id,
                                  creator_id,
                                  updater_id,
                                  deleted,
                                  create_time,
                                  update_time)
        VALUES (#{req.id},
                #{req.spuCode},
                #{req.spuName},
                #{req.description},
                #{req.brandId},
                #{req.firstClassification},
                #{req.secondClassification},
                #{req.thirdClassification},
                #{req.fourthClassification},
                #{req.channelId},
                #{req.seriesId},
                #{req.categoryId},
                #{req.operatorId},
                #{req.operatorId},
                0,
                NOW(),
                NOW())
    </insert>

    <!-- 批量新增SPU -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO sop_spu_info (
        spu_code,
        spu_name,
        description,
        brand_id,
        first_classification,
        second_classification,
        third_classification,
        fourth_classification,
        channel_id,
        series_id,
        category_id,
        creator_id,
        updater_id,
        deleted,
        create_time,
        update_time
        ) VALUES
        <foreach collection="spus" item="req" separator=",">
            (
            #{req.spuCode},
            #{req.spuName},
            #{req.description},
            #{req.brandId},
            #{req.firstClassification},
            #{req.secondClassification},
            #{req.thirdClassification},
            #{req.fourthClassification},
            #{req.channelId},
            #{req.seriesId},
            #{req.categoryId},
            #{req.operatorId},
            #{req.operatorId},
            0,
            NOW(),
            NOW()
            )
        </foreach>
    </insert>

    <!-- 根据ID更新SPU -->
    <update id="updateById" parameterType="com.spes.sop.goods.mapper.model.req.SpuUpdateReq">
        UPDATE sop_spu_info
        <set>
            <if test="req.spuCode != null">
                spu_code = #{req.spuCode},
            </if>
            <if test="req.spuName != null">
                spu_name = #{req.spuName},
            </if>
            <if test="req.description != null">
                description = #{req.description},
            </if>
            <if test="req.brandId != null">
                brand_id = #{req.brandId},
            </if>
            <if test="req.firstClassification != null">
                first_classification = #{req.firstClassification},
            </if>
            <if test="req.secondClassification != null">
                second_classification = #{req.secondClassification},
            </if>
            <if test="req.thirdClassification != null">
                third_classification = #{req.thirdClassification},
            </if>
            <if test="req.fourthClassification != null">
                fourth_classification = #{req.fourthClassification},
            </if>
            <if test="req.channelId != null">
                channel_id = #{req.channelId},
            </if>
            <if test="req.seriesId != null">
                series_id = #{req.seriesId},
            </if>
            <if test="req.categoryId != null">
                category_id = #{req.categoryId},
            </if>
            <if test="req.operatorId != null">
                updater_id = #{req.operatorId},
            </if>
            update_time = NOW()
        </set>
        <where>
            id = #{req.id} AND deleted = 0
        </where>
    </update>

    <!-- 根据编码前缀统计SPU数量 -->
    <select id="countByCodePrefix" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM sop_spu_info
        WHERE spu_code LIKE #{codePrefix}
          AND deleted = 0
    </select>

</mapper> 