<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.spes.sop.user.mapper.UserMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.spes.sop.user.mapper.model.entity.UserDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="employee_id" property="employeeId" jdbcType="VARCHAR"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="role" property="role" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="department" property="department" jdbcType="VARCHAR"/>
        <result column="weaver_id" property="weaverId" jdbcType="BIGINT"/>
        <result column="creator_id" property="creatorId" jdbcType="BIGINT"/>
        <result column="updater_id" property="updaterId" jdbcType="BIGINT"/>
        <result column="deleted" property="deleted" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id
        , employee_id, username, role, password, department, weaver_id, creator_id, updater_id, deleted,
        create_time, update_time
    </sql>

    <!-- 列表查询通用条件 -->
    <sql id="List_Where_Clause">
        <where>

            <if test="query.ids != null and query.ids.size() > 0">
                AND id IN
                <foreach collection="query.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="query.employeeIds != null and query.employeeIds.size() > 0">
                AND employee_id IN
                <foreach collection="query.employeeIds" item="employeeId" open="(" separator="," close=")">
                    #{employeeId}
                </foreach>
            </if>
            <if test="query.username != null and query.username != ''">
                AND username LIKE CONCAT('%', #{query.username}, '%')
            </if>
            <if test="query.roles != null and query.roles.size() > 0">
                AND role IN
                <foreach collection="query.roles" item="role" open="(" separator="," close=")">
                    #{role}
                </foreach>
            </if>
            <if test="query.departments != null and query.departments.size() > 0">
                AND department IN
                <foreach collection="query.departments" item="department" open="(" separator="," close=")">
                    #{department}
                </foreach>
            </if>
            AND deleted = 0
        </where>
    </sql>

    <!-- 单个查询通用条件 -->
    <sql id="Get_Where_Clause">
        <where>
            <if test="query.id != null">
                AND id = #{query.id}
            </if>
            <if test="query.employeeId != null and query.employeeId != ''">
                AND employee_id = #{query.employeeId}
            </if>
            AND deleted = 0
        </where>
    </sql>

    <!-- 通用排序 -->
    <sql id="Common_Order_By">
        <choose>
            <when test="query.orderBy != null and query.orderBy != ''">
                ORDER BY ${query.orderBy}
                <if test="query.orderDirection != null and query.orderDirection != ''">
                    ${query.orderDirection}
                </if>
            </when>
            <otherwise>
                ORDER BY create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 分页查询用户列表 -->
    <select id="list" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sop_user
        <include refid="List_Where_Clause"/>
        <include refid="Common_Order_By"/>
        <if test="query.limit != null and query.limit > 0">
            LIMIT #{query.offset}, #{query.limit}
        </if>
    </select>

    <!-- 查询用户总数 -->
    <select id="count" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM sop_user
        <include refid="List_Where_Clause"/>
    </select>

    <!-- 查询单个用户 -->
    <select id="getUser" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sop_user
        <include refid="Get_Where_Clause"/>
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 新增单个用户 -->
    <insert id="insert" parameterType="com.spes.sop.user.mapper.model.req.UserAddReq" useGeneratedKeys="true"
            keyProperty="req.id">
        INSERT INTO sop_user (id,
                              employee_id,
                              username,
                              role,
                              password,
                              department,
                              weaver_id,
                              creator_id,
                              updater_id,
                              deleted,
                              create_time,
                              update_time)
        VALUES (#{req.id},
                #{req.employeeId},
                #{req.username},
                #{req.role},
                #{req.password},
                #{req.department},
                #{req.weaverId},
                #{req.operatorId},
                #{req.operatorId},
                0,
                NOW(),
                NOW())
    </insert>

    <!-- 批量新增用户 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO sop_user (
        id,
        employee_id,
        username,
        role,
        password,
        department,
        weaver_id,
        creator_id,
        updater_id,
        deleted,
        create_time,
        update_time
        ) VALUES
        <foreach collection="users" item="req" separator="," open="(" close=")">
            (
            #{req.id},
            #{req.employeeId},
            #{req.username},
            #{req.role},
            #{req.password},
            #{req.department},
            #{req.weaverId},
            #{req.operatorId},
            #{req.operatorId},
            0,
            NOW(),
            NOW()
            )
        </foreach>
    </insert>

    <!-- 根据ID更新用户 -->
    <update id="updateById" parameterType="com.spes.sop.user.mapper.model.req.UserUpdateReq">
        UPDATE sop_user
        <set>
            <if test="employeeId != null">
                employee_id = #{employeeId},
            </if>
            <if test="username != null">
                username = #{username},
            </if>
            <if test="role != null">
                role = #{role},
            </if>
            <if test="password != null">
                password = #{password},
            </if>
            <if test="department != null">
                department = #{department},
            </if>
            <if test="weaverId != null">
                weaver_id = #{weaverId},
            </if>
            <if test="operatorId != null">
                updater_id = #{operatorId},
            </if>
            update_time = NOW()
        </set>
        where
            id = #{id} AND deleted = 0
    </update>

    <!-- 根据条件逻辑删除用户 -->
    <update id="delete" parameterType="com.spes.sop.user.mapper.model.req.UserDeleteReq">
        UPDATE sop_user
        SET deleted = 1,
        updater_id = #{query.operatorId},
        update_time = NOW()
        where
            id = #{query.userId} AND deleted = 0
    </update>

</mapper> 