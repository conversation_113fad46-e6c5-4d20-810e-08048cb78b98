<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.spes.sop.config.mapper.GoodsChannelMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.spes.sop.config.mapper.model.entity.GoodsChannelDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="creator_id" property="creatorId" jdbcType="BIGINT"/>
        <result column="updater_id" property="updaterId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id
        , name, description, status, creator_id, updater_id, create_time, update_time, deleted
    </sql>

    <!-- 查询条件 -->
    <sql id="queryCondition">
        <where>
            AND deleted = 0
            <if test="query.ids != null and query.ids.size() > 0">
                AND id IN
                <foreach collection="query.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="query.names != null and query.names.size() > 0">
                AND name IN
                <foreach collection="query.names" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
            </if>
            <if test="query.nameSearch != null and query.nameSearch != ''">
                AND name LIKE CONCAT('%', #{query.nameSearch}, '%')
            </if>
            <if test="query.statuses != null and query.statuses.size() > 0">
                AND status IN
                <foreach collection="query.statuses" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
        </where>
    </sql>

    <!-- 分页查询商品渠道列表 -->
    <select id="list" parameterType="com.spes.sop.config.mapper.model.query.GoodsChannelDOListQuery"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sop_goods_channel
        <include refid="queryCondition"/>
        <if test="query.orderBy != null and query.orderBy != ''">
            ORDER BY ${query.orderBy}
            <if test="query.orderDirection != null and query.orderDirection != ''">
                ${query.orderDirection}
            </if>
        </if>
        <if test="query.orderBy == null or query.orderBy == ''">
            ORDER BY create_time DESC
        </if>
        <if test="query.offset != null and query.limit != null">
            LIMIT #{query.offset}, #{query.limit}
        </if>
    </select>

    <!-- 查询商品渠道总数 -->
    <select id="count" parameterType="com.spes.sop.config.mapper.model.query.GoodsChannelDOListQuery"
            resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM sop_goods_channel
        <include refid="queryCondition"/>
    </select>

    <!-- 查询单个商品渠道 -->
    <select id="getGoodsChannel"
            parameterType="com.spes.sop.config.mapper.model.query.GoodsChannelDOGetQuery"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sop_goods_channel
        <where>
            deleted = 0
            <if test="query.id != null">
                AND id = #{query.id}
            </if>
            <if test="query.name != null and query.name != ''">
                AND name = #{query.name}
            </if>
        </where>
        LIMIT 1
    </select>

    <!-- 新增商品渠道 -->
    <insert id="insert" parameterType="com.spes.sop.config.mapper.model.req.GoodsChannelAddReq"
            useGeneratedKeys="true" keyProperty="req.id">
        INSERT INTO sop_goods_channel (name,
                                       description,
                                       status,
                                       creator_id,
                                       updater_id,
                                       create_time,
                                       update_time,
                                       deleted)
        VALUES (#{req.name},
                #{req.description},
                #{req.status},
                #{req.creatorId},
                #{req.updaterId},
                NOW(),
                NOW(),
                0)
    </insert>

    <!-- 更新商品渠道 -->
    <update id="updateById" parameterType="com.spes.sop.config.mapper.model.req.GoodsChannelUpdateReq">
        UPDATE sop_goods_channel
        <set>
            <if test="req.name != null and req.name != ''">
                name = #{req.name},
            </if>
            <if test="req.description != null">
                description = #{req.description},
            </if>
            <if test="req.status != null">
                status = #{req.status},
            </if>
            <if test="req.updaterId != null">
                updater_id = #{req.updaterId},
            </if>
            update_time = NOW()
        </set>
        WHERE id = #{req.id} AND deleted = 0
    </update>

    <!-- 逻辑删除商品渠道 -->
    <update id="delete" parameterType="com.spes.sop.config.mapper.model.req.GoodsChannelDeleteReq">
        UPDATE sop_goods_channel
        SET deleted     = 1,
            updater_id = #{req.updaterId},
            update_time = NOW()
        WHERE id = #{req.id}
          AND deleted = 0
    </update>

</mapper> 