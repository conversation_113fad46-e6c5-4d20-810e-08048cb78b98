<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.spes.sop.goods.mapper.CombinationSkuRelationMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.spes.sop.goods.mapper.model.entity.CombinationSkuRelationDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="combination_id" property="combinationId" jdbcType="BIGINT"/>
        <result column="combination_version" property="combinationVersion" jdbcType="INTEGER"/>
        <result column="sku_id" property="skuId" jdbcType="BIGINT"/>
        <result column="amount" property="amount" jdbcType="INTEGER"/>
        <result column="main" property="main" jdbcType="TINYINT"/>
        <result column="gift" property="gift" jdbcType="TINYINT"/>
        <result column="effective_time" property="effectiveTime" jdbcType="TIMESTAMP"/>
        <result column="expiration_time" property="expirationTime" jdbcType="TIMESTAMP"/>
        <result column="effective" property="effective" jdbcType="TINYINT"/>
        <result column="creator_id" property="creatorId" jdbcType="BIGINT"/>
        <result column="updater_id" property="updaterId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id
        , combination_id, combination_version, sku_id, amount, main, gift, effective_time, expiration_time, effective,
        creator_id, updater_id, create_time, update_time, deleted
    </sql>

    <!-- 查询条件 -->
    <sql id="queryCondition">

        <where>
            <if test="query.ids != null and query.ids.size() > 0">
                AND id IN
                <foreach item="item" collection="query.ids" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.combinationIds != null and query.combinationIds.size() > 0">
                AND combination_id IN
                <foreach item="item" collection="query.combinationIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.versions != null and query.versions.size() > 0">
                AND combination_version IN
                <foreach item="item" collection="query.versions" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.skuIds != null and query.skuIds.size() > 0">
                AND sku_id IN
                <foreach item="item" collection="query.skuIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.main != null">
                AND main = #{query.main}
            </if>
            <if test="query.gift != null">
                AND gift = #{query.gift}
            </if>
            <if test="query.effectiveTimeStart != null">
                AND effective_time &gt;= #{query.effectiveTimeStart}
            </if>
            <if test="query.effectiveTimeEnd != null">
                AND effective_time &lt;= #{query.effectiveTimeEnd}
            </if>
            <if test="query.expirationTimeStart != null">
                AND expiration_time &gt;= #{query.expirationTimeStart}
            </if>
            <if test="query.expirationTimeEnd != null">
                AND expiration_time &lt;= #{query.expirationTimeEnd}
            </if>
            <if test="query.effective != null">
                AND effective = #{query.effective}
            </if>
            <if test="true">
                AND deleted = 0
            </if>

        </where>
    </sql>

    <!-- 分页查询组合品SKU关联关系列表 -->
    <select id="list" parameterType="com.spes.sop.goods.mapper.model.query.CombinationSkuRelationDOListQuery"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sop_combination_sku_relation
        <include refid="queryCondition"/>
        <if test="query.orderBy != null and query.orderBy != ''">
            ORDER BY ${query.orderBy}
            <if test="query.orderDirection != null and query.orderDirection != ''">
                ${query.orderDirection}
            </if>
        </if>
        <if test="query.orderBy == null or query.orderBy == ''">
            ORDER BY create_time DESC
        </if>
        <if test="query.offset != null and query.limit != null">
            LIMIT #{query.offset}, #{query.limit}
        </if>
    </select>

    <!-- 查询组合品SKU关联关系总数 -->
    <select id="count" parameterType="com.spes.sop.goods.mapper.model.query.CombinationSkuRelationDOListQuery"
            resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM sop_combination_sku_relation
        <include refid="queryCondition"/>
    </select>


    <!-- 新增组合品SKU关联关系 -->
    <insert id="insert" parameterType="com.spes.sop.goods.mapper.model.req.CombinationSkuRelationAddReq"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sop_combination_sku_relation (combination_id,
                                                  combination_version,
                                                  sku_id,
                                                  amount,
                                                  main,
                                                  gift,
                                                  effective_time,
                                                  expiration_time,
                                                  effective,
                                                  creator_id,
                                                  updater_id,
                                                  create_time,
                                                  update_time,
                                                  deleted)
        VALUES (#{req.combinationId},
                #{req.combinationVersion},
                #{req.skuId},
                #{req.amount},
                #{req.main},
                #{req.gift},
                #{req.effectiveTime},
                #{req.expirationTime},
                COALESCE(#{req.effective}, 1),
                #{req.creatorId},
                #{req.updaterId},
                NOW(),
                NOW(),
                0)
    </insert>

    <!-- 批量新增组合品SKU关联关系 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO sop_combination_sku_relation (
        combination_id,
        combination_version,
        sku_id,
        amount,
        main,
        gift,
        effective_time,
        expiration_time,
        effective,
        creator_id,
        updater_id,
        create_time,
        update_time,
        deleted
        ) VALUES
        <foreach collection="req" item="item" separator=",">
            (
            #{item.combinationId},
            #{item.combinationVersion},
            #{item.skuId},
            #{item.amount},
            #{item.main},
            #{item.gift},
            #{item.effectiveTime},
            #{item.expirationTime},
            COALESCE(#{item.effective}, 1),
            #{item.creatorId},
            #{item.updaterId},
            NOW(),
            NOW(),
            0
            )
        </foreach>
    </insert>

    <!-- 更新组合品SKU关联关系 -->
    <update id="updateById" parameterType="com.spes.sop.goods.mapper.model.req.CombinationSkuRelationUpdateReq">
        UPDATE sop_combination_sku_relation
        <set>
            <if test="req.combinationId != null">
                combination_id = #{req.combinationId},
            </if>
            <if test="req.combinationVersion != null">
                combination_version = #{req.combinationVersion},
            </if>
            <if test="req.skuId != null">
                sku_id = #{req.skuId},
            </if>
            <if test="req.amount != null">
                amount = #{req.amount},
            </if>
            <if test="req.main != null">
                main = #{req.main},
            </if>
            <if test="req.gift != null">
                gift = #{req.gift},
            </if>
            <if test="req.effectiveTime != null">
                effective_time = #{req.effectiveTime},
            </if>
            <if test="req.expirationTime != null">
                expiration_time = #{req.expirationTime},
            </if>
            <if test="req.effective != null">
                effective = #{req.effective},
            </if>
            <if test="req.updaterId != null">
                updater_id = #{req.updaterId},
            </if>
            update_time = NOW()
        </set>
        WHERE id = #{req.id} AND deleted = 0
    </update>

    <!-- 逻辑删除组合品SKU关联关系 -->
    <update id="delete" parameterType="com.spes.sop.goods.mapper.model.req.CombinationSkuRelationDeleteReq">
        UPDATE sop_combination_sku_relation
        SET deleted     = 1,
            updater_id = #{req.updaterId},
            update_time = NOW()
        WHERE id = #{req.id}
          AND deleted = 0
    </update>

    <!-- 批量设置组合品SKU关联关系失效 -->
    <update id="batchSetIneffective">
        UPDATE sop_combination_sku_relation
        SET effective       = 0,
            expiration_time = NOW(),
            updater_id      = #{updaterId},
            update_time     = NOW()
        WHERE combination_id = #{combinationId}
          AND combination_version = #{version}
          AND deleted = 0
          AND effective = 1
    </update>

    <!-- 批量设置组合品SKU关联关系失效 -->
    <update id="batchSetEffective">
        UPDATE sop_combination_sku_relation
        SET effective      = 1,
            effective_time = NOW(),
            updater_id     = #{updaterId},
            update_time    = NOW()
        WHERE combination_id = #{combinationId}
          AND combination_version = #{version}
          AND deleted = 0
          AND effective = 0
    </update>

</mapper> 