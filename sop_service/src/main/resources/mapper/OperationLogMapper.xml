<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.spes.sop.log.mapper.OperationLogMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.spes.sop.log.mapper.model.entity.OperationLogDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="ip_address" property="ipAddress" jdbcType="VARCHAR"/>
        <result column="request_method" property="requestMethod" jdbcType="VARCHAR"/>
        <result column="request_url" property="requestUrl" jdbcType="LONGVARCHAR"/>
        <result column="request_path" property="requestPath" jdbcType="VARCHAR"/>
        <result column="class_name" property="className" jdbcType="VARCHAR"/>
        <result column="method_name" property="methodName" jdbcType="VARCHAR"/>
        <result column="request_params" property="requestParams" jdbcType="LONGVARCHAR"/>
        <result column="response_result" property="responseResult" jdbcType="LONGVARCHAR"/>
        <result column="request_time" property="requestTime" jdbcType="TIMESTAMP"/>
        <result column="response_time" property="responseTime" jdbcType="TIMESTAMP"/>
        <result column="execution_time" property="executionTime" jdbcType="BIGINT"/>
        <result column="success" property="success" jdbcType="TINYINT"/>
        <result column="error_message" property="errorMessage" jdbcType="LONGVARCHAR"/>
        <result column="operation_desc" property="operationDesc" jdbcType="VARCHAR"/>
        <result column="user_agent" property="userAgent" jdbcType="LONGVARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 插入操作日志 -->
    <insert id="insert" parameterType="com.spes.sop.log.mapper.model.entity.OperationLogDO">
        INSERT INTO sop_operation_log (user_id,
                                       username,
                                       ip_address,
                                       request_method,
                                       request_url,
                                       request_path,
                                       class_name,
                                       method_name,
                                       request_params,
                                       response_result,
                                       request_time,
                                       response_time,
                                       execution_time,
                                       success,
                                       error_message,
                                       operation_desc,
                                       user_agent,
                                       create_time)
        VALUES (#{log.userId},
                #{log.username},
                #{log.ipAddress},
                #{log.requestMethod},
                #{log.requestUrl},
                #{log.requestPath},
                #{log.className},
                #{log.methodName},
                #{log.requestParams},
                #{log.responseResult},
                #{log.requestTime},
                #{log.responseTime},
                #{log.executionTime},
                #{log.success},
                #{log.errorMessage},
                #{log.operationDesc},
                #{log.userAgent},
                #{log.createTime})
    </insert>

</mapper> 