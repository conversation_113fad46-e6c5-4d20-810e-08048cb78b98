// 简单的编译测试
import com.spes.sop.support.goods.model.dto.*;

public class TestCompilation {
    public static void main(String[] args) {
        // 测试 DTO 类是否能正确实例化
        CombinationPageDTO combinationPageDTO = new CombinationPageDTO();
        CombinationDetailDTO combinationDetailDTO = new CombinationDetailDTO();
        CombinationSkuItemDTO combinationSkuItemDTO = new CombinationSkuItemDTO();
        SpuPageDTO spuPageDTO = new SpuPageDTO();
        SpuDetailDTO spuDetailDTO = new SpuDetailDTO();
        SkuListDTO skuListDTO = new SkuListDTO();
        SkuDetailDTO skuDetailDTO = new SkuDetailDTO();
        
        System.out.println("所有 DTO 类编译成功！");
    }
} 