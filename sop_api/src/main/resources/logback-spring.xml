<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 定义日志文件的基础路径 -->
    <springProfile name="dev">
        <property name="LOG_PATH" value="./logs/dev"/>
        <property name="LOG_LEVEL" value="DEBUG"/>
    </springProfile>

    <springProfile name="prod">
        <property name="LOG_PATH" value="/var/log/sop/prod"/>
        <property name="LOG_LEVEL" value="INFO"/>
    </springProfile>

    <springProfile name="!dev,!prod">
        <property name="LOG_PATH" value="./logs"/>
        <property name="LOG_LEVEL" value="INFO"/>
    </springProfile>

    <!-- 定义日志格式 -->
    <property name="CONSOLE_LOG_PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %highlight(%-5level) %cyan(%logger{50}) - %msg%n"/>

    <property name="FILE_LOG_PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-}] %logger{50} - %msg%n"/>

    <!-- 控制台输出 -->
    <springProfile name="dev">
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>${CONSOLE_LOG_PATTERN}</pattern>
                <charset>UTF-8</charset>
            </encoder>
        </appender>
    </springProfile>

    <!-- 应用日志文件输出 - 按日期分片 -->
    <appender name="APP_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/app/sop-app.log</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>

        <!-- 滚动策略：按日期和大小 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件名模式：按日期分片 -->
            <fileNamePattern>${LOG_PATH}/app/sop-app.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <!-- 单个文件最大大小 -->
            <maxFileSize>100MB</maxFileSize>
            <!-- 保留天数 -->
            <maxHistory>30</maxHistory>
            <!-- 总大小限制 -->
            <totalSizeCap>10GB</totalSizeCap>
            <!-- 启动时清理旧文件 -->
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>

        <!-- 过滤器：只记录应用相关日志 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
    </appender>

    <!-- 错误日志文件输出 - 按日期分片 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/error/sop-error.log</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>

        <!-- 滚动策略：按日期和大小 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/error/sop-error.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>60</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>

        <!-- 过滤器：只记录ERROR级别日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- SQL日志文件输出 - 按日期分片 -->
    <appender name="SQL_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/sql/sop-sql.log</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/sql/sop-sql.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>200MB</maxFileSize>
            <maxHistory>15</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
    </appender>

    <!-- 第三方服务日志文件输出 - 按日期分片 -->
    <appender name="THIRD_PARTY_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/third-party/sop-third-party.log</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/third-party/sop-third-party.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
    </appender>

    <!-- 安全相关日志文件输出 - 按日期分片 -->
    <appender name="SECURITY_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/security/sop-security.log</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/security/sop-security.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>90</maxHistory>
            <totalSizeCap>2GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
    </appender>

    <!-- 异步日志输出 - 提高性能 -->
    <appender name="ASYNC_APP_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="APP_FILE"/>
        <queueSize>1024</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>false</includeCallerData>
    </appender>

    <appender name="ASYNC_ERROR_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="ERROR_FILE"/>
        <queueSize>256</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>true</includeCallerData>
    </appender>

    <appender name="ASYNC_SQL_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="SQL_FILE"/>
        <queueSize>512</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>false</includeCallerData>
    </appender>

    <appender name="ASYNC_THIRD_PARTY_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="THIRD_PARTY_FILE"/>
        <queueSize>256</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>false</includeCallerData>
    </appender>

    <appender name="ASYNC_SECURITY_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="SECURITY_FILE"/>
        <queueSize>256</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>true</includeCallerData>
    </appender>

    <!-- 具体Logger配置 -->

    <!-- 应用主要包的日志 -->
    <logger name="com.spes.sop" level="${LOG_LEVEL}" additivity="false">
        <springProfile name="dev">
            <appender-ref ref="CONSOLE"/>
        </springProfile>
        <appender-ref ref="ASYNC_APP_FILE"/>
        <appender-ref ref="ASYNC_ERROR_FILE"/>
    </logger>

    <!-- SQL相关日志 -->
    <logger name="com.spes.sop.goods.mapper" level="DEBUG" additivity="false">
        <springProfile name="dev">
            <appender-ref ref="CONSOLE"/>
        </springProfile>
        <appender-ref ref="ASYNC_SQL_FILE"/>
    </logger>

    <!-- 第三方服务日志 -->
    <logger name="com.spes.sop.third" level="INFO" additivity="false">
        <springProfile name="dev">
            <appender-ref ref="CONSOLE"/>
        </springProfile>
        <appender-ref ref="ASYNC_THIRD_PARTY_FILE"/>
        <appender-ref ref="ASYNC_ERROR_FILE"/>
    </logger>

    <!-- 安全相关日志 -->
    <logger name="com.spes.sop.common.security" level="INFO" additivity="false">
        <springProfile name="dev">
            <appender-ref ref="CONSOLE"/>
        </springProfile>
        <appender-ref ref="ASYNC_SECURITY_FILE"/>
        <appender-ref ref="ASYNC_ERROR_FILE"/>
    </logger>

    <logger name="org.springframework.security" level="WARN" additivity="false">
        <springProfile name="dev">
            <appender-ref ref="CONSOLE"/>
        </springProfile>
        <appender-ref ref="ASYNC_SECURITY_FILE"/>
    </logger>

    <!-- Druid监控日志 -->
    <logger name="com.alibaba.druid" level="INFO" additivity="false">
        <springProfile name="dev">
            <appender-ref ref="CONSOLE"/>
        </springProfile>
        <appender-ref ref="ASYNC_APP_FILE"/>
    </logger>

    <!-- 根Logger -->
    <root level="WARN">
        <springProfile name="dev">
            <appender-ref ref="CONSOLE"/>
        </springProfile>
        <appender-ref ref="ASYNC_APP_FILE"/>
        <appender-ref ref="ASYNC_ERROR_FILE"/>
    </root>

</configuration> 