spring:
  # 数据源配置
  datasource:
    # 线上环境数据库连接
    url: *******************************************************************************************************************************************************************************************************************
    username: sop_prod
    password: i=Qc03J:5w@BNVp
    # Druid连接池配置
    druid:
      # 线上环境连接池配置（较小）
      initial-size: 5
      min-idle: 10
      max-active: 20

# MyBatis线上环境配置
mybatis:
  configuration:
    # 线上环境显示SQL
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


# JWT Token线上环境配置
app:
  jwt:
    # 线上环境JWT密钥
    secret: "dev-spes-sop-jwt-secret-key-for-development-environment-only-must-be-at-least-512-bits-long-to-meet-hs512-algorithm-security-requirements"
    # 线上环境较短过期时间 - 2小时
    access-token-expiration: 7200
    # 线上环境刷新Token过期时间 - 1天
    refresh-token-expiration: 86400

  # 线上模式配置
  dev:
    enabled: false

  # 线上环境CORS配置
  cors:
    # 线上环境允许所有源地址
    allowed-origins: "*"
    allowed-methods: "GET,POST,PUT,DELETE,OPTIONS,PATCH"
    allowed-headers: "*"
    allow-credentials: false  # 当allowed-origins为*时，必须设置为false
    max-age: 3600


# 金蝶云星空配置 - 线上环境
kingdee:
  # 线上环境金蝶服务器地址
  server-url: "http://10.8.0.4/k3cloud/"
  # 线上环境套接ID
  acct-id: "65a768916d6b22"
  # 线上环境用户名
  username: "administrator"
  # 线上环境密码
  password: "kingdee123@"
  # 语言ID（2052为中文简体）
  lcid: 2052
  # 线上环境较短的超时时间
  connect-timeout: 10000
  read-timeout: 30000
  # Session过期时间（线上环境10 分钟）
  session-expire-time: 60000

# 吉客云配置 - 线上环境
jackyun:
  # 线上环境吉客云服务器地址
  server-url: "https://open.jackyun.com/open/openapi/do"
  # API版本
  api-version: "1.0"
  # 线上环境应用ID
  app-key: "73914332"
  # 线上环境应用密钥
  app-secret: "a664417f04db4f0086a77fb0a16c99d1"

# 泛微ecology系统配置 - 线上环境
weaver:
  # 线上环境泛微ecology系统地址
  serverUrl: "http://121.199.175.82:8090"
  # 应用ID（APPID）
  appId: "04680122-6822-5E8C-F4C8-B2490840BB23"
  # 默认用户ID
  defaultUserId: "1"
  # Token有效期（秒）
  tokenExpireTime: 600
  # 连接超时时间（毫秒）
  connectTimeout: 10000
  # 读取超时时间（毫秒）
  readTimeout: 30000
  # 是否启用缓存
  enableCache: true


# 生产环境日志配置
logging:
  config: classpath:logback-spring.xml  # 使用专门的日志配置文件
  level:
    # 根日志级别 - 生产环境只记录重要信息
    root: WARN
    # 应用日志级别
    com.spes.sop: INFO
    # 安全相关保持INFO级别
    org.springframework.security: WARN
    com.spes.sop.common.security: INFO
    # 控制器层保持INFO
    com.spes.sop.controller: INFO
    # 三方服务日志 - 生产环境INFO级别
    com.spes.sop.third.kingdee: INFO
    com.spes.sop.third.jackyun: INFO
    com.spes.sop.third.weaver: INFO
    # SQL相关关闭详细日志
    com.spes.sop.goods.mapper: WARN
  # 生产环境不输出到控制台，只输出到文件
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"

# 生产环境管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics  # 生产环境只暴露必要端点
  endpoint:
    health:
      show-details: never  # 生产环境不显示健康检查详情