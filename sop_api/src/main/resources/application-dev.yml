spring:
  # 数据源配置
  datasource:
    # 开发环境数据库连接
    url: **************************************************************************************************************************************************************************************************************
    username: sop
    password: 0UarfGuz
    # Druid连接池配置
    druid:
      # 开发环境连接池配置（较小）
      initial-size: 5
      min-idle: 10
      max-active: 20

# MyBatis开发环境配置
mybatis:
  configuration:
    # 开发环境显示SQL
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


# JWT Token开发环境配置
app:
  jwt:
    # 开发环境JWT密钥
    secret: "dev-spes-sop-jwt-secret-key-for-development-environment-only-must-be-at-least-512-bits-long-to-meet-hs512-algorithm-security-requirements"
    # 开发环境较短过期时间 - 2小时
    access-token-expiration: 7200
    # 开发环境刷新Token过期时间 - 1天
    refresh-token-expiration: 86400

  # 开发模式配置
  dev:
    enabled: false
    mock-user-id: 320796283612827648
    mock-username: "系统"
    mock-user-roles: "ADMIN,USER"
    mock-employee-Id: "H-0000"
    mock-department: "全部"

  # 开发环境CORS配置
  cors:
    # 开发环境允许所有源地址
    allowed-origins: "*"
    allowed-methods: "GET,POST,PUT,DELETE,OPTIONS,PATCH"
    allowed-headers: "*"
    allow-credentials: false  # 当allowed-origins为*时，必须设置为false
    max-age: 3600


# 金蝶云星空配置 - 开发环境
kingdee:
  # 开发环境金蝶服务器地址
  server-url: "http://192.168.49.230/k3cloud/"
  # 开发环境套接ID
  acct-id: "66d66a2167314d"
  # 开发环境用户名
  username: "administrator"
  # 开发环境密码
  password: "kingdee123@"
  # 语言ID（2052为中文简体）
  lcid: 2052
  # 开发环境较短的超时时间
  connect-timeout: 10000
  read-timeout: 30000
  # Session过期时间（开发环境10 分钟）
  session-expire-time: 60000


# 吉客云配置 - 开发环境
jackyun:
  # 开发环境吉客云服务器地址
  server-url: "https://open.jackyun.com/open/openapi/do"
  # API版本
  api-version: "1.0"
  # 开发环境应用ID
  app-key: "73914332"
  # 开发环境应用密钥
  app-secret: "a664417f04db4f0086a77fb0a16c99d1"




# 泛微ecology系统配置 - 开发环境
weaver:
  # 开发环境泛微ecology系统地址
  serverUrl: "http://121.199.175.82:8090"
  # 应用ID（APPID）
  appId: "04680122-6822-5E8C-F4C8-B2490840BB23"
  # 默认用户ID
  defaultUserId: "1"
  # Token有效期（秒）
  tokenExpireTime: 600
  # 连接超时时间（毫秒）
  connectTimeout: 10000
  # 读取超时时间（毫秒）
  readTimeout: 30000
  # 是否启用缓存
  enableCache: true


# 开发环境日志配置
logging:
  level:
    # 根日志级别
    root: INFO
    # 开发环境详细日志
    org.springframework.security: DEBUG
    com.spes.sop.common.security: DEBUG
    com.spes.sop.common.security.JwtAuthenticationFilter: DEBUG
    com.spes.sop.common.context: DEBUG
    com.spes.sop.common.interceptor: DEBUG
    com.spes.sop.controller.auth: DEBUG
    com.spes.sop.controller: DEBUG
    com.spes.sop.goods.mapper: DEBUG
    # 三方服务日志
    com.spes.sop.third.kingdee: DEBUG
    com.spes.sop.third.jackyun: DEBUG
    com.spes.sop.third.weaver: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n" 