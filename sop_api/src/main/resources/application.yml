server:
  port: 8999

spring:
  profiles:
    active: @profiles.active@
  application:
    name: sop-system
  
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
      enabled: true

  # 数据源通用配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    # Druid连接池通用配置
    druid:
      # 通用连接池配置
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall,slf4j
      connection-properties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      web-stat-filter:
        enabled: true
        url-pattern: "/*"
        exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*"

# MyBatis配置
mybatis:
  # 修改映射文件路径，指向service模块
  mapper-locations: classpath*:mapper/*.xml
  type-aliases-package: com.spes.sop.*.mapper.model.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'

# SOP系统配置
sop:
  # 分布式ID生成器配置
  id-worker:
    # 工作机器ID (0-31)
    worker-id: 1
    # 数据中心ID (0-31)
    datacenter-id: 1

# 日志配置 - 使用 logback-spring.xml
logging:
  config: classpath:logback-spring.xml 