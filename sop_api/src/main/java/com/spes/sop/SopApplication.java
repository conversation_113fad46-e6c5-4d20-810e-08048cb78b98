package com.spes.sop;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * SOP应用启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = "com.spes.sop")
@EnableTransactionManagement
@EnableScheduling
@MapperScan("com.spes.sop.*.mapper")
public class SopApplication {

    public static void main(String[] args) {
        SpringApplication.run(SopApplication.class, args);
    }
} 
