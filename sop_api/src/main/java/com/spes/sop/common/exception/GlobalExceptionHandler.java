package com.spes.sop.common.exception;

import com.spes.sop.common.result.Result;
import com.spes.sop.common.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.cors.CorsUtils;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 *
 * <AUTHOR>

 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理参数校验异常（@Valid注解）
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Result<Void>> handleMethodArgumentNotValidException(
            MethodArgumentNotValidException e, HttpServletRequest request) {

        Objects.requireNonNull(e, "异常对象不能为空");
        Objects.requireNonNull(request, "请求对象不能为空");

        log.warn("参数校验失败，请求路径：{}，错误详情：{}", request.getRequestURI(), e.getMessage());

        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .filter(Objects::nonNull)
                .collect(Collectors.joining("; "));

        if (errorMessage.isEmpty()) {
            errorMessage = "参数校验失败";
        }

        return ResponseEntity.status(HttpStatus.OK)
                .body(Result.error(ResultCode.ERROR, errorMessage));
    }

    /**
     * 处理参数绑定异常
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<Result<Void>> handleBindException(
            BindException e, HttpServletRequest request) {

        log.warn("参数绑定失败，请求路径：{}，错误详情：{}", request.getRequestURI(), e.getMessage());

        String errorMessage = e.getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .filter(Objects::nonNull)
                .collect(Collectors.joining("; "));

        if (errorMessage.isEmpty()) {
            errorMessage = "参数绑定失败";
        }

        return ResponseEntity.status(HttpStatus.OK)
                .body(Result.error(ResultCode.ERROR, errorMessage));
    }

    /**
     * 处理约束校验异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<Result<Void>> handleConstraintViolationException(
            ConstraintViolationException e, HttpServletRequest request) {

        log.warn("约束校验失败，请求路径：{}，错误详情：{}", request.getRequestURI(), e.getMessage());
        
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        String errorMessage = violations.stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining("; "));

        if (errorMessage.isEmpty()) {
            errorMessage = "约束校验失败";
        }

        return ResponseEntity.status(HttpStatus.OK)
                .body(Result.error(ResultCode.ERROR, errorMessage));
    }

    /**
     * 处理参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<Result<Void>> handleMethodArgumentTypeMismatchException(
            MethodArgumentTypeMismatchException e, HttpServletRequest request) {

        log.warn("参数类型不匹配，请求路径：{}，参数名：{}，错误详情：{}",
                request.getRequestURI(), e.getName(), e.getMessage());

        String errorMessage = String.format("参数 '%s' 类型不正确", e.getName());

        return ResponseEntity.status(HttpStatus.OK)
                .body(Result.error(ResultCode.ERROR, errorMessage));
    }

    /**
     * 处理请求方法不支持异常
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseEntity<Result<Void>> handleHttpRequestMethodNotSupportedException(
            HttpRequestMethodNotSupportedException e, HttpServletRequest request) {

        log.warn("请求方法不支持，请求路径：{}，请求方法：{}，支持的方法：{}",
                request.getRequestURI(), request.getMethod(), e.getSupportedHttpMethods());

        String errorMessage = String.format("请求方法 '%s' 不支持，支持的方法：%s",
                request.getMethod(), e.getSupportedHttpMethods());

        return ResponseEntity.status(HttpStatus.OK)
                .body(Result.error(ResultCode.ERROR, errorMessage));
    }

    /**
     * 处理404异常
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public ResponseEntity<Result<Void>> handleNoHandlerFoundException(
            NoHandlerFoundException e, HttpServletRequest request) {

        log.warn("请求路径不存在，请求路径：{}，请求方法：{}",
                request.getRequestURI(), request.getMethod());

        String errorMessage = String.format("请求路径 '%s' 不存在", request.getRequestURI());

        return ResponseEntity.status(HttpStatus.OK)
                .body(Result.error(404, errorMessage));
    }

    /**
     * 处理认证异常
     */
    @ExceptionHandler(AuthenticationException.class)
    public ResponseEntity<Result<Void>> handleAuthenticationException(
            AuthenticationException e, HttpServletRequest request) {

        log.warn("认证失败，请求路径：{}，错误详情：{}", request.getRequestURI(), e.getMessage());

        String errorMessage = "认证失败";
        if (e instanceof BadCredentialsException) {
            errorMessage = "用户名或密码错误";
        }

        return ResponseEntity.status(HttpStatus.OK)
                .body(Result.error(ResultCode.ERROR, errorMessage));
    }

    /**
     * 处理权限不足异常
     */
    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<Result<Void>> handleAccessDeniedException(
            AccessDeniedException e, HttpServletRequest request) {

        log.warn("权限不足，请求路径：{}，错误详情：{}", request.getRequestURI(), e.getMessage());

        return ResponseEntity.status(HttpStatus.OK)
                .body(Result.error(ResultCode.ERROR, "权限不足"));
    }

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<Result<Void>> handleBusinessException(
            BusinessException e, HttpServletRequest request) {

        log.warn("业务异常，请求路径：{}，错误码：{}，错误信息：{}",
                request.getRequestURI(), e.getCode(), e.getMessage());

        return ResponseEntity.status(HttpStatus.OK)
                .body(Result.error(ResultCode.ERROR, e.getMessage()));
    }

    /**
     * 处理系统异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Result<Void>> handleException(
            Exception e, HttpServletRequest request, HttpServletResponse response) {

        // 检查是否为CORS预检请求
        if (CorsUtils.isPreFlightRequest(request)) {
            log.debug("CORS预检请求，请求路径：{}", request.getRequestURI());
            return ResponseEntity.ok().build();
        }

        log.error("系统异常，请求路径：{}，错误详情：", request.getRequestURI(), e);

        // 设置CORS响应头（确保错误响应也支持跨域）
        setCorsHeaders(response);

        return ResponseEntity.status(HttpStatus.OK)
                .body(Result.error(ResultCode.ERROR, e.getMessage()));
    }

    /**
     * 设置CORS响应头
     */
    private void setCorsHeaders(HttpServletResponse response) {
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Authorization, Content-Type, X-Requested-With");
        response.setHeader("Access-Control-Max-Age", "3600");
    }
} 
