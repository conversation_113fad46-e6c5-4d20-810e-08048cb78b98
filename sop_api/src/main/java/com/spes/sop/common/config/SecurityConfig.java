package com.spes.sop.common.config;

import com.spes.sop.common.security.JwtAuthenticationFilter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfigurationSource;

/**
 * Spring Security配置类
 *
 * <AUTHOR>

 */
@Slf4j
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true, securedEnabled = true)
@RequiredArgsConstructor
public class SecurityConfig {

    private final JwtAuthenticationFilter jwtAuthenticationFilter;
    private final CorsConfigurationSource corsConfigurationSource;

    /**
     * 配置Security过滤器链
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        log.info("配置Spring Security过滤器链");

        http
                // 启用CORS支持
                .cors().configurationSource(corsConfigurationSource)
                .and()

                // 禁用CSRF（因为使用JWT Token）
                .csrf().disable()

                // 禁用Session（使用无状态JWT认证）
                .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and()

                // 配置请求授权
                .authorizeHttpRequests(authz -> authz
                        // 允许访问的路径（无需认证）
                        .antMatchers(
                                "/api/auth/login",      // 登录接口
                                "/api/health",          // 健康检查
                                "/actuator/**",         // 监控端点
                                "/swagger-ui/**",       // Swagger UI
                                "/v3/api-docs/**",      // OpenAPI文档
                                "/favicon.ico",         // 网站图标
                                "/error"                // 错误页面
                        ).permitAll()

                        // 其他所有请求都需要认证
                        .anyRequest().authenticated()
                )

                // 添加JWT认证过滤器
                .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class)

                // 禁用默认的登录页面
                .formLogin().disable()

                // 禁用HTTP Basic认证
                .httpBasic().disable()

                // 禁用默认的登出功能（我们有自己的登出接口）
                .logout().disable();

        log.info("Spring Security配置完成，已启用CORS跨域支持");

        return http.build();
    }
} 
