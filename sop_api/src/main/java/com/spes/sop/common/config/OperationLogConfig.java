package com.spes.sop.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * 操作日志配置类
 *
 * <AUTHOR>
 */
@Configuration
@EnableAsync
public class OperationLogConfig {

    /**
     * 异步任务执行器
     */
    @Bean(name = "operationLogExecutor")
    public Executor operationLogExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(5);
        executor.setQueueCapacity(100);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("operation-log-");
        executor.setRejectedExecutionHandler((r, executor1) -> {
            // 如果队列满了，在当前线程执行，确保日志不丢失
            r.run();
        });
        executor.initialize();
        return executor;
    }
} 