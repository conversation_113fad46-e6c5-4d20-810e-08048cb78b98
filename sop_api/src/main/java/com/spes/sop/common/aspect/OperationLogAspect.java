package com.spes.sop.common.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.spes.sop.common.annotation.OperationLog;
import com.spes.sop.common.security.JwtUserDetails;
import com.spes.sop.log.mapper.model.entity.OperationLogDO;
import com.spes.sop.log.service.OperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Date;

/**
 * 操作日志切面
 * 自动记录所有Controller接口的入参出参
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class OperationLogAspect {

    @Autowired
    private OperationLogService operationLogService;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 切点：controller包下且类名以Controller结尾的类中的方法
     */
    @Pointcut("execution(* com.spes.sop.controller..*Controller.*(..))")
    public void controllerMethods() {
    }

    /**
     * 环绕通知：记录操作日志
     */
    @Around("controllerMethods()")
    public Object logOperation(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        Date requestTime = new Date();

        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes != null ? attributes.getRequest() : null;

        // 获取方法信息
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 获取注解信息（如果有的话）
        OperationLog operationLogAnnotation = method.getAnnotation(OperationLog.class);

        // 构建日志对象
        OperationLogDO.OperationLogDOBuilder logBuilder = OperationLogDO.builder()
                .requestTime(requestTime)
                .className(joinPoint.getTarget().getClass().getName())
                .methodName(method.getName())
                .createTime(new Date());

        // 获取用户信息
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getPrincipal() instanceof JwtUserDetails) {
                JwtUserDetails userDetails = (JwtUserDetails) authentication.getPrincipal();
                logBuilder.userId(userDetails.getUserId())
                        .username(userDetails.getUsername());
            }
        } catch (Exception e) {
            log.debug("获取用户信息失败", e);
        }

        // 获取请求信息
        if (request != null) {
            logBuilder.ipAddress(getClientIpAddress(request))
                    .requestMethod(request.getMethod())
                    .requestUrl(request.getRequestURL().toString())
                    .requestPath(request.getRequestURI())
                    .userAgent(request.getHeader("User-Agent"));
        }

        // 记录请求参数
        boolean recordParams = operationLogAnnotation == null || operationLogAnnotation.recordParams();
        if (recordParams) {
            try {
                Object[] args = joinPoint.getArgs();
                if (args != null && args.length > 0) {
                    // 过滤掉HttpServletRequest等不需要序列化的参数
                    Object[] filteredArgs = filterSensitiveArgs(args);
                    String requestParams = objectMapper.writeValueAsString(filteredArgs);
                    logBuilder.requestParams(requestParams);
                }
            } catch (Exception e) {
                log.debug("序列化请求参数失败", e);
                logBuilder.requestParams("序列化失败");
            }
        }

        // 设置操作描述
        if (operationLogAnnotation != null && !operationLogAnnotation.value().isEmpty()) {
            logBuilder.operationDesc(operationLogAnnotation.value());
        }

        Object result = null;
        boolean success = true;
        String errorMessage = null;

        try {
            // 执行原方法
            result = joinPoint.proceed();
        } catch (Throwable throwable) {
            success = false;
            errorMessage = throwable.getMessage();
            throw throwable;
        } finally {
            long endTime = System.currentTimeMillis();
            Date responseTime = new Date();
            long executionTime = endTime - startTime;

            // 记录响应结果
            boolean recordResult = operationLogAnnotation == null || operationLogAnnotation.recordResult();
            if (recordResult && result != null && success) {
                try {
                    String responseResult = objectMapper.writeValueAsString(result);
                    // 限制响应结果长度，避免数据库字段溢出
                    if (responseResult.length() > 5000) {
                        responseResult = responseResult.substring(0, 5000) + "...";
                    }
                    logBuilder.responseResult(responseResult);
                } catch (Exception e) {
                    log.debug("序列化响应结果失败", e);
                    logBuilder.responseResult("序列化失败");
                }
            }

            // 完善日志信息
            OperationLogDO operationLog = logBuilder
                    .responseTime(responseTime)
                    .executionTime(executionTime)
                    .success(success ? 1 : 0)
                    .errorMessage(errorMessage)
                    .build();

            // 异步保存日志
            operationLogService.saveOperationLog(operationLog);
        }

        return result;
    }

    /**
     * 过滤敏感参数
     */
    private Object[] filterSensitiveArgs(Object[] args) {
        if (args == null) {
            return null;
        }

        Object[] filteredArgs = new Object[args.length];
        for (int i = 0; i < args.length; i++) {
            Object arg = args[i];
            // 过滤掉HttpServletRequest、HttpServletResponse等不需要序列化的对象
            if (arg instanceof HttpServletRequest ||
                    arg instanceof javax.servlet.http.HttpServletResponse ||
                    arg instanceof org.springframework.web.multipart.MultipartFile) {
                filteredArgs[i] = arg.getClass().getSimpleName();
            } else {
                filteredArgs[i] = arg;
            }
        }
        return filteredArgs;
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        // 如果通过代理，可能会有多个IP，取第一个
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        return ip;
    }
} 