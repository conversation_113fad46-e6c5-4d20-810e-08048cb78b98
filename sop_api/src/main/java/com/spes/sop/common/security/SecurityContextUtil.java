package com.spes.sop.common.security;

import com.spes.sop.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StringUtils;

import java.util.Optional;

/**
 * Spring Security上下文工具类
 * 提供便捷的用户信息获取方法
 *
 * <AUTHOR>

 */
@Slf4j
public class SecurityContextUtil {

    /**
     * 获取当前认证信息
     *
     * @return 认证信息的Optional包装
     */
    public static Optional<Authentication> getCurrentAuthentication() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            return Optional.ofNullable(authentication);
        } catch (Exception e) {
            log.debug("获取当前认证信息失败：{}", e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * 获取当前用户详情
     *
     * @return 用户详情的Optional包装
     */
    public static Optional<JwtUserDetails> getCurrentUserDetails() {
        return getCurrentAuthentication()
                .filter(auth -> auth.getPrincipal() instanceof JwtUserDetails)
                .map(auth -> (JwtUserDetails) auth.getPrincipal());
    }

    /**
     * 获取当前用户ID
     *
     * @return 用户ID，如果未登录则抛出异常
     * @throws BusinessException 用户未登录时抛出
     */
    public static Long getCurrentUserId() {
        return getCurrentUserDetails()
                .map(JwtUserDetails::getUserId)
                .orElseThrow(() -> {
                    log.warn("获取当前用户ID失败，用户未登录或认证信息不完整");
                    return new BusinessException("用户未登录，请先登录");
                });
    }

    /**
     * 获取当前用户ID（可选）
     *
     * @return 用户ID的Optional包装，如果未登录则返回空Optional
     */
    public static Optional<Long> getCurrentUserIdOptional() {
        return getCurrentUserDetails()
                .map(JwtUserDetails::getUserId);
    }

    /**
     * 获取当前用户名
     *
     * @return 用户名，如果未登录则返回null
     */
    public static String getCurrentUsername() {
        return getCurrentUserDetails()
                .map(JwtUserDetails::getUsername)
                .orElse(null);
    }



    /**
     * 检查当前用户是否已登录
     *
     * @return 是否已登录
     */
    public static boolean isAuthenticated() {
        return getCurrentAuthentication()
                .map(Authentication::isAuthenticated)
                .orElse(false);
    }

    /**
     * 检查当前用户是否具有指定角色
     *
     * @param role 角色名称（不需要ROLE_前缀）
     * @return 是否具有该角色
     */
    public static boolean hasRole(String role) {
        if (!StringUtils.hasText(role)) {
            return false;
        }

        return getCurrentUserDetails()
                .map(userDetails -> userDetails.hasRole(role))
                .orElse(false);
    }

    /**
     * 检查当前用户是否具有指定权限
     *
     * @param authority 权限名称
     * @return 是否具有该权限
     */
    public static boolean hasAuthority(String authority) {
        if (!StringUtils.hasText(authority)) {
            return false;
        }

        return getCurrentUserDetails()
                .map(userDetails -> userDetails.hasAuthority(authority))
                .orElse(false);
    }

    /**
     * 检查当前用户是否具有任意一个指定角色
     *
     * @param roles 角色名称数组
     * @return 是否具有任意一个角色
     */
    public static boolean hasAnyRole(String... roles) {
        if (roles == null || roles.length == 0) {
            return false;
        }

        for (String role : roles) {
            if (hasRole(role)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查当前用户是否具有任意一个指定权限
     *
     * @param authorities 权限名称数组
     * @return 是否具有任意一个权限
     */
    public static boolean hasAnyAuthority(String... authorities) {
        if (authorities == null || authorities.length == 0) {
            return false;
        }

        for (String authority : authorities) {
            if (hasAuthority(authority)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取当前用户的所有角色（逗号分隔的字符串）
     *
     * @return 角色字符串，如果未设置则返回null
     */
    public static String getCurrentUserRoles() {
        return getCurrentUserDetails()
                .map(JwtUserDetails::getRoles)
                .orElse(null);
    }

    /**
     * 获取当前用户工号
     *
     * @return 工号，如果未登录返回null
     */
    public static String getCurrentEmployeeId() {
        return getCurrentUserDetails()
                .map(JwtUserDetails::getEmployeeId)
                .orElse(null);
    }

    /**
     * 获取当前用户部门
     *
     * @return 部门，如果未登录返回null
     */
    public static String getCurrentDepartment() {
        return getCurrentUserDetails()
                .map(JwtUserDetails::getDepartment)
                .orElse(null);
    }

    // ==================== 兼容UserContext的宽松模式方法 ====================

    /**
     * 获取当前用户ID（宽松模式）
     * 兼容原UserContext.getCurrentUserId()方法
     * 未登录时返回默认值而不抛异常
     *
     * @return 用户ID，如果未登录返回默认值1L
     */
    public static Long getCurrentUserIdLenient() {
        try {
            return getCurrentUserDetails()
                    .map(JwtUserDetails::getUserId)
                    .orElse(1L); // 开发环境默认用户ID
        } catch (Exception e) {
            log.error("获取当前用户ID失败", e);
            return 1L; // 开发环境默认用户ID
        }
    }

    /**
     * 获取当前用户名（宽松模式）
     * 兼容原UserContext.getCurrentUsername()方法
     * 未登录时返回默认值而不抛异常
     *
     * @return 用户名，如果未登录返回默认值"admin"
     */
    public static String getCurrentUsernameLenient() {
        try {
            return getCurrentUserDetails()
                    .map(JwtUserDetails::getUsername)
                    .orElse("admin"); // 开发环境默认用户名
        } catch (Exception e) {
            log.error("获取当前用户名失败", e);
            return "admin"; // 开发环境默认用户名
        }
    }

    /**
     * 获取当前用户角色（宽松模式）
     * 兼容原UserContext.getCurrentUserRoles()方法
     * 未登录时返回默认值而不抛异常
     *
     * @return 用户角色，如果未登录返回默认值"ADMIN,USER"
     */
    public static String getCurrentUserRolesLenient() {
        try {
            return getCurrentUserDetails()
                    .map(JwtUserDetails::getRoles)
                    .orElse("ADMIN,USER"); // 开发环境默认角色
        } catch (Exception e) {
            log.error("获取当前用户角色失败", e);
            return "ADMIN,USER"; // 开发环境默认角色
        }
    }

    /**
     * 获取当前线程的用户上下文信息（用于异步场景）
     * 兼容原UserContext.getCurrentUserContext()方法
     *
     * @return 用户上下文信息
     */
    public static UserContextInfo getCurrentUserContext() {
        return UserContextInfo.builder()
                .userId(getCurrentUserIdLenient())
                .username(getCurrentUsernameLenient())
                .roles(getCurrentUserRolesLenient())
                .employeeId(getCurrentEmployeeId())
                .department(getCurrentDepartment())
                .authenticated(isAuthenticated())
                .build();
    }

    /**
     * 用户上下文信息类
     * 用于在异步场景中传递用户信息
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class UserContextInfo {
        private Long userId;
        private String username;
        private String roles;
        private String employeeId;
        private String department;
        private boolean authenticated;
    }
} 
