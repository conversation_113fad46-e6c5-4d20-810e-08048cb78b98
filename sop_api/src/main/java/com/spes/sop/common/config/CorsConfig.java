package com.spes.sop.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;
import java.util.Collections;

/**
 * 跨域配置类
 *
 * <AUTHOR>

 */
@Slf4j
@Configuration
public class CorsConfig {

    /**
     * 允许的源地址（从配置文件读取，支持多个地址用逗号分隔）
     */
    @Value("${app.cors.allowed-origins:http://localhost:3000,http://localhost:8080,http://127.0.0.1:3000,http://127.0" +
            ".0.1:8080}")
    private String allowedOrigins;

    /**
     * 允许的请求方法
     */
    @Value("${app.cors.allowed-methods:GET,POST,PUT,DELETE,OPTIONS,PATCH}")
    private String allowedMethods;

    /**
     * 允许的请求头
     */
    @Value("${app.cors.allowed-headers:*}")
    private String allowedHeaders;

    /**
     * 是否允许携带凭证（Cookie等）
     */
    @Value("${app.cors.allow-credentials:true}")
    private boolean allowCredentials;

    /**
     * 预检请求的缓存时间（秒）
     */
    @Value("${app.cors.max-age:3600}")
    private long maxAge;

    /**
     * 配置CORS
     *
     * @return CorsConfigurationSource
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        log.info("配置CORS跨域支持");

        CorsConfiguration configuration = new CorsConfiguration();

        // 设置允许的源地址
        if ("*".equals(allowedOrigins.trim())) {
            configuration.setAllowedOriginPatterns(Collections.singletonList("*"));
            log.info("CORS配置：允许所有源地址访问");
        } else {
            String[] origins = allowedOrigins.split(",");
            for (int i = 0; i < origins.length; i++) {
                origins[i] = origins[i].trim();
            }
            configuration.setAllowedOrigins(Arrays.asList(origins));
            log.info("CORS配置：允许的源地址 = {}", Arrays.toString(origins));
        }

        // 设置允许的请求方法
        String[] methods = allowedMethods.split(",");
        for (int i = 0; i < methods.length; i++) {
            methods[i] = methods[i].trim();
        }
        configuration.setAllowedMethods(Arrays.asList(methods));
        log.info("CORS配置：允许的请求方法 = {}", Arrays.toString(methods));

        // 设置允许的请求头
        if ("*".equals(allowedHeaders.trim())) {
            configuration.setAllowedHeaders(Collections.singletonList("*"));
            log.info("CORS配置：允许所有请求头");
        } else {
            String[] headers = allowedHeaders.split(",");
            for (int i = 0; i < headers.length; i++) {
                headers[i] = headers[i].trim();
            }
            configuration.setAllowedHeaders(Arrays.asList(headers));
            log.info("CORS配置：允许的请求头 = {}", Arrays.toString(headers));
        }

        // 设置是否允许携带凭证
        configuration.setAllowCredentials(allowCredentials);
        log.info("CORS配置：允许携带凭证 = {}", allowCredentials);

        // 设置预检请求的缓存时间
        configuration.setMaxAge(maxAge);
        log.info("CORS配置：预检请求缓存时间 = {} 秒", maxAge);

        // 设置暴露的响应头（客户端可以访问的响应头）
        configuration.setExposedHeaders(Arrays.asList(
                "Authorization",
                "X-Token-Expiring",
                "X-Total-Count",
                "X-Request-Id",
                "Content-Disposition"
        ));
        log.info("CORS配置：暴露的响应头 = {}", configuration.getExposedHeaders());

        // 应用到所有路径
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);

        log.info("CORS跨域配置完成");
        return source;
    }
} 
