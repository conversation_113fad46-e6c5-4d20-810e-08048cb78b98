package com.spes.sop.common.security;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * JWT认证过滤器
 * 基于Spring Security的OncePerRequestFilter实现
 *
 * <AUTHOR>

 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtTokenUtil jwtTokenUtil;

    /**
     * Authorization请求头名称
     */
    private static final String AUTHORIZATION_HEADER = "Authorization";

    /**
     * Bearer Token前缀
     */
    private static final String BEARER_PREFIX = "Bearer ";

    /**
     * 是否启用开发模式（从配置文件读取）
     */
    @Value("${app.dev.enabled:false}")
    private boolean devModeEnabled;

    /**
     * 开发模式下的模拟用户ID
     */
    @Value("${app.dev.mock-user-id:1}")
    private Long mockUserId;

    /**
     * 开发模式下的模拟用户名
     */
    @Value("${app.dev.mock-username:admin}")
    private String mockUsername;

    /**
     * 开发模式下的模拟用户角色
     */
    @Value("${app.dev.mock-user-roles:ADMIN,USER}")
    private String mockUserRoles;

    @Value("${app.dev.mock-department:开发部门}")
    private String mockDepartment;

    @Value("${app.dev.mock-employee-Id:H-1000}")
    private String mockEmployeeId;


    /**
     * 不需要认证的路径列表
     */
    private static final List<String> EXCLUDE_PATHS = Arrays.asList(
            "/api/auth/login",
            "/api/health",
            "/actuator"
    );

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
                                    FilterChain filterChain) throws ServletException, IOException {

        String requestPath = request.getRequestURI();
        log.debug("处理请求：{} {}", request.getMethod(), requestPath);

        try {
            // 检查是否为不需要认证的路径
            if (isExcludePath(requestPath)) {
                log.debug("路径 {} 无需认证，跳过Token验证", requestPath);
                filterChain.doFilter(request, response);
                return;
            }

            // 开发模式下使用模拟用户
            if (devModeEnabled) {
                setMockAuthentication(request);
                log.debug("开发模式：使用模拟用户，用户ID：{}，用户名：{}", mockUserId, mockUsername);
                filterChain.doFilter(request, response);
                return;
            }

            // 从请求头中提取Token
            String token = extractTokenFromRequest(request);
            if (!StringUtils.hasText(token)) {
                log.warn("请求 {} 缺少Authorization头或Token格式不正确", requestPath);
                sendUnauthorizedResponse(response, "未提供有效的认证Token");
                return;
            }

            // 解析Token获取用户信息
            JwtTokenUtil.UserTokenInfo tokenInfo = jwtTokenUtil.validateAccessToken(token);
            if (tokenInfo == null) {
                log.warn("请求 {} 的Token无效或已过期", requestPath);
                sendUnauthorizedResponse(response, "Token无效或已过期");
                return;
            }

            // 设置Spring Security认证信息
            setAuthentication(request, tokenInfo);

            log.debug("成功设置Spring Security认证信息，用户ID：{}，用户名：{}",
                    tokenInfo.getUserId(), tokenInfo.getUsername());

            // 检查Token是否即将过期，如果是则在响应头中添加提示
            if (jwtTokenUtil.isTokenExpiringSoon(token)) {
                response.setHeader("X-Token-Expiring", "true");
                log.debug("Token即将过期，已在响应头中添加提示");
            }

            filterChain.doFilter(request, response);

        } catch (Exception e) {
            log.error("JWT认证过滤器处理失败", e);
            sendErrorResponse(response, "认证处理失败");
        }
    }

    /**
     * 从请求中提取JWT Token
     *
     * @param request HTTP请求
     * @return JWT Token，如果不存在或格式不正确返回null
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        String authHeader = request.getHeader(AUTHORIZATION_HEADER);

        if (!StringUtils.hasText(authHeader)) {
            log.debug("请求头中未找到Authorization字段");
            return null;
        }

        if (!authHeader.startsWith(BEARER_PREFIX)) {
            log.debug("Authorization头格式不正确，应以 '{}' 开头", BEARER_PREFIX);
            return null;
        }

        String token = authHeader.substring(BEARER_PREFIX.length()).trim();
        if (!StringUtils.hasText(token)) {
            log.debug("从Authorization头中提取的Token为空");
            return null;
        }

        return token;
    }

    /**
     * 检查路径是否在排除列表中
     *
     * @param requestPath 请求路径
     * @return 是否需要排除
     */
    private boolean isExcludePath(String requestPath) {
        if (!StringUtils.hasText(requestPath)) {
            return false;
        }

        return EXCLUDE_PATHS.stream()
                .anyMatch(excludePath -> requestPath.startsWith(excludePath));
    }

    /**
     * 设置Spring Security认证信息
     *
     * @param request   HTTP请求
     * @param tokenInfo Token信息
     */
    private void setAuthentication(HttpServletRequest request, JwtTokenUtil.UserTokenInfo tokenInfo) {
        // 创建JwtUserDetails
        JwtUserDetails userDetails = JwtUserDetails.builder()
                .userId(tokenInfo.getUserId())
                .username(tokenInfo.getUsername())
                .roles(tokenInfo.getRoles())
                .employeeId(tokenInfo.getEmployeeId())
                .department(tokenInfo.getDepartment())
                .weaverId(tokenInfo.getWeaverId())
                .enabled(true)
                .accountNonExpired(true)
                .accountNonLocked(true)
                .credentialsNonExpired(true)
                .build();

        // 创建认证对象
        UsernamePasswordAuthenticationToken authentication =
                new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

        // 设置到SecurityContext
        SecurityContextHolder.getContext().setAuthentication(authentication);
    }

    /**
     * 设置开发模式下的模拟认证信息
     *
     * @param request HTTP请求
     */
    private void setMockAuthentication(HttpServletRequest request) {
        JwtUserDetails mockUserDetails = JwtUserDetails.builder()
                .userId(mockUserId)
                .username(mockUsername)
                .roles(mockUserRoles)
                .department(mockDepartment)
                .employeeId(mockEmployeeId)
                .enabled(true)
                .accountNonExpired(true)
                .accountNonLocked(true)
                .credentialsNonExpired(true)
                .build();

        UsernamePasswordAuthenticationToken authentication =
                new UsernamePasswordAuthenticationToken(mockUserDetails, null, mockUserDetails.getAuthorities());
        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

        SecurityContextHolder.getContext().setAuthentication(authentication);
    }

    /**
     * 发送未授权响应
     *
     * @param response HTTP响应
     * @param message  错误消息
     */
    private void sendUnauthorizedResponse(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write(String.format("{\"code\":401,\"message\":\"%s\"}", message));
    }

    /**
     * 发送错误响应
     *
     * @param response HTTP响应
     * @param message  错误消息
     */
    private void sendErrorResponse(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write(String.format("{\"code\":500,\"message\":\"%s\"}", message));
    }
} 
