package com.spes.sop.common.constant;

/**
 * 排序常量类
 * 定义系统中所有排序相关的常量，避免使用魔法值
 *
 * <AUTHOR>
 */
public final class SortConstants {

    private SortConstants() {
        // 私有构造函数，防止实例化
    }

    /**
     * 排序方向常量
     */
    public static final class Direction {
        /**
         * 升序
         */
        public static final String ASC = "ASC";
        /**
         * 降序
         */
        public static final String DESC = "DESC";

        private Direction() {
        }
    }

    /**
     * 通用排序字段常量
     */
    public static final class CommonFields {
        /**
         * 创建时间
         */
        public static final String CREATE_TIME = "create_time";
        /**
         * 更新时间
         */
        public static final String UPDATE_TIME = "update_time";
        /**
         * 主键ID
         */
        public static final String ID = "id";

        private CommonFields() {
        }
    }


    /**
     * 默认排序配置
     */
    public static final class Defaults {
        /**
         * 默认排序字段
         */
        public static final String ORDER_BY = CommonFields.CREATE_TIME;
        /**
         * 默认排序方向
         */
        public static final String ORDER_DIRECTION = Direction.DESC;

        private Defaults() {
        }
    }

} 