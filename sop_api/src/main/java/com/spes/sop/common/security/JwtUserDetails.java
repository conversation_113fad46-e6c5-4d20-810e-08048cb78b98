package com.spes.sop.common.security;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.stream.Collectors;

/**
 * JWT用户详情类
 * 实现Spring Security的UserDetails接口
 *
 * <AUTHOR>

 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JwtUserDetails implements UserDetails {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码（JWT认证中不需要）
     */
    private String password;

    /**
     * 用户角色（逗号分隔）
     */
    private String roles;

    /**
     * 部门ID
     */
    private String employeeId;

    /**
     * 泛微 id
     */
    private Long weaverId;

    /**
     * 部门名称
     */
    private String department;

    /**
     * 账户是否未过期
     */
    @Builder.Default
    private boolean accountNonExpired = true;

    /**
     * 账户是否未锁定
     */
    @Builder.Default
    private boolean accountNonLocked = true;

    /**
     * 凭证是否未过期
     */
    @Builder.Default
    private boolean credentialsNonExpired = true;

    /**
     * 账户是否启用
     */
    @Builder.Default
    private boolean enabled = true;

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        if (!StringUtils.hasText(roles)) {
            return Collections.emptyList();
        }

        return Arrays.stream(roles.split(","))
                .map(String::trim)
                .filter(StringUtils::hasText)
                .map(role -> {
                    // 如果角色不以ROLE_开头，则添加ROLE_前缀
                    if (!role.startsWith("ROLE_")) {
                        return new SimpleGrantedAuthority("ROLE_" + role);
                    }
                    return new SimpleGrantedAuthority(role);
                })
                .collect(Collectors.toList());
    }

    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public String getUsername() {
        return username;
    }

    @Override
    public boolean isAccountNonExpired() {
        return accountNonExpired;
    }

    @Override
    public boolean isAccountNonLocked() {
        return accountNonLocked;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return credentialsNonExpired;
    }

    @Override
    public boolean isEnabled() {
        return enabled;
    }

    /**
     * 检查用户是否具有指定角色
     *
     * @param role 角色名称
     * @return 是否具有该角色
     */
    public boolean hasRole(String role) {
        if (!StringUtils.hasText(roles) || !StringUtils.hasText(role)) {
            return false;
        }

        String normalizedRole = role.startsWith("ROLE_") ? role.substring(5) : role;
        return Arrays.stream(roles.split(","))
                .map(String::trim)
                .anyMatch(r -> r.equalsIgnoreCase(normalizedRole));
    }

    /**
     * 检查用户是否具有指定权限
     *
     * @param authority 权限名称
     * @return 是否具有该权限
     */
    public boolean hasAuthority(String authority) {
        return getAuthorities().stream()
                .anyMatch(auth -> auth.getAuthority().equals(authority));
    }
} 
