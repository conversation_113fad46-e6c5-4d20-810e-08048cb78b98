package com.spes.sop.common.security;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.crypto.SecretKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * JWT Token 工具类
 * 负责JWT Token的生成、解析和验证
 * 支持Token版本控制和刷新Token机制
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class JwtTokenUtil {

    /**
     * JWT密钥（从配置文件读取）
     */
    @Value("${app.jwt.secret:spes-sop-default-secret-key-for-jwt-token-generation-and-validation}")
    private String jwtSecret;

    /**
     * 访问Token过期时间（秒）
     */
    @Value("${app.jwt.access-token-expiration:86400}")
    private Long accessTokenExpiration;

    /**
     * 刷新Token过期时间（秒）
     */
    @Value("${app.jwt.refresh-token-expiration:604800}")
    private Long refreshTokenExpiration;

    /**
     * 用户Token版本管理服务
     */
    private final ConcurrentMap<Long, Long> userTokenVersions = new ConcurrentHashMap<>();

    /**
     * JWT签名密钥
     */
    private SecretKey secretKey;

    /**
     * Token类型：访问Token
     */
    private static final String TOKEN_TYPE_ACCESS = "access";

    /**
     * Token类型：刷新Token
     */
    private static final String TOKEN_TYPE_REFRESH = "refresh";

    /**
     * 初始化密钥
     */
    @PostConstruct
    public void init() {
        // 验证密钥长度是否符合HS512要求
        validateJwtSecret();
        
        // 使用HMAC-SHA算法生成密钥
        this.secretKey = Keys.hmacShaKeyFor(jwtSecret.getBytes());
        log.info("JWT工具类初始化完成，访问Token过期时间：{}秒，刷新Token过期时间：{}秒",
                accessTokenExpiration, refreshTokenExpiration);
    }

    /**
     * 验证JWT密钥是否符合安全要求
     */
    private void validateJwtSecret() {
        if (jwtSecret == null || jwtSecret.trim().isEmpty()) {
            throw new IllegalArgumentException("JWT密钥不能为空");
        }

        byte[] keyBytes = jwtSecret.getBytes();
        int keyBits = keyBytes.length * 8;

        if (keyBits < 512) {
            String errorMsg = String.format(
                    "JWT密钥长度不足！当前：%d 位，HS512算法要求：至少 512 位。" +
                            "请使用 JwtKeyGenerator.generateSecureKey() 生成符合要求的密钥。", keyBits);
            log.error(errorMsg);
            throw new IllegalArgumentException(errorMsg);
        }

        log.info("JWT密钥验证通过，长度：{} 位 ({} 字节)", keyBits, keyBytes.length);
    }

    /**
     * 生成访问Token
     *
     * @param userId     用户ID
     * @param username   用户名
     * @param roles      用户角色（逗号分隔）
     * @param employeeId 工号
     * @param department 部门
     * @return JWT Token
     */
    public String generateAccessToken(Long userId, String username, String roles,
                                      String employeeId, String department, Long weaverId) {
        // 生成新的Token版本号
        Long tokenVersion = System.currentTimeMillis();
        // 保存版本号到内存中
        userTokenVersions.put(userId, tokenVersion);
        return generateAccessToken(userId, username, roles, employeeId, department, tokenVersion, weaverId);
    }

    /**
     * 生成访问Token（带版本号）
     *
     * @param userId       用户ID
     * @param username     用户名
     * @param roles        用户角色（逗号分隔）
     * @param employeeId   工号
     * @param department   部门
     * @param tokenVersion Token版本号（用于失效控制）
     * @return JWT Token
     */
    public String generateAccessToken(Long userId, String username, String roles,
                                      String employeeId, String department, Long tokenVersion, Long weaverId) {
        Objects.requireNonNull(userId, "用户ID不能为空");
        Objects.requireNonNull(username, "用户名不能为空");
        Objects.requireNonNull(tokenVersion, "Token版本号不能为空");

        // 确保版本号已保存
        userTokenVersions.put(userId, tokenVersion);

        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("username", username);
        claims.put("roles", roles);
        claims.put("employeeId", employeeId);
        claims.put("department", department);
        claims.put("tokenType", TOKEN_TYPE_ACCESS);
        claims.put("tokenVersion", tokenVersion);
        claims.put("weaverId", weaverId);

        log.debug("生成访问Token，用户ID：{}，版本号：{}", userId, tokenVersion);
        return generateToken(claims, accessTokenExpiration);
    }

    /**
     * 生成刷新Token
     *
     * @param userId       用户ID
     * @param username     用户名
     * @param tokenVersion Token版本号
     * @return 刷新Token
     */
    public String generateRefreshToken(Long userId, String username, Long tokenVersion) {
        Objects.requireNonNull(userId, "用户ID不能为空");
        Objects.requireNonNull(username, "用户名不能为空");
        Objects.requireNonNull(tokenVersion, "Token版本号不能为空");

        // 确保版本号已保存
        userTokenVersions.put(userId, tokenVersion);

        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("username", username);
        claims.put("tokenType", TOKEN_TYPE_REFRESH);
        claims.put("tokenVersion", tokenVersion);

        log.debug("生成刷新Token，用户ID：{}，版本号：{}", userId, tokenVersion);
        return generateToken(claims, refreshTokenExpiration);
    }

    /**
     * 从Token中解析用户信息
     *
     * @param token JWT Token
     * @return 用户信息，解析失败返回null
     */
    public UserTokenInfo parseToken(String token) {
        if (!StringUtils.hasText(token)) {
            log.debug("Token为空，无法解析");
            return null;
        }

        try {
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(secretKey)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();

            // 检查Token是否过期
            if (claims.getExpiration().before(new Date())) {
                log.debug("Token已过期");
                return null;
            }

            // 构建用户信息
            UserTokenInfo userInfo = new UserTokenInfo();
            userInfo.setUserId(getLongFromClaims(claims, "userId"));
            userInfo.setUsername(claims.get("username", String.class));
            userInfo.setRoles(claims.get("roles", String.class));
            userInfo.setEmployeeId(claims.get("employeeId", String.class));
            userInfo.setDepartment(claims.get("department", String.class));
            userInfo.setWeaverId(getLongFromClaims(claims, "weaverId"));
            userInfo.setTokenType(claims.get("tokenType", String.class));
            userInfo.setTokenVersion(getLongFromClaims(claims, "tokenVersion"));
            userInfo.setExpiration(claims.getExpiration());
            userInfo.setIssuedAt(claims.getIssuedAt());

            log.debug("成功解析Token，用户ID：{}，用户名：{}，Token类型：{}，版本：{}",
                    userInfo.getUserId(), userInfo.getUsername(), userInfo.getTokenType(), userInfo.getTokenVersion());
            return userInfo;

        } catch (ExpiredJwtException e) {
            log.debug("Token已过期：{}", e.getMessage());
            return null;
        } catch (UnsupportedJwtException e) {
            log.warn("不支持的Token格式：{}", e.getMessage());
            return null;
        } catch (MalformedJwtException e) {
            log.warn("Token格式错误：{}", e.getMessage());
            return null;
        } catch (SecurityException e) {
            log.warn("Token签名验证失败：{}", e.getMessage());
            return null;
        } catch (IllegalArgumentException e) {
            log.warn("Token参数错误：{}", e.getMessage());
            return null;
        } catch (Exception e) {
            log.error("解析Token时发生未知错误", e);
            return null;
        }
    }

    /**
     * 验证访问Token
     *
     * @param token JWT Token
     * @return 用户信息，验证失败返回null
     */
    public UserTokenInfo validateAccessToken(String token) {
        UserTokenInfo tokenInfo = parseToken(token);
        if (tokenInfo == null) {
            return null;
        }

        // 检查Token类型
        if (!TOKEN_TYPE_ACCESS.equals(tokenInfo.getTokenType())) {
            log.warn("Token类型不正确，期望：{}，实际：{}", TOKEN_TYPE_ACCESS, tokenInfo.getTokenType());
            return null;
        }

        // 检查Token版本是否有效
        if (!isTokenVersionValid(tokenInfo.getUserId(), tokenInfo.getTokenVersion())) {
            log.warn("Token版本无效，用户ID：{}，Token版本：{}，当前版本：{}",
                    tokenInfo.getUserId(), tokenInfo.getTokenVersion(),
                    userTokenVersions.get(tokenInfo.getUserId()));
            return null;
        }

        return tokenInfo;
    }

    /**
     * 验证刷新Token
     *
     * @param token 刷新Token
     * @return 用户信息，验证失败返回null
     */
    public UserTokenInfo validateRefreshToken(String token) {
        UserTokenInfo tokenInfo = parseToken(token);
        if (tokenInfo == null) {
            return null;
        }

        // 检查Token类型
        if (!TOKEN_TYPE_REFRESH.equals(tokenInfo.getTokenType())) {
            log.warn("Token类型不正确，期望：{}，实际：{}", TOKEN_TYPE_REFRESH, tokenInfo.getTokenType());
            return null;
        }

        // 检查Token版本是否有效
        if (!isTokenVersionValid(tokenInfo.getUserId(), tokenInfo.getTokenVersion())) {
            log.warn("刷新Token版本无效，用户ID：{}，Token版本：{}，当前版本：{}",
                    tokenInfo.getUserId(), tokenInfo.getTokenVersion(),
                    userTokenVersions.get(tokenInfo.getUserId()));
            return null;
        }

        return tokenInfo;
    }

    /**
     * 检查Token是否即将过期（剩余时间少于1小时）
     *
     * @param token JWT Token
     * @return 是否即将过期
     */
    public boolean isTokenExpiringSoon(String token) {
        UserTokenInfo userInfo = parseToken(token);
        if (userInfo == null || userInfo.getExpiration() == null) {
            return true;
        }

        long remainingTime = userInfo.getExpiration().getTime() - System.currentTimeMillis();
        return remainingTime < 3600000; // 1小时 = 3600000毫秒
    }

    /**
     * 使用户的所有Token失效
     *
     * @param userId 用户ID
     * @return 新的Token版本号
     */
    public Long invalidateAllUserTokens(Long userId) {
        if (userId == null) {
            log.warn("用户ID为空，无法失效Token");
            return null;
        }

        // 生成新的版本号并更新
        Long newVersion = System.currentTimeMillis();
        userTokenVersions.put(userId, newVersion);

        log.info("用户所有Token已失效，用户ID：{}，新版本号：{}", userId, newVersion);
        return newVersion;
    }

    /**
     * 生成Token的通用方法
     *
     * @param claims     声明信息
     * @param expiration 过期时间（秒）
     * @return JWT Token
     */
    private String generateToken(Map<String, Object> claims, Long expiration) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration * 1000);

        return Jwts.builder()
                .setClaims(claims)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(secretKey, SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 从Claims中安全获取Long类型值
     *
     * @param claims Claims对象
     * @param key    键名
     * @return Long值，如果不存在或转换失败返回null
     */
    private Long getLongFromClaims(Claims claims, String key) {
        Object value = claims.get(key);
        if (value == null) {
            return null;
        }

        if (value instanceof Long) {
            return (Long) value;
        }

        if (value instanceof Integer) {
            return ((Integer) value).longValue();
        }

        if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                log.warn("无法将字符串转换为Long：{}", value);
                return null;
            }
        }

        log.warn("无法将类型 {} 转换为Long：{}", value.getClass().getSimpleName(), value);
        return null;
    }

    /**
     * 检查Token版本是否有效
     *
     * @param userId       用户ID
     * @param tokenVersion Token版本号
     * @return 是否有效
     */
    private boolean isTokenVersionValid(Long userId, Long tokenVersion) {
        if (userId == null || tokenVersion == null) {
            log.debug("用户ID或Token版本号为空，验证失败");
            return false;
        }

        // 获取当前用户的Token版本
        Long currentVersion = userTokenVersions.get(userId);

        // 如果用户没有版本记录，但Token中有版本号，说明是首次登录或应用重启，设置版本号
        if (currentVersion == null) {
            log.debug("用户没有Token版本记录，设置为当前Token版本，用户ID：{}，版本：{}", userId, tokenVersion);
            userTokenVersions.put(userId, tokenVersion);
            return true;
        }

        // 检查版本是否匹配
        boolean isValid = currentVersion.equals(tokenVersion);
        log.debug("Token版本验证，用户ID：{}，Token版本：{}，当前版本：{}，验证结果：{}",
                userId, tokenVersion, currentVersion, isValid);

        return isValid;
    }

    /**
     * 用户Token信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserTokenInfo {
        private Long userId;
        private String username;
        private String roles;
        private String employeeId;
        private String department;
        private Long weaverId;
        private String tokenType;
        private Long tokenVersion;
        private Date expiration;
        private Date issuedAt;
    }
} 
