package com.spes.sop.support.config.convert;

import cn.hutool.core.collection.CollUtil;
import com.spes.sop.config.service.model.bo.GoodsCategoryBO;
import com.spes.sop.controller.config.model.vo.CategoryVO;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 分类转换器
 */
public class CategoryConverter {

    /**
     * 转换分类BO为分类VO
     *
     * @param categoryBO 分类BO
     * @return 分类VO
     */
    public static CategoryVO toCategoryVO(GoodsCategoryBO categoryBO) {
        if (categoryBO == null) {
            return null;
        }

        return CategoryVO.builder()
                .id(categoryBO.getId())
                .name(categoryBO.getName())
                .description(categoryBO.getDescription())
                .status(categoryBO.getStatus() != null ? categoryBO.getStatus().name() : null)
                .statusDesc(categoryBO.getStatus() != null ? categoryBO.getStatus().getStatusDesc() : null)
                .createTime(categoryBO.getCreateTime())
                .build();
    }

    /**
     * 转换分类BO列表为分类VO列表
     *
     * @param categoryBOList 分类BO列表
     * @return 分类VO列表
     */
    public static List<CategoryVO> toCategoryVOList(List<GoodsCategoryBO> categoryBOList) {
        if (CollUtil.isEmpty(categoryBOList)) {
            return Collections.emptyList();
        }

        return categoryBOList.stream()
                .map(CategoryConverter::toCategoryVO)
                .collect(Collectors.toList());
    }
} 