package com.spes.sop.support.goods.model.vo;

import com.spes.sop.common.enums.OaStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuListVO {
    /**
     * 主键 id
     */
    private Long id;
    /**
     * 货品编码
     */
    private String skuCode;
    /**
     * SKU名称
     */
    private String skuName;
    /**
     * 条码
     */
    private String barCode;
    /**
     * 产品对应SPU
     */
    private String spuCode;
    private String spuName;
    /**
     * 产品规格
     */
    private Integer spec;
    /**
     * 产品上线时间
     */
    private String onlineTime;
    /**
     * 一级分类
     */
    private String firstClassification;
    /**
     * 二级分类
     */
    private String secondClassification;
    /**
     * 三级分类
     */
    private String thirdClassification;
    /**
     * 系列
     */
    private String series;
    /**
     * 产品分类
     */
    private String category;
    /**
     * 渠道
     */
    private String channel;
    /**
     * 是否赠品
     */
    private Boolean gift;
    /**
     * 是否为包裹卡
     */
    private Boolean card;
    /**
     * 状态
     * @see OaStatusEnum#name()
     */
    private String status;
    /**
     * 状态描述
     * @see OaStatusEnum#getDesc()
     */
    private String statusDesc;
    /**
     * 同步失败原因
     */
    private String syncFail;
    /**
     * 标准价
     */
    private BigDecimal fairPrice;
    /**
     * 底价
     */
    private BigDecimal basePrice;
    /**
     * 成本
     */
    private BigDecimal cost;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人
     */
    private String creatorName;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 更新人
     */
    private String updaterName;
}
