package com.spes.sop.support.config;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.spes.sop.common.exception.BusinessException;
import com.spes.sop.common.page.BasePager;
import com.spes.sop.common.page.PageResult;
import com.spes.sop.common.security.SecurityContextUtil;
import com.spes.sop.config.enums.ConfigStatusEnum;
import com.spes.sop.config.service.GoodsBrandService;
import com.spes.sop.config.service.model.bo.GoodsBrandBO;
import com.spes.sop.config.service.model.command.GoodsBrandCreateCommand;
import com.spes.sop.config.service.model.command.GoodsBrandDeleteCommand;
import com.spes.sop.config.service.model.command.GoodsBrandUpdateCommand;
import com.spes.sop.config.service.model.query.BrandBOPagerQuery;
import com.spes.sop.controller.config.model.query.BrandPagerQuery;
import com.spes.sop.controller.config.model.request.BrandAddRequest;
import com.spes.sop.controller.config.model.request.BrandEditRequest;
import com.spes.sop.controller.config.model.request.BrandEnableRequest;
import com.spes.sop.controller.config.model.vo.BrandVO;
import com.spes.sop.support.config.convert.BrandConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Collections;
import java.util.Objects;

@Component
@RequiredArgsConstructor
@Slf4j
public class BrandSupport {
    private final GoodsBrandService goodsBrandService;

    /**
     * 分页查询品牌列表
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public PageResult<BrandVO> list(@NotNull BrandPagerQuery query) {
        BrandBOPagerQuery queryBuilder = BrandBOPagerQuery.builder()
                .nameSearch(query.getNameSearch())
                .statuses(ObjectUtil.isNotNull(query.getStatuses()) ? Arrays.asList(query.getStatuses()) :
                        Collections.emptyList())
                .pager(new BasePager.Pager(query.getPageNum(), query.getPageSize()))
                .build();
        PageResult<GoodsBrandBO> brands = goodsBrandService.pagerBrands(queryBuilder);
        return PageResult.of(CollUtil.isEmpty(brands.getRecords()) ? Collections.emptyList() :
                        BrandConverter.toBrandVOList(brands.getRecords()),
                brands.getTotal(), query.getPageNum(), query.getPageSize());
    }


    /**
     * 新增品牌
     *
     * @param request 新增请求
     */
    public void add(@NotNull BrandAddRequest request) {
        // 检查名称重复
        if (existsByName(request.getName().trim(), null)) {
            throw new BusinessException("品牌名称已存在：" + request.getName().trim());
        }

        // 获取当前操作用户
        Long currentUserId = SecurityContextUtil.getCurrentUserIdLenient();

        // 构建创建命令
        GoodsBrandCreateCommand command = GoodsBrandCreateCommand.builder()
                .name(request.getName().trim())
                .description(request.getDescription())
                .creatorId(currentUserId)
                .build();
        // 创建品牌
        goodsBrandService.createBrand(command);
    }

    /**
     * 修改品牌
     *
     * @param request 修改请求
     */
    public void edit(@NotNull BrandEditRequest request) {
        // 检查名称重复（排除自身）
        if (StrUtil.isNotBlank(request.getName()) &&
                existsByName(request.getName().trim(), request.getId())) {
            throw new BusinessException("品牌名称已存在：" + request.getName().trim());
        }

        // 获取当前操作用户
        Long currentUserId = SecurityContextUtil.getCurrentUserIdLenient();

        // 构建更新命令
        GoodsBrandUpdateCommand command = GoodsBrandUpdateCommand.builder()
                .id(request.getId())
                .name(StrUtil.isNotBlank(request.getName()) ? request.getName().trim() : null)
                .description(request.getDescription())
                .updaterId(currentUserId)
                .build();

        // 更新品牌
        goodsBrandService.updateBrand(command);
        log.info("修改品牌成功，ID：{}，名称：{}", request.getId(), request.getName());
    }

    /**
     * 启用/禁用品牌
     *
     * @param request 启用/禁用请求
     */
    public void enable(@NotNull BrandEnableRequest request) {
        // 获取当前操作用户
        Long currentUserId = SecurityContextUtil.getCurrentUserIdLenient();

        GoodsBrandBO brand = goodsBrandService.getBrand(request.getId());
        ConfigStatusEnum status = ConfigStatusEnum.valueOf(request.getStatus());
        if (ObjectUtil.isNull(brand) || ObjectUtil.isNull(status)) {
            throw new BusinessException("品牌不存在或状态无效");
        }
        // 构建更新命令
        GoodsBrandUpdateCommand command = GoodsBrandUpdateCommand.builder()
                .id(request.getId())
                .status(status)
                .updaterId(currentUserId)
                .build();
        goodsBrandService.updateBrand(command);
    }

    /**
     * 删除品牌
     *
     * @param id 品牌ID
     */
    public void delete(@NotNull Long id) {
        // 获取当前操作用户
        Long currentUserId = SecurityContextUtil.getCurrentUserIdLenient();

        // 构建删除命令
        GoodsBrandDeleteCommand command = GoodsBrandDeleteCommand.builder()
                .id(id)
                .deleterId(currentUserId)
                .build();

        // 删除品牌
        goodsBrandService.deleteBrand(command);
        log.info("删除品牌成功，ID：{}", id);
    }

    /**
     * 检查品牌名称是否已存在
     *
     * @param name      品牌名称
     * @param excludeId 排除的ID（用于更新时排除自身）
     * @return 是否存在
     */
    public Boolean existsByName(String name, Long excludeId) {
        if (StrUtil.isBlank(name)) {
            return false;
        }

        GoodsBrandBO brand = goodsBrandService.getBrandByName(name);
        if (ObjectUtil.isNull(brand)) {
            return false;
        }
        if (Objects.equals(brand.getId(), excludeId)) {
            return false;
        }
        return true;
    }

}
