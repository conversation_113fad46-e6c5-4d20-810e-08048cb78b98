package com.spes.sop.support.config.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ClassificationAddRequest {
    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空")
    private String name;
    /**
     * 父级分类ID
     */
    private Long parentId;
    /**
     * 级别
     */
    @NotNull(message = "级别不能为空")
    @Max(value = 4, message = "级别不能大于4")
    private Integer level;
}
