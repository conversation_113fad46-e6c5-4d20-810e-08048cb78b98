package com.spes.sop.support.config;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.spes.sop.common.exception.BusinessException;
import com.spes.sop.common.page.BasePager;
import com.spes.sop.common.page.PageResult;
import com.spes.sop.common.security.SecurityContextUtil;
import com.spes.sop.config.enums.ConfigStatusEnum;
import com.spes.sop.config.service.GoodsChannelService;
import com.spes.sop.config.service.model.bo.GoodsChannelBO;
import com.spes.sop.config.service.model.command.GoodsChannelCreateCommand;
import com.spes.sop.config.service.model.command.GoodsChannelDeleteCommand;
import com.spes.sop.config.service.model.command.GoodsChannelUpdateCommand;
import com.spes.sop.config.service.model.query.ChannelBOPagerQuery;
import com.spes.sop.controller.config.model.query.ChannelPagerQuery;
import com.spes.sop.controller.config.model.request.ChannelAddRequest;
import com.spes.sop.controller.config.model.request.ChannelEditRequest;
import com.spes.sop.controller.config.model.request.ChannelEnableRequest;
import com.spes.sop.controller.config.model.vo.ChannelVO;
import com.spes.sop.support.config.convert.ChannelConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Collections;
import java.util.Objects;

@Component
@RequiredArgsConstructor
@Slf4j
public class ChannelSupport {
    private final GoodsChannelService goodsChannelService;

    /**
     * 分页查询渠道列表
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public PageResult<ChannelVO> list(@NotNull ChannelPagerQuery query) {
        ChannelBOPagerQuery queryBuilder = ChannelBOPagerQuery.builder()
                .nameSearch(query.getNameSearch())
                .statuses(ObjectUtil.isNotNull(query.getStatuses()) ? Arrays.asList(query.getStatuses()) :
                        Collections.emptyList())
                .pager(new BasePager.Pager(query.getPageNum(), query.getPageSize()))
                .build();
        PageResult<GoodsChannelBO> channels = goodsChannelService.pager(queryBuilder);
        return PageResult.of(CollUtil.isEmpty(channels.getRecords()) ? Collections.emptyList() :
                        ChannelConverter.toChannelVOList(channels.getRecords()),
                channels.getTotal(), query.getPageNum(), query.getPageSize());
    }

    /**
     * 新增渠道
     *
     * @param request 新增请求
     */
    public void add(@NotNull ChannelAddRequest request) {
        // 检查名称重复
        if (existsByName(request.getName().trim(), null)) {
            throw new BusinessException("渠道名称已存在：" + request.getName().trim());
        }

        // 获取当前操作用户
        Long currentUserId = SecurityContextUtil.getCurrentUserIdLenient();

        // 构建创建命令
        GoodsChannelCreateCommand command = GoodsChannelCreateCommand.builder()
                .name(request.getName().trim())
                .description(request.getDescription())
                .creatorId(currentUserId)
                .build();

        // 创建渠道
        Long channelId = goodsChannelService.createChannel(command);
        log.info("新增渠道成功，ID：{}，名称：{}", channelId, request.getName());
    }

    /**
     * 修改渠道
     *
     * @param request 修改请求
     */
    public void edit(@NotNull ChannelEditRequest request) {
        // 检查名称重复（排除自身）
        if (StrUtil.isNotBlank(request.getName()) &&
                existsByName(request.getName().trim(), request.getId())) {
            throw new BusinessException("渠道名称已存在：" + request.getName().trim());
        }

        // 获取当前操作用户
        Long currentUserId = SecurityContextUtil.getCurrentUserIdLenient();

        // 构建更新命令
        GoodsChannelUpdateCommand command = GoodsChannelUpdateCommand.builder()
                .id(request.getId())
                .name(StrUtil.isNotBlank(request.getName()) ? request.getName().trim() : null)
                .description(request.getDescription())
                .updaterId(currentUserId)
                .build();

        // 更新渠道
        goodsChannelService.updateChannel(command);
        log.info("修改渠道成功，ID：{}，名称：{}", request.getId(), request.getName());
    }

    /**
     * 启用/禁用渠道
     *
     * @param request 启用/禁用请求
     */
    public void enable(@NotNull ChannelEnableRequest request) {
        // 获取当前操作用户
        Long currentUserId = SecurityContextUtil.getCurrentUserIdLenient();

        GoodsChannelBO channel = goodsChannelService.getById(request.getId());
        ConfigStatusEnum status = ConfigStatusEnum.valueOf(request.getStatus());
        if (ObjectUtil.isNull(channel) || ObjectUtil.isNull(status)) {
            throw new BusinessException("渠道不存在或状态无效");
        }
        // 构建更新命令
        GoodsChannelUpdateCommand command = GoodsChannelUpdateCommand.builder()
                .id(request.getId())
                .status(status)
                .updaterId(currentUserId)
                .build();
        goodsChannelService.updateChannel(command);
    }

    /**
     * 删除渠道
     *
     * @param id 渠道ID
     */
    public void delete(@NotNull Long id) {
        // 获取当前操作用户
        Long currentUserId = SecurityContextUtil.getCurrentUserIdLenient();

        // 构建删除命令
        GoodsChannelDeleteCommand command = GoodsChannelDeleteCommand.builder()
                .id(id)
                .deleterId(currentUserId)
                .build();

        // 删除渠道
        goodsChannelService.deleteChannel(command);
        log.info("删除渠道成功，ID：{}", id);
    }

    /**
     * 检查渠道名称是否已存在
     *
     * @param name      渠道名称
     * @param excludeId 排除的ID（用于更新时排除自身）
     * @return 是否存在
     */
    public Boolean existsByName(String name, Long excludeId) {
        if (StrUtil.isBlank(name)) {
            return false;
        }

        GoodsChannelBO channel = goodsChannelService.getByName(name);
        if (ObjectUtil.isNull(channel)) {
            return false;
        }
        if (Objects.equals(channel.getId(), excludeId)) {
            return false;
        }
        return true;
    }
}
