package com.spes.sop.support.goods.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * SPU响应VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpuDetailVO {

    /**
     * SPU ID
     */
    private Long id;

    /**
     * SPU编码
     */
    private String spuCode;

    /**
     * SPU名称
     */
    private String spuName;

    /**
     * 描述
     */
    private String description;

    /**
     * 品牌
     */
    private String brand;
    private Long brandId;

    /**
     * 一级分类
     */
    private String firstClassification;
    private Long firstClassificationId;

    /**
     * 二级分类
     */
    private String secondClassification;
    private Long secondClassificationId;

    /**
     * 三级分类
     */
    private String thirdClassification;
    private Long thirdClassificationId;

    /**
     * 四级分类
     */
    private String fourthClassification;
    private Long fourthClassificationId;

    /**
     * 渠道
     */
    private String channel;
    private Long channelId;

    /**
     * 系列
     */
    private String series;
    private Long seriesId;

    /**
     * 分类
     */
    private String category;
    private Long categoryId;

    /**
     * sku 列表
     */
    private List<SkuListDTO> skuItems;

    /**
     * 创建人ID
     */
    private Long creatorId;
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;
    private String updaterName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
} 
