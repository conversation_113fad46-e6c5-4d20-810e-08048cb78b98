package com.spes.sop.support.config.convert;

import cn.hutool.core.collection.CollUtil;
import com.spes.sop.config.service.model.bo.GoodsBrandBO;
import com.spes.sop.controller.config.model.vo.BrandVO;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 品牌转换器
 */
public class BrandConverter {

    /**
     * 转换品牌BO为品牌VO
     *
     * @param brandBO 品牌BO
     * @return 品牌VO
     */
    public static BrandVO toBrandVO(GoodsBrandBO brandBO) {
        if (brandBO == null) {
            return null;
        }

        return BrandVO.builder()
                .id(brandBO.getId())
                .name(brandBO.getName())
                .description(brandBO.getDescription())
                .status(brandBO.getStatus() != null ? brandBO.getStatus().name() : null)
                .statusDesc(brandBO.getStatus() != null ? brandBO.getStatus().getStatusDesc() : null)
                .createTime(brandBO.getCreateTime())
                .build();
    }

    /**
     * 转换品牌BO列表为品牌VO列表
     *
     * @param brandBOList 品牌BO列表
     * @return 品牌VO列表
     */
    public static List<BrandVO> toBrandVOList(List<GoodsBrandBO> brandBOList) {
        if (CollUtil.isEmpty(brandBOList)) {
            return Collections.emptyList();
        }

        return brandBOList.stream()
                .map(BrandConverter::toBrandVO)
                .collect(Collectors.toList());
    }

}