package com.spes.sop.support.goods;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.spes.sop.common.exception.BusinessException;
import com.spes.sop.common.page.BasePager;
import com.spes.sop.common.page.PageResult;
import com.spes.sop.common.security.SecurityContextUtil;
import com.spes.sop.common.util.CodeGenerationUtil;
import com.spes.sop.config.service.*;
import com.spes.sop.config.service.model.bo.*;
import com.spes.sop.config.service.model.query.BrandBOPagerQuery;
import com.spes.sop.config.service.model.query.CategoryBOPagerQuery;
import com.spes.sop.config.service.model.query.ChannelBOPagerQuery;
import com.spes.sop.config.service.model.query.SeriesBOPagerQuery;
import com.spes.sop.controller.goods.model.query.SpuPageQuery;
import com.spes.sop.controller.goods.model.request.SpuCreateRequest;
import com.spes.sop.controller.goods.model.request.SpuUpdateRequest;
import com.spes.sop.goods.service.sku.SkuService;
import com.spes.sop.goods.service.spu.SpuService;
import com.spes.sop.goods.service.spu.model.bo.SpuBO;
import com.spes.sop.goods.service.spu.model.command.SpuCreateCommand;
import com.spes.sop.goods.service.spu.model.query.SpuBOListQuery;
import com.spes.sop.support.goods.converter.SpuDTOConverter;
import com.spes.sop.support.goods.model.dto.SpuDetailDTO;
import com.spes.sop.support.goods.model.dto.SpuPageDTO;
import com.spes.sop.user.service.user.UserService;
import com.spes.sop.user.service.user.model.bo.UserBO;
import com.spes.sop.user.service.user.model.query.UserPageQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * SPU支持类 - 负责处理Controller层与Service层之间的数据转换和业务协调
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SpuSupport {

    private final SpuService spuService;
    private final SkuService skuService;
    private final UserService userService;
    private final GoodsClassificationService goodsClassificationService;
    private final GoodsChannelService goodsChannelService;
    private final GoodsSeriesService goodsSeriesService;
    private final GoodsCategoryService goodsCategoryService;
    private final GoodsBrandService goodsBrandService;
    private final TransactionTemplate transactionTemplate;

    /**
     * SPU分页查询
     *
     * @param request 分页查询请求
     * @return 分页查询结果
     */
    public PageResult<SpuPageDTO> page(@NotNull SpuPageQuery request) {
        // 转换查询参数
        SpuBOListQuery query = SpuDTOConverter.queryConvert(request);
        if (query == null) {
            log.warn("转换查询参数失败，返回空结果");
            return PageResult.of(Collections.emptyList(), 0L, request.getPageNum(), request.getPageSize());
        }

        // 先查询总数
        Long total = spuService.count(query);
        if (total == null || total <= 0) {
            log.info("查询结果为空，总数：{}", total);
            return PageResult.of(Collections.emptyList(), 0L, request.getPageNum(), request.getPageSize());
        }

        // 执行业务查询
        List<SpuBO> spuBOList = spuService.list(query);
        if (CollectionUtils.isEmpty(spuBOList)) {
            log.info("查询数据为空，但总数为：{}", total);
            return PageResult.of(Collections.emptyList(), total, request.getPageNum(), request.getPageSize());
        }
        List<SpuPageDTO> spus = fillSpusInfo(spuBOList);
        return PageResult.of(spus, total, request.getPageNum(), request.getPageSize());
    }




    /**
     * 根据ID查询SPU详情
     *
     * @param id SPU ID
     * @return SPU详情信息
     */
    public SpuDetailDTO getById(Long id) {
        Objects.requireNonNull(id, "SPU ID不能为空");
        log.info("开始查询SPU详情，ID：{}", id);
        // 执行查询
        SpuBO spu = spuService.getById(id);
        if (ObjectUtil.isNull(spu)) {
            log.warn("未找到SPU信息，ID：{}", id);
            return null;
        }
        List<GoodsBrandBO> brands = getGoodsBrands(Lists.newArrayList(spu.getBrandId()));
        List<GoodsClassificationBO> classifications = getGoodsClassifications(Lists.newArrayList(spu));
        List<GoodsChannelBO> channels = getGoodsChannels(Lists.newArrayList(spu.getChannelId()));
        List<GoodsSeriesBO> series = getGoodsSeries(Lists.newArrayList(spu.getSeriesId()));
        List<GoodsCategoryBO> categories = getGoodsCategories(Lists.newArrayList(spu.getCategoryId()));
        List<UserBO> users = getUsers(Lists.newArrayList(spu));
        // 转换为详情
        // todo sku 列表

        return SpuDTOConverter.toSpuDetailDTO(spu, users, classifications, channels, series, categories, brands);
    }

    /**
     * 创建SPU
     *
     * @param request 创建请求
     */
    public void create(@Valid @NotNull SpuCreateRequest request) {
        Objects.requireNonNull(request, "创建请求不能为空");
        log.info("开始创建SPU，名称：{}", request.getSpuName());
        SpuBO spu = spuService.getBySpuName(request.getSpuName());
        if (ObjectUtil.isNotNull(spu)) {
            throw new BusinessException("该 SPU 名称已存在");
        }

        // 使用事务模板确保数据一致性
        transactionTemplate.execute(status -> {
            // 转换为业务对象
            SpuCreateCommand command = SpuDTOConverter.convert2Command(request);
            if (command == null) {
                throw new IllegalArgumentException("转换创建请求失败");
            }

            String spuCode = CodeGenerationUtil.generateSpuCode();
            command.setSpuCode(spuCode);

            // 从上下文获取当前用户ID
            Long currentUserId = SecurityContextUtil.getCurrentUserIdLenient();
            command.setCreatorId(currentUserId);
            command.setUpdaterId(currentUserId);
            // 执行创建逻辑
            spuService.create(command);
            log.info("创建SPU成功，名称：{}，编码：{}", request.getSpuName(), spuCode);
            return null;
        });
    }

    /**
     * 更新SPU
     *
     * @param request 更新请求
     */
    public void update(@Valid @NotNull SpuUpdateRequest request) {
        Objects.requireNonNull(request, "更新请求不能为空");
        Objects.requireNonNull(request.getId(), "SPU ID不能为空");
        log.info("开始更新SPU，ID：{}，名称：{}", request.getId(), request.getSpuName());

        // 使用事务模板确保数据一致性
        transactionTemplate.execute(status -> {
            // 检查SPU是否存在
            SpuBO existingSpu = spuService.getById(request.getId());
            if (existingSpu == null) {
                throw new IllegalArgumentException("SPU不存在，ID：" + request.getId());
            }

            // 转换为业务对象
            SpuBO spuBO = SpuDTOConverter.convertUpdateRequestToBO(request);
            if (spuBO == null) {
                throw new IllegalArgumentException("转换更新请求失败");
            }

            // 执行更新逻辑
            spuService.update(spuBO);
            log.info("更新SPU成功，ID：{}，名称：{}", request.getId(), request.getSpuName());
            return null;
        });
    }


    public List<SpuPageDTO> getByIds(List<Long> spuIds) {
        if (CollectionUtils.isEmpty(spuIds)) {
            return Collections.emptyList();
        }
        List<SpuBO> spuBOList = spuService.list(SpuBOListQuery.builder()
                .ids(spuIds)
                .pager(new BasePager.Pager(1, spuIds.size()))
                .build());
        if (CollectionUtils.isEmpty(spuBOList)) {
            return Collections.emptyList();
        }
        return fillSpusInfo(spuBOList);
    }

    /**
     * 填充 spu 信息
     */
    private List<SpuPageDTO> fillSpusInfo(List<SpuBO> spuBOList) {
        //  更新人、创建人信息
        List<UserBO> users = getUsers(spuBOList);
        //类别信息
        List<GoodsClassificationBO> classificationBOList = getGoodsClassifications(spuBOList);
        //渠道信息
        List<GoodsChannelBO> channels =
                getGoodsChannels(spuBOList.stream().map(SpuBO::getChannelId).collect(Collectors.toList()));
        //系列信息
        List<GoodsSeriesBO> seriesList =
                getGoodsSeries(spuBOList.stream().map(SpuBO::getSeriesId).collect(Collectors.toList()));
        //分类信息
        List<GoodsCategoryBO> categoryList =
                getGoodsCategories(spuBOList.stream().map(SpuBO::getCategoryId).collect(Collectors.toList()));
        //品牌信息
        List<GoodsBrandBO> brandList =
                getGoodsBrands(spuBOList.stream().map(SpuBO::getBrandId).collect(Collectors.toList()));

        List<SpuPageDTO> spus = SpuDTOConverter.toSpuPageDTO(spuBOList, users, classificationBOList, channels,
                seriesList,
                categoryList, brandList);
        return spus;
    }

    /**
     * 获取品牌信息
     */
    private List<GoodsBrandBO> getGoodsBrands(List<Long> brandIds) {
        return goodsBrandService.listBrands(BrandBOPagerQuery.builder().ids(brandIds)
                .pager(new BasePager.Pager(1, brandIds.size())).build());
    }

    /**
     * 获取用户信息
     */
    private List<UserBO> getUsers(List<SpuBO> spuBOList) {
        List<Long> userIds = spuBOList.stream().map(SpuBO::getCreatorId).collect(Collectors.toList());
        userIds.addAll(spuBOList.stream().map(SpuBO::getUpdaterId).collect(Collectors.toList()));
        return userService.getUserPage(UserPageQuery.builder().ids(userIds).build());
    }

    /**
     * 获取类目信息
     */
    private List<GoodsClassificationBO> getGoodsClassifications(List<SpuBO> spuBOList) {
        List<Long> classificationIds =
                spuBOList.stream().map(SpuBO::getFirstClassification).collect(Collectors.toList());
        classificationIds.addAll(spuBOList.stream().map(SpuBO::getSecondClassification).collect(Collectors.toList()));
        classificationIds.addAll(spuBOList.stream().map(SpuBO::getThirdClassification).collect(Collectors.toList()));
        classificationIds.addAll(spuBOList.stream().map(SpuBO::getFourthClassification).collect(Collectors.toList()));
        return goodsClassificationService.getClassification(classificationIds.stream().filter(Objects::nonNull).collect(Collectors.toList()));

    }

    /**
     * 获取渠道信息
     */
    private List<GoodsChannelBO> getGoodsChannels(List<Long> channelIds) {
        return goodsChannelService.list(ChannelBOPagerQuery.builder().ids(channelIds)
                .pager(new BasePager.Pager(1, channelIds.size())).build());
    }

    /**
     * 获取系列信息
     */
    private List<GoodsSeriesBO> getGoodsSeries(List<Long> seriesIds) {
        return goodsSeriesService.list(SeriesBOPagerQuery.builder().ids(seriesIds)
                .pager(new BasePager.Pager(1, seriesIds.size())).build());
    }

    /**
     * 获取类目信息
     */
    private List<GoodsCategoryBO> getGoodsCategories(List<Long> categories) {
        return goodsCategoryService.list(CategoryBOPagerQuery.builder().ids(categories)
                .pager(new BasePager.Pager(1, categories.size())).build());
    }
}
