package com.spes.sop.support.goods.converter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Maps;
import com.spes.sop.common.enums.OaStatusEnum;
import com.spes.sop.common.enums.UserRoleEnum;
import com.spes.sop.common.page.BasePager;
import com.spes.sop.common.security.JwtUserDetails;
import com.spes.sop.controller.goods.model.query.SkuPageQuery;
import com.spes.sop.controller.goods.model.request.SkuCreateRequest;
import com.spes.sop.controller.goods.model.request.SkuUpdateRequest;
import com.spes.sop.controller.goods.model.vo.value.SkuBusinessInfoValue;
import com.spes.sop.controller.goods.model.vo.value.SkuProductInfoValue;
import com.spes.sop.controller.goods.model.vo.value.SkuSupplyChainInfoValue;
import com.spes.sop.goods.service.sku.model.bo.SkuDetailBO;
import com.spes.sop.goods.service.sku.model.bo.SkuListBO;
import com.spes.sop.goods.service.sku.model.bo.value.SkuBusinessInfoValueBO;
import com.spes.sop.goods.service.sku.model.command.SkuCreateCommand;
import com.spes.sop.goods.service.sku.model.command.SkuUpdateCommand;
import com.spes.sop.goods.service.sku.model.query.SkuBOListQuery;
import com.spes.sop.support.goods.model.dto.SkuDetailDTO;
import com.spes.sop.support.goods.model.dto.SkuListDTO;
import com.spes.sop.third.weaver.constants.WeaverWorkflowConstant;
import com.spes.sop.third.weaver.model.request.value.WorkflowAttachmentValue;
import com.spes.sop.third.weaver.model.request.value.WorkflowRequestTable;
import com.spes.sop.third.weaver.model.request.value.WorkflowRequestTableField;
import com.spes.sop.third.weaver.model.request.value.WorkflowRequestTableRecord;
import com.spes.sop.user.service.user.model.bo.UserBO;

import javax.validation.constraints.NotNull;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * SKU VO转换器
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public class SkuVOConverter {

    /**
     * 转换SKU业务对象为VO
     *
     * @param skuListBO SKU业务对象
     * @return SKU列表VO
     */
    static SkuListDTO convertSkuBOToVO(SkuListBO skuListBO, String role) {
        if (skuListBO == null) {
            return null;
        }

        return SkuListDTO.builder()
                .id(skuListBO.getId())
                .skuCode(skuListBO.getSkuCode())
                .skuName(skuListBO.getSkuName())
                .barCode(skuListBO.getBarCode())
                .spuId(skuListBO.getSpuId())
                .gift(skuListBO.getGift() != null && skuListBO.getGift() == 1)
                .card(skuListBO.getCard() != null && skuListBO.getCard() == 1)
                .status(ObjectUtil.isNull(skuListBO.getStatus()) ? null : skuListBO.getStatus().name())
                .statusDesc(ObjectUtil.isNull(skuListBO.getStatus()) ? null : skuListBO.getStatus().getDesc())
                .syncFail(skuListBO.getSyncFail())
                .fairPrice(skuListBO.getFairPrice())
                .basePrice(UserRoleEnum.getPricePermissionRole().contains(role) ?
                        skuListBO.getBasePrice() : null)
                .cost(skuListBO.getCost())
                .createTime(skuListBO.getCreateTime() != null ? skuListBO.getCreateTime() : null)
                .updateTime(skuListBO.getUpdateTime() != null ? skuListBO.getUpdateTime() : null)
                .build();
    }


    /**
     * 将SkuPageQuery转换为SkuBOListQuery
     *
     * @param request 分页查询请求
     * @return 服务层查询对象
     */
    public static SkuBOListQuery toServiceQuery(@NotNull SkuPageQuery request) {
        return SkuBOListQuery.builder()
                .ids(request.getId() != null ? Collections.singletonList(request.getId()) : null)
                .spuIds(request.getSpuId() != null ? Collections.singletonList(request.getSpuId()) : null)
                .skuCodes(request.getSkuCode() != null ? Collections.singletonList(request.getSkuCode()) : null)
                .skuNameSearch(request.getSkuNameSearch())
                .barCodes(request.getBarCode() != null ? Collections.singletonList(request.getBarCode()) : null)
                .oaStatuses(CollUtil.isEmpty(request.getStatuses()) ? null :
                        request.getStatuses().stream().map(OaStatusEnum::getByName).collect(Collectors.toList()))
                .channelIds(request.getChannelIds())
                .series(request.getSeriesId())
                .gift(request.getGift())
                .card(request.getPackageCard())
                .pager(new BasePager.Pager(request.getPageNum(), request.getPageSize()))
                .build();
    }

    /**
     * 将SkuDetailBO转换为SkuDetailVO（完整版本，查询关联信息）
     *
     * @param skuBO              SKU详情业务对象
     * @return SKU详情VO
     */
    public static SkuDetailDTO convertToSkuDetail(SkuDetailBO skuBO,
                                                  JwtUserDetails userDetails) {
        if (skuBO == null) {
            return null;
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SkuDetailDTO result = SkuDetailDTO.builder()
                .id(skuBO.getId())
                .skuCode(skuBO.getSkuCode())
                .skuName(skuBO.getSkuName())
                .barCode(skuBO.getBarCode())
                .spec(skuBO.getSpecification())
                .spuId(skuBO.getSpuId())
                .gift(skuBO.getGift() != null && skuBO.getGift() == 1)
                .packageCard(skuBO.getCard() != null && skuBO.getCard() == 1)
                .status(skuBO.getStatus())
                .statusDesc(OaStatusEnum.getByName(skuBO.getStatus()).getDesc())
                .syncFail(skuBO.getSyncFail())
                .fairPrice(skuBO.getFairPrice())
                .basePrice(UserRoleEnum.getPricePermissionRole().contains(userDetails.getRoles())
                        ? skuBO.getBasePrice() : null)
                .filingNumber(skuBO.getFilingNumber())
                .cost(skuBO.getCost())
                // 解析业务信息JSON
                .businessInfo(parseBusinessInfo(skuBO.getBusinessInfo()))
                // 解析产品信息JSON
                .productInfo(parseProductInfo(skuBO.getProductInfo()))
                // 解析供应链信息JSON
                .supplyChainInfo(parseSupplyChainInfo(skuBO.getSupplyChainInfo()))
                // 时间和用户信息
                .createTime(skuBO.getCreateTime() != null ? dateFormat.format(skuBO.getCreateTime()) : null)
                .updateTime(skuBO.getUpdateTime() != null ? dateFormat.format(skuBO.getUpdateTime()) : null)
                .build();
        return result;
    }

    /**
     * 将SkuListBO列表转换为SkuListVO列表
     *
     * @param skuBOList SKU业务对象列表
     * @return SKU VO列表
     */
    public static List<SkuListDTO> convertToSkuListVOList(List<SkuListBO> skuBOList,
                                                          List<UserBO> users,
                                                          JwtUserDetails userDetails) {
        if (CollUtil.isEmpty(skuBOList)) {
            return Collections.emptyList();
        }

        Map<Long, UserBO> userMap = CollUtil.isNotEmpty(users) ?
                users.stream().collect(Collectors.toMap(UserBO::getId, Function.identity())) :
                Maps.newHashMap();

        return skuBOList.stream().map(skuBO -> {

            UserBO creator = userMap.get(skuBO.getCreatorId());
            UserBO modifier = userMap.get(skuBO.getUpdaterId());
            return SkuListDTO.builder()
                    .id(skuBO.getId())
                    .skuCode(skuBO.getSkuCode())
                    .skuName(skuBO.getSkuName())
                    .barCode(skuBO.getBarCode())
                    .spec(skuBO.getSpecification())
                    .spuId(skuBO.getSpuId())
                    .gift(skuBO.getGift() != null && skuBO.getGift() == 1)
                    .card(skuBO.getCard() != null && skuBO.getCard() == 1)
                    .status(ObjectUtil.isNull(skuBO.getId()) ? null : skuBO.getStatus().name())
                    .statusDesc(ObjectUtil.isNull(skuBO.getId()) ? null : skuBO.getStatus().getDesc())
                    .syncFail(skuBO.getSyncFail())
                    .fairPrice(skuBO.getFairPrice())
                    .basePrice(UserRoleEnum.getPricePermissionRole().contains(userDetails.getRoles())
                            ? skuBO.getBasePrice() : null)
                    .cost(skuBO.getCost())
                    .createTime(skuBO.getCreateTime() != null ? skuBO.getCreateTime() : null)
                    .updateTime(skuBO.getUpdateTime() != null ? skuBO.getUpdateTime() : null)
                    .creatorName(ObjectUtil.isNull(creator) ? null : creator.getUsername())
                    .updaterName(ObjectUtil.isNull(modifier) ? null : modifier.getUsername())
                    .build();
        }).collect(Collectors.toList());

    }

    /**
     * 将SkuUpdateRequest转换为SkuUpdateReq
     *
     * @param request 更新请求
     * @return SKU更新请求
     */
    public static SkuUpdateCommand toCmd(SkuUpdateRequest request) {
        if (request == null) {
            return null;
        }

        return SkuUpdateCommand.builder()
                .id(request.getId())
                .skuName(request.getSkuName())
                .build();
    }


    /**
     * 构建SKU创建命令
     *
     * @param request   创建请求
     * @param creatorId 创建人ID
     * @return SKU创建命令
     */
    public static SkuCreateCommand buildSkuCreateCommand(SkuCreateRequest request, Long creatorId, String skuCode) {


        // 创建基础命令
        SkuCreateCommand command = SkuCreateCommand.builder()
                .skuCode(skuCode)
                .skuName(request.getSkuName())
                .channelId(request.getChannelId())
                .oaStatus(OaStatusEnum.CREATED)
                .specification(request.getSpecification())
                .creatorId(creatorId)
                .updaterId(creatorId).build();

        // 构建运营信息对象
        SkuBusinessInfoValueBO businessInfo = SkuBusinessInfoValueBO.builder()
                .applicationDate(request.getApplicationDate())
                .launchDate(request.getLaunchDate())
                .channelExclusive(request.getChannelExclusive())
                .productPurpose(request.getProductPurpose())
                .firstBatchQuantity(request.getFirstBatchQuantity())
                .factoryMinOrderQuantity(request.getFactoryMinOrderQuantity())
                .minOrderQuantity(request.getMinOrderQuantity())
                .minOrderReason(request.getMinOrderReason())
                .shelfLifeRequirement(request.getShelfLifeRequirement())
                .exclusiveSale(request.getExclusiveSale())
                .salesResponsibleEmployeeId(request.getSalesResponsibleEmployeeId())
                .estimatedPrice(request.getEstimatedPrice())
                .remark(request.getRemark())
                .build();
        command.setBusinessInfo(businessInfo);
        return command;
    }

    /**
     * 解析业务信息JSON
     *
     * @param businessInfoJson 业务信息JSON字符串
     * @return 业务信息对象
     */
    private static SkuBusinessInfoValue parseBusinessInfo(String businessInfoJson) {
        if (StrUtil.isBlank(businessInfoJson)) {
            return null;
        }

        try {
            return JSONUtil.toBean(businessInfoJson, SkuBusinessInfoValue.class);
        } catch (Exception e) {
            // 如果解析失败，返回null或默认值
            return null;
        }
    }

    /**
     * 解析产品信息JSON
     *
     * @param productInfoJson 产品信息JSON字符串
     * @return 产品信息对象
     */
    private static SkuProductInfoValue parseProductInfo(String productInfoJson) {
        if (StrUtil.isBlank(productInfoJson)) {
            return null;
        }
        try {
            return JSONUtil.toBean(productInfoJson, SkuProductInfoValue.class);
        } catch (Exception e) {
            // 如果解析失败，返回null或默认值
            return null;
        }
    }

    /**
     * 解析供应链信息JSON
     *
     * @param supplyChainInfoJson 供应链信息JSON字符串
     * @return 供应链信息对象
     */
    private static SkuSupplyChainInfoValue parseSupplyChainInfo(String supplyChainInfoJson) {
        if (StrUtil.isBlank(supplyChainInfoJson)) {
            return null;
        }
        try {
            return JSONUtil.toBean(supplyChainInfoJson, SkuSupplyChainInfoValue.class);
        } catch (Exception e) {
            // 如果解析失败，返回null或默认值
            return null;
        }
    }

    public static Map<String, Object> convert2workflowRequest(List<SkuListBO> skuList, List<String> attachments,
                                                              JwtUserDetails user, String channelName) {
        Map<String, Object> params = new HashMap<>();
        params.put("requestName", generateRequestName(skuList));
        params.put("mainData", getFormMainData(user, channelName, attachments));
        params.put("workflowId", WeaverWorkflowConstant.WorkflowId.SKU_APPROVAL_WORKFLOW_ID);
        params.put("detailData", getDetailData(skuList));
        return params;
    }

    private static String getDetailData(List<SkuListBO> skuList) {
        List<WorkflowRequestTable> detailData = new ArrayList<>();
        WorkflowRequestTable businessTable = new WorkflowRequestTable();
        businessTable.setTableDBName("formtable_main_246_dt1");
        businessTable.setWorkflowRequestTableRecords(new ArrayList<>(skuList.size()));
        WorkflowRequestTable productTable = new WorkflowRequestTable();
        productTable.setTableDBName("formtable_main_246_dt2");
        productTable.setWorkflowRequestTableRecords(new ArrayList<>(skuList.size()));
        WorkflowRequestTable warehouseTable = new WorkflowRequestTable();
        warehouseTable.setTableDBName("formtable_main_246_dt3");
        warehouseTable.setWorkflowRequestTableRecords(new ArrayList<>(skuList.size()));
        for (SkuListBO skuListBO : skuList) {
            WorkflowRequestTableRecord businessTableRecord = new WorkflowRequestTableRecord();
            businessTableRecord.setRecordOrder(0);
            businessTableRecord.setWorkflowRequestTableFields(buildBusinessTableFields(skuListBO));
            businessTable.getWorkflowRequestTableRecords().add(businessTableRecord);
            WorkflowRequestTableRecord productTableRecord = new WorkflowRequestTableRecord();
            productTableRecord.setRecordOrder(0);
            productTableRecord.setWorkflowRequestTableFields(buildProductTableFields(skuListBO));
            productTable.getWorkflowRequestTableRecords().add(productTableRecord);
            WorkflowRequestTableRecord warehouseTableRecord = new WorkflowRequestTableRecord();
            warehouseTableRecord.setRecordOrder(0);
            warehouseTableRecord.setWorkflowRequestTableFields(buildWarehouseTableFields(skuListBO));
            warehouseTable.getWorkflowRequestTableRecords().add(warehouseTableRecord);
        }
        detailData.add(businessTable);
        detailData.add(productTable);
        detailData.add(warehouseTable);
        return JSONObject.toJSONString(detailData);
    }

    private static List<WorkflowRequestTableField> buildWarehouseTableFields(SkuListBO skuListBO) {
        List<WorkflowRequestTableField> tableFields = new ArrayList<>();
        tableFields.add(new WorkflowRequestTableField("skuCode", skuListBO.getSkuCode()));
        return tableFields;
    }

    private static List<WorkflowRequestTableField> buildProductTableFields(SkuListBO skuListBO) {
        List<WorkflowRequestTableField> tableFields = new ArrayList<>();
        tableFields.add(new WorkflowRequestTableField("skuCode", skuListBO.getSkuCode()));
        return tableFields;
    }

    private static List<WorkflowRequestTableField> buildBusinessTableFields(SkuListBO sku) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        List<WorkflowRequestTableField> tableFields = new ArrayList<>();
        tableFields.add(new WorkflowRequestTableField("goods", sku.getSkuCode()));
        tableFields.add(new WorkflowRequestTableField("skuName", sku.getSkuName()));
        tableFields.add(new WorkflowRequestTableField("spec", String.valueOf(sku.getSpecification())));
        SkuBusinessInfoValueBO businessInfo = sku.getBusinessInfo();
        tableFields.add(new WorkflowRequestTableField("productPurpose", businessInfo.getProductPurpose()));
        tableFields.add(new WorkflowRequestTableField("applicationDate",
                ObjectUtil.isNotNull(businessInfo.getApplicationDate()) ?
                        simpleDateFormat.format(businessInfo.getApplicationDate()) : null));
        tableFields.add(new WorkflowRequestTableField("launchDate", ObjectUtil.isNotNull(businessInfo.getLaunchDate()) ?
                simpleDateFormat.format(businessInfo.getLaunchDate()) : null));
        tableFields.add(new WorkflowRequestTableField("sfqdtgp",
                ObjectUtil.isNotNull(businessInfo.getChannelExclusive()) && businessInfo.getChannelExclusive() ? "1"
                        : "0"));
        tableFields.add(new WorkflowRequestTableField("estimatedPrice",
                String.valueOf(businessInfo.getEstimatedPrice())));
        tableFields.add(new WorkflowRequestTableField("firstBatchQuantity",
                String.valueOf(businessInfo.getFirstBatchQuantity())));
        tableFields.add(new WorkflowRequestTableField("factoryMinOrderQuantity",
                String.valueOf(businessInfo.getFactoryMinOrderQuantity())));
        tableFields.add(new WorkflowRequestTableField("minOrderQuantity",
                String.valueOf(businessInfo.getMinOrderQuantity())));
        tableFields.add(new WorkflowRequestTableField("minOrderReason",
                businessInfo.getMinOrderReason()));
        tableFields.add(new WorkflowRequestTableField("shelfLifeRequirement",
                businessInfo.getShelfLifeRequirement()));
        tableFields.add(new WorkflowRequestTableField("exclusiveSale",
                ObjectUtil.isNotNull(businessInfo.getExclusiveSale()) && businessInfo.getExclusiveSale() ? "1" : "0"));
        tableFields.add(new WorkflowRequestTableField("salesResponsibleEmployeeId",
                businessInfo.getSalesResponsibleEmployeeId()));
        tableFields.add(new WorkflowRequestTableField("remark",
                businessInfo.getRemark()));
        return tableFields;
    }

    /**
     * 生成流程名称
     */
    private static String generateRequestName(List<SkuListBO> skuList) {
        String today = DateUtil.format(new Date(), "yyyy-MM-dd");

        if (skuList.size() == 1) {
            return String.format("SKU立项审批-%s-%s", skuList.get(0).getSkuName(), today);
        } else {
            return String.format("批量SKU立项审批-%d个产品-%s", skuList.size(), today);
        }
    }

    /**
     * 主表数据
     * <p>
     * 附件上传 包含base64, http等
     * 包含浏览框数据，单行文本数据
     */
    private static String getFormMainData(JwtUserDetails user, String channelName, List<String> attachmentUrls) {
        List<WorkflowRequestTableField> mainData = new ArrayList<>();
        //附件上传字段
        WorkflowRequestTableField field1 = new WorkflowRequestTableField();
        field1.setFieldName("channel");
        field1.setFieldValue(channelName);
        mainData.add(field1);

        //单行文本字段
        WorkflowRequestTableField field2 = new WorkflowRequestTableField();
        field2.setFieldName("department");
        field2.setFieldValue(user.getDepartment());
        mainData.add(field2);

        WorkflowRequestTableField field3 = new WorkflowRequestTableField();
        field3.setFieldName("submitter");
        field3.setFieldValue(String.valueOf(user.getWeaverId()));
        mainData.add(field3);
        if (CollUtil.isNotEmpty(attachmentUrls)) {
            WorkflowRequestTableField field4 = new WorkflowRequestTableField();
            field4.setFieldName("files");
            List<WorkflowAttachmentValue> attachmentValues = new ArrayList<>(attachmentUrls.size());
            attachmentUrls.forEach(url -> {
                //url 取最后一个 / 后面的值
                String fileName = url.substring(url.lastIndexOf('/') + 1);
                attachmentValues.add(WorkflowAttachmentValue.builder()
                        .fileName(fileName)
                        .filePath(url)
                        .build());
            });
            field4.setFieldValue(JSONUtil.toJsonStr(attachmentValues));
            mainData.add(field4);
        }


        return JSONObject.toJSONString(mainData);
    }
}