package com.spes.sop.controller.goods.model.vo;


import com.spes.sop.common.enums.OaStatusEnum;
import com.spes.sop.support.goods.model.vo.value.SkuBusinessInfoValue;
import com.spes.sop.support.goods.model.vo.value.SkuProductInfoValue;
import com.spes.sop.support.goods.model.vo.value.SkuSupplyChainInfoValue;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuDetailVO {
    /**
     * 主键 id
     */
    private Long id;
    /**
     * 货品编码
     */
    private String skuCode;
    /**
     * SKU名称
     */
    private String skuName;
    /**
     * 条码
     */
    private String barCode;
    /**
     * 产品对应SPU
     */
    private Long spuId;

    /**
     * 备案号
     */
    private String filingNumber;
    /**
     * 产品规格
     */
    private Integer spec;
    /**
     * 是否赠品
     */
    private Boolean gift;
    /**
     * 是否为包裹卡
     */
    private Boolean packageCard;
    /**
     * 状态
     *
     * @see OaStatusEnum#name()
     */
    private String status;
    /**
     * 状态描述
     *
     * @see OaStatusEnum#getDesc()
     */
    private String statusDesc;
    /**
     * 同步失败原因
     */
    private String syncFail;
    /**
     * 标准价
     */
    private BigDecimal fairPrice;
    /**
     * 底价
     */
    private BigDecimal basePrice;
    /**
     * 成本
     */
    private BigDecimal cost;
    /**
     * 运营信息
     */
    private SkuBusinessInfoValue businessInfo;
    /**
     * 产品信息
     */
    private SkuProductInfoValue productInfo;
    /**
     * 供应链信息
     */
    private SkuSupplyChainInfoValue supplyChainInfo;

    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 创建人
     */
    private String creatorName;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 更新人
     */
    private String updaterName;
}
