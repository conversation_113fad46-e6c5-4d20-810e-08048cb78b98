package com.spes.sop.support.goods.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.spes.sop.common.enums.GoodsStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 组合品分页查询响应DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CombinationPageDTO {

    /**
     * 组合品ID
     */
    private Long id;

    /**
     * 组合品编码
     */
    private String combinationCode;

    /**
     * 组合品名称
     */
    private String combinationName;

    /**
     * 描述
     */
    private String description;
    /**
     * 品牌
     */
    private String brand;
    /**
     * 一级分类
     */
    private String firstClassification;

    /**
     * 二级分类
     */
    private String secondClassification;

    /**
     * 三级分类
     */
    private String thirdClassification;
    /**
     * 四级分类
     */
    private String fourthClassification;
    /**
     * 状态
     *
     * @see GoodsStatusEnum#name()
     */
    private String status;

    /**
     * 状态描述
     *
     * @see GoodsStatusEnum#getDesc()
     */
    private String statusDesc;

    /**
     * 渠道 id
     */
    private String channel;

    /**
     * 系列ID
     */
    private String series;
    /**
     * 分类ID
     */
    private String category;

    /**
     * 组合品明细列表（简化版）
     */
    private List<String> skuNames;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人
     */
    private String updaterName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
} 