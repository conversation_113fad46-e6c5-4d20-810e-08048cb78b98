package com.spes.sop.support.goods.converter;

import cn.hutool.core.util.StrUtil;
import com.spes.sop.controller.goods.model.request.SkuBatchImportRequest;
import com.spes.sop.controller.goods.model.request.SkuCreateRequest;

/**
 * SKU批量导入转换器
 *
 * <AUTHOR>
 */
public class SkuBatchImportConverter {

    /**
     * 将批量导入请求转换为创建请求
     *
     * @param importRequest 批量导入请求
     * @return 创建请求
     */
    public static SkuCreateRequest toCreateRequest(SkuBatchImportRequest importRequest) {
        if (importRequest == null) {
            return null;
        }

        return SkuCreateRequest.builder()
                .skuName(importRequest.getSkuName())
                .specification(importRequest.getSpecification())
                .applicationDate(importRequest.getApplicationDate())
                .launchDate(importRequest.getLaunchDate())
                .channelExclusive(importRequest.getChannelExclusive())
                .productPurpose(importRequest.getProductPurpose())
                .estimatedPrice(importRequest.getEstimatedPrice())
                .firstBatchQuantity(importRequest.getFirstBatchQuantity())
                .factoryMinOrderQuantity(importRequest.getFactoryMinOrderQuantity())
                .minOrderQuantity(importRequest.getMinOrderQuantity())
                .minOrderReason(importRequest.getMinOrderReason())
                .shelfLifeRequirement(importRequest.getShelfLifeRequirement())
                .exclusiveSale(importRequest.getExclusiveSale())
                .salesResponsibleEmployeeId(importRequest.getSalesResponsibleEmployeeId())
                .remark(importRequest.getRemark())
                .build();
    }

    /**
     * 验证导入数据
     *
     * @param importRequest 导入请求
     * @return 验证错误信息，如果为空则验证通过
     */
    public static String validateImportData(SkuBatchImportRequest importRequest) {
        if (importRequest == null) {
            return "导入数据不能为空";
        }

        if (StrUtil.isBlank(importRequest.getSkuName())) {
            return "SKU名称不能为空";
        }

        if (importRequest.getSkuName().length() > 100) {
            return "SKU名称长度不能超过100个字符";
        }

        return null;
    }
} 