package com.spes.sop.support.goods.model.request;

import com.spes.sop.support.goods.model.request.value.CombinationSkuItem;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 组合品更新请求
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CombinationUpdateRequest {

    /**
     * 组合品ID
     */
    @NotNull(message = "组合品ID不能为空")
    private Long id;

    /**
     * 组合品名称
     */
    private String combinationName;

    /**
     * 描述
     */
    private String description;


    /**
     * sku 列表信息
     */
    private List<CombinationSkuItem> skuItems;

} 
