package com.spes.sop.support.goods.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.spes.sop.common.enums.OaStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 组合品分页查询响应VO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CombinationPageVO {

    /**
     * 组合品ID
     */
    private Long id;

    /**
     * 组合品编码
     */
    private String combinationCode;

    /**
     * 组合品名称
     */
    private String combinationName;

    /**
     * 描述
     */
    private String description;

    /**
     * 一级分类
     */
    private String firstClassification;

    /**
     * 二级分类
     */
    private String secondClassification;

    /**
     * 三级分类
     */
    private String thirdClassification;
    /**
     * 状态
     * @see OaStatusEnum#name()
     */
    private String status;

    /**
     * 状态描述
     * @see OaStatusEnum#getDesc()
     */
    private String statusDesc;

    /**
     * 渠道 id
     */
    private String channel;

    /**
     * 系列ID
     */
    private List<String> series;
    /**
     * 分类ID
     */
    private String category;

    /**
     * 组合品明细列表（简化版）
     */
    private List<String> skuNames;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人
     */
    private String updaterName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
} 
