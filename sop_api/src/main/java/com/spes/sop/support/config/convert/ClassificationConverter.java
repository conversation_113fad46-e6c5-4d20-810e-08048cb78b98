package com.spes.sop.support.config.convert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.spes.sop.config.service.model.bo.GoodsClassificationTreeBO;
import com.spes.sop.controller.config.model.vo.ClassificationVO;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 商品分类转换器
 */
public class ClassificationConverter {

    /**
     * 转换商品分类树BO为商品分类VO
     *
     * @param classificationTreeBO 商品分类树BO
     * @return 商品分类VO
     */
    public static ClassificationVO toClassificationVO(GoodsClassificationTreeBO classificationTreeBO) {
        if (classificationTreeBO == null) {
            return null;
        }

        ClassificationVO.ClassificationVOBuilder builder = ClassificationVO.builder()
                .id(classificationTreeBO.getId())
                .name(classificationTreeBO.getName())
                .level(classificationTreeBO.getLevel())
                .status(ObjectUtil.isNotNull(classificationTreeBO.getStatus()) ?
                        classificationTreeBO.getStatus().name() : null)
                .statusDesc(ObjectUtil.isNotNull(classificationTreeBO.getStatus()) ?
                        classificationTreeBO.getStatus().getStatusDesc() : null);

        // 递归转换子分类
        if (CollUtil.isNotEmpty(classificationTreeBO.getChildren())) {
            List<ClassificationVO> children = classificationTreeBO.getChildren().stream()
                    .map(ClassificationConverter::toClassificationVO)
                    .collect(Collectors.toList());
            builder.children(children);
        }

        return builder.build();
    }

    /**
     * 转换商品分类树BO列表为商品分类VO列表
     *
     * @param classificationTreeBOList 商品分类树BO列表
     * @return 商品分类VO列表
     */
    public static List<ClassificationVO> toClassificationVOList(List<GoodsClassificationTreeBO> classificationTreeBOList) {
        if (CollUtil.isEmpty(classificationTreeBOList)) {
            return Collections.emptyList();
        }

        return classificationTreeBOList.stream()
                .map(ClassificationConverter::toClassificationVO)
                .collect(Collectors.toList());
    }
} 