package com.spes.sop.support.goods.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * SPU分页查询响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpuPageDTO {

    /**
     * SPU ID
     */
    private Long id;

    /**
     * SPU编码
     */
    private String spuCode;

    /**
     * SPU名称
     */
    private String spuName;

    /**
     * 描述
     */
    private String description;
    /**
     * 品牌 id+名称
     */
    private String brand;
    private Long brandId;

    /**
     * 一级分类
     */
    private String firstClassification;
    private Long firstClassificationId;

    /**
     * 二级分类
     */
    private String secondClassification;
    private Long secondClassificationId;

    /**
     * 三级分类
     */
    private String thirdClassification;
    private Long thirdClassificationId;

    /**
     * 四级分类
     */
    private String fourthClassification;
    private Long fourthClassificationId;

    /**
     * 类别
     */
    private String category;
    private Long categoryId;

    /**
     * 渠道
     */
    private String channel;
    private Long channelId;

    /**
     * 系列
     */
    private String series;
    private Long seriesId;

    /**
     * 创建人ID
     */
    private Long creatorId;
    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;
    /**
     * 更新人
     */
    private String updaterName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
} 