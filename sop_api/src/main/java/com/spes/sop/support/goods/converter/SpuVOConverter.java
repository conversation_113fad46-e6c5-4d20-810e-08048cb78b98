package com.spes.sop.support.goods.converter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Maps;
import com.spes.sop.common.page.BasePager;
import com.spes.sop.config.service.model.bo.*;
import com.spes.sop.controller.goods.model.query.SpuPageQuery;
import com.spes.sop.controller.goods.model.request.SpuCreateRequest;
import com.spes.sop.controller.goods.model.request.SpuUpdateRequest;
import com.spes.sop.controller.goods.model.vo.SpuDetailVO;
import com.spes.sop.controller.goods.model.vo.SpuPageVO;
import com.spes.sop.support.goods.model.dto.SpuDetailDTO;
import com.spes.sop.support.goods.model.dto.SpuPageDTO;
import com.spes.sop.goods.service.spu.model.bo.SpuBO;
import com.spes.sop.goods.service.spu.model.command.SpuCreateCommand;
import com.spes.sop.goods.service.spu.model.query.SpuBOListQuery;
import com.spes.sop.user.service.user.model.bo.UserBO;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * SPU DTO转换器
 *
 * <AUTHOR>
 */
public interface SpuVOConverter {

    /**
     * 转换分页查询请求为业务查询对象
     *
     * @param request 分页查询请求
     * @return 业务查询对象
     */
    static SpuBOListQuery queryConvert(SpuPageQuery request) {
        if (request == null) {
            return null;
        }

        SpuBOListQuery.SpuBOListQueryBuilder<?, ?> builder = SpuBOListQuery.builder()
                .spuCodes(Objects.nonNull(request.getSpuCode()) ?
                        Collections.singletonList(request.getSpuCode()) : null)
                .spuName(request.getSpuNameSearch())
                .brandIds(Objects.nonNull(request.getBrandId()) ?
                        Collections.singletonList(request.getBrandId()) : null)
                .firstClassifications(Objects.nonNull(request.getFirstClassification()) ?
                        Collections.singletonList(request.getFirstClassification()) : null)
                .secondClassifications(Objects.nonNull(request.getSecondClassification()) ?
                        Collections.singletonList(request.getSecondClassification()) : null)
                .thirdClassifications(Objects.nonNull(request.getThirdClassification()) ?
                        Collections.singletonList(request.getThirdClassification()) : null)
                .fourthClassifications(Objects.nonNull(request.getFourthClassification()) ?
                        Collections.singletonList(request.getFourthClassification()) : null)
                .channelIds(Objects.nonNull(request.getChannelId()) ?
                        Collections.singletonList(request.getChannelId()) : null)
                .seriesIds(Objects.nonNull(request.getSeriesId()) ?
                        Collections.singletonList(request.getSeriesId()) : null)
                .categoryIds(Objects.nonNull(request.getCategoryId()) ?
                        Collections.singletonList(request.getCategoryId()) : null);

        // 设置分页信息
        int pageNum = request.getPageNum() != null ? request.getPageNum() : 1;
        int pageSize = request.getPageSize() != null ? request.getPageSize() : 10;
        builder.pager(new BasePager.Pager(pageNum, pageSize));

        return builder.build();
    }


    /**
     * 转换创建请求为业务对象
     */
    static SpuCreateCommand convert2Command(SpuCreateRequest request) {
        if (request == null) {
            return null;
        }

        return SpuCreateCommand.builder()
                .spuName(request.getSpuName())
                .description(request.getDescription())
                .brandId(request.getBrandId())
                .firstClassification(request.getFirstClassification())
                .secondClassification(request.getSecondClassification())
                .thirdClassification(request.getThirdClassification())
                .fourthClassification(request.getFourthClassification())
                .channelId(request.getChannelId())
                .seriesId(request.getSeriesId())
                .categoryId(request.getCategoryId())
                .build();
    }

    /**
     * 转换更新请求为业务对象
     */
    static SpuBO convertUpdateRequestToBO(SpuUpdateRequest request) {
        if (request == null) {
            return null;
        }

        return SpuBO.builder()
                .id(request.getId())
                .spuName(request.getSpuName())
                .description(request.getDescription())
                .brandId(request.getBrandId())
                .firstClassification(request.getFirstClassification())
                .secondClassification(request.getSecondClassification())
                .thirdClassification(request.getThirdClassification())
                .fourthClassification(request.getFourthClassification())
                .channelId(request.getChannelId())
                .seriesId(request.getSeriesId())
                .categoryId(request.getCategoryId())
                .build();
    }

    static List<SpuPageVO> toSpuDetailVO(List<SpuBO> spuBO,
                                         List<UserBO> users,
                                         List<GoodsClassificationBO> classificationBOList,
                                         List<GoodsChannelBO> channels,
                                         List<GoodsSeriesBO> seriesList,
                                         List<GoodsCategoryBO> categoryList,
                                         List<GoodsBrandBO> brandList) {
        Map<Long, String> classificationBOMap = CollUtil.isEmpty(classificationBOList) ?
                Maps.newHashMap() : classificationBOList.stream()
                .collect(Collectors.toMap(GoodsClassificationBO::getId, GoodsClassificationBO::getName));
        Map<Long, String> channelMap = CollUtil.isEmpty(channels) ?
                Maps.newHashMap() : channels.stream()
                .collect(Collectors.toMap(GoodsChannelBO::getId, GoodsChannelBO::getName));
        Map<Long, String> seriesMap = CollUtil.isEmpty(seriesList) ?
                Maps.newHashMap() : seriesList.stream()
                .collect(Collectors.toMap(GoodsSeriesBO::getId, GoodsSeriesBO::getName));
        Map<Long, String> categoryMap = CollUtil.isEmpty(categoryList) ?
                Maps.newHashMap() : categoryList.stream()
                .collect(Collectors.toMap(GoodsCategoryBO::getId, GoodsCategoryBO::getName));
        Map<Long, String> brandMap = CollUtil.isEmpty(brandList) ?
                Maps.newHashMap() : brandList.stream()
                .collect(Collectors.toMap(GoodsBrandBO::getId, GoodsBrandBO::getName));
        Map<Long, String> userMap = CollUtil.isNotEmpty(users) ? users.stream().collect(Collectors.toMap(UserBO::getId,
                UserBO::getUsername)) : Maps.newHashMap();
        return spuBO.stream()
                .map(spu -> convertBOToPageVO(spu, classificationBOMap, channelMap, seriesMap, categoryMap, brandMap,
                        userMap))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

    }

    static SpuPageVO convertBOToPageVO(SpuBO spu, Map<Long, String> classificationBOMap, Map<Long, String> channelMap,
                                       Map<Long, String> seriesMap, Map<Long, String> categoryMap,
                                       Map<Long, String> brandMap, Map<Long, String> userMap) {
        return SpuPageVO.builder()
                .id(spu.getId())
                .spuCode(spu.getSpuCode())
                .spuName(spu.getSpuName())
                .description(spu.getDescription())
                .brandId(spu.getBrandId())
                .brand(brandMap.getOrDefault(spu.getBrandId(), null))
                .firstClassification(ObjectUtil.isNotNull(spu.getFirstClassification()) && classificationBOMap.containsKey(spu.getFirstClassification())
                        ? classificationBOMap.get(spu.getFirstClassification()) : null)
                .firstClassificationId(spu.getFirstClassification())
                .secondClassification(ObjectUtil.isNotNull(spu.getSecondClassification()) && classificationBOMap.containsKey(spu.getSecondClassification())
                        ? classificationBOMap.get(spu.getSecondClassification()) : null)
                .secondClassificationId(spu.getSecondClassification())
                .thirdClassification(ObjectUtil.isNotNull(spu.getThirdClassification()) && classificationBOMap.containsKey(spu.getThirdClassification())
                        ? classificationBOMap.get(spu.getThirdClassification()) : null)
                .thirdClassificationId(spu.getThirdClassification())
                .fourthClassification(ObjectUtil.isNotNull(spu.getFourthClassification()) && classificationBOMap.containsKey(spu.getFourthClassification())
                        ? classificationBOMap.get(spu.getFourthClassification()) : null)
                .fourthClassificationId(spu.getFourthClassification())
                .channelId(spu.getChannelId())
                .channel(channelMap.getOrDefault(spu.getChannelId(), null))
                .seriesId(spu.getSeriesId())
                .series(seriesMap.getOrDefault(spu.getSeriesId(), null))
                .categoryId(spu.getCategoryId())
                .category(categoryMap.getOrDefault(spu.getCategoryId(), null))
                .creatorId(spu.getCreatorId())
                .updaterId(spu.getUpdaterId())
                .createTime(spu.getCreateTime())
                .updateTime(spu.getUpdateTime())
                .creatorName(userMap.getOrDefault(spu.getCreatorId(), ""))
                .updaterName(userMap.getOrDefault(spu.getUpdaterId(), ""))
                .build();
    }


    static SpuDetailVO toSpuDetailVO(SpuBO spu,
                                     List<UserBO> users,
                                     List<GoodsClassificationBO> classifications,
                                     List<GoodsChannelBO> channels,
                                     List<GoodsSeriesBO> series,
                                     List<GoodsCategoryBO> categories,
                                     List<GoodsBrandBO> brands) {
        Map<Long, String> classificationMap = CollUtil.isEmpty(classifications) ? Maps.newHashMap() :
                classifications.stream().collect(Collectors.toMap(GoodsClassificationBO::getId,
                        GoodsClassificationBO::getName));
        Map<Long, String> userMap = CollUtil.isEmpty(users) ? Maps.newHashMap() :
                users.stream().collect(Collectors.toMap(UserBO::getId, UserBO::getUsername));
        return SpuDetailVO.builder()
                .id(spu.getId())
                .spuCode(spu.getSpuCode())
                .spuName(spu.getSpuName())
                .description(spu.getDescription())
                .brandId(spu.getBrandId())
                .brand(CollUtil.isEmpty(brands) ? null : brands.get(0).getName())
                .firstClassification(classificationMap.getOrDefault(spu.getFirstClassification(), null))
                .firstClassificationId(spu.getFirstClassification())
                .secondClassification(classificationMap.getOrDefault(spu.getSecondClassification(), null))
                .secondClassificationId(spu.getSecondClassification())
                .thirdClassification(classificationMap.getOrDefault(spu.getThirdClassification(), null))
                .thirdClassificationId(spu.getThirdClassification())
                .fourthClassification(classificationMap.getOrDefault(spu.getFourthClassification(), null))
                .fourthClassificationId(spu.getFourthClassification())
                .channelId(spu.getChannelId())
                .channel(CollUtil.isEmpty(channels) ? null : channels.get(0).getName())
                .seriesId(spu.getSeriesId())
                .series(CollUtil.isEmpty(series) ? null : series.get(0).getName())
                .categoryId(spu.getCategoryId())
                .category(CollUtil.isEmpty(categories) ? null : categories.get(0).getName())
                .creatorId(spu.getCreatorId())
                .updaterId(spu.getUpdaterId())
                .createTime(spu.getCreateTime())
                .updateTime(spu.getUpdateTime())
                .creatorName(userMap.getOrDefault(spu.getCreatorId(), ""))
                .updaterName(userMap.getOrDefault(spu.getUpdaterId(), ""))
                .build();
    }

    // DTO 转换方法 - 用于 Support 层返回

    /**
     * 转换为 SpuPageDTO 列表
     */
    static List<SpuPageDTO> toSpuPageDTO(List<SpuBO> spuBO,
                                         List<UserBO> users,
                                         List<GoodsClassificationBO> classificationBOList,
                                         List<GoodsChannelBO> channels,
                                         List<GoodsSeriesBO> seriesList,
                                         List<GoodsCategoryBO> categoryList,
                                         List<GoodsBrandBO> brandList) {
        Map<Long, String> classificationBOMap = CollUtil.isEmpty(classificationBOList) ?
                Maps.newHashMap() : classificationBOList.stream()
                .collect(Collectors.toMap(GoodsClassificationBO::getId, GoodsClassificationBO::getName));
        Map<Long, String> channelMap = CollUtil.isEmpty(channels) ?
                Maps.newHashMap() : channels.stream()
                .collect(Collectors.toMap(GoodsChannelBO::getId, GoodsChannelBO::getName));
        Map<Long, String> seriesMap = CollUtil.isEmpty(seriesList) ?
                Maps.newHashMap() : seriesList.stream()
                .collect(Collectors.toMap(GoodsSeriesBO::getId, GoodsSeriesBO::getName));
        Map<Long, String> categoryMap = CollUtil.isEmpty(categoryList) ?
                Maps.newHashMap() : categoryList.stream()
                .collect(Collectors.toMap(GoodsCategoryBO::getId, GoodsCategoryBO::getName));
        Map<Long, String> brandMap = CollUtil.isEmpty(brandList) ?
                Maps.newHashMap() : brandList.stream()
                .collect(Collectors.toMap(GoodsBrandBO::getId, GoodsBrandBO::getName));
        Map<Long, String> userMap = CollUtil.isNotEmpty(users) ? users.stream().collect(Collectors.toMap(UserBO::getId,
                UserBO::getUsername)) : Maps.newHashMap();
        return spuBO.stream()
                .map(spu -> convertBOToPageDTO(spu, classificationBOMap, channelMap, seriesMap, categoryMap, brandMap,
                        userMap))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    static SpuPageDTO convertBOToPageDTO(SpuBO spu, Map<Long, String> classificationBOMap, Map<Long, String> channelMap,
                                         Map<Long, String> seriesMap, Map<Long, String> categoryMap,
                                         Map<Long, String> brandMap, Map<Long, String> userMap) {
        return SpuPageDTO.builder()
                .id(spu.getId())
                .spuCode(spu.getSpuCode())
                .spuName(spu.getSpuName())
                .description(spu.getDescription())
                .brandId(spu.getBrandId())
                .brand(brandMap.getOrDefault(spu.getBrandId(), null))
                .firstClassification(ObjectUtil.isNotNull(spu.getFirstClassification()) && classificationBOMap.containsKey(spu.getFirstClassification())
                        ? classificationBOMap.get(spu.getFirstClassification()) : null)
                .firstClassificationId(spu.getFirstClassification())
                .secondClassification(ObjectUtil.isNotNull(spu.getSecondClassification()) && classificationBOMap.containsKey(spu.getSecondClassification())
                        ? classificationBOMap.get(spu.getSecondClassification()) : null)
                .secondClassificationId(spu.getSecondClassification())
                .thirdClassification(ObjectUtil.isNotNull(spu.getThirdClassification()) && classificationBOMap.containsKey(spu.getThirdClassification())
                        ? classificationBOMap.get(spu.getThirdClassification()) : null)
                .thirdClassificationId(spu.getThirdClassification())
                .fourthClassification(ObjectUtil.isNotNull(spu.getFourthClassification()) && classificationBOMap.containsKey(spu.getFourthClassification())
                        ? classificationBOMap.get(spu.getFourthClassification()) : null)
                .fourthClassificationId(spu.getFourthClassification())
                .channelId(spu.getChannelId())
                .channel(channelMap.getOrDefault(spu.getChannelId(), null))
                .seriesId(spu.getSeriesId())
                .series(seriesMap.getOrDefault(spu.getSeriesId(), null))
                .categoryId(spu.getCategoryId())
                .category(categoryMap.getOrDefault(spu.getCategoryId(), null))
                .creatorId(spu.getCreatorId())
                .updaterId(spu.getUpdaterId())
                .createTime(spu.getCreateTime())
                .updateTime(spu.getUpdateTime())
                .creatorName(userMap.getOrDefault(spu.getCreatorId(), ""))
                .updaterName(userMap.getOrDefault(spu.getUpdaterId(), ""))
                .build();
    }

    /**
     * 转换为 SpuDetailDTO
     */
    static SpuDetailDTO toSpuDetailDTO(SpuBO spu,
                                       List<UserBO> users,
                                       List<GoodsClassificationBO> classifications,
                                       List<GoodsChannelBO> channels,
                                       List<GoodsSeriesBO> series,
                                       List<GoodsCategoryBO> categories,
                                       List<GoodsBrandBO> brands) {
        Map<Long, String> classificationMap = CollUtil.isEmpty(classifications) ? Maps.newHashMap() :
                classifications.stream().collect(Collectors.toMap(GoodsClassificationBO::getId,
                        GoodsClassificationBO::getName));
        Map<Long, String> userMap = CollUtil.isEmpty(users) ? Maps.newHashMap() :
                users.stream().collect(Collectors.toMap(UserBO::getId, UserBO::getUsername));
        return SpuDetailDTO.builder()
                .id(spu.getId())
                .spuCode(spu.getSpuCode())
                .spuName(spu.getSpuName())
                .description(spu.getDescription())
                .brandId(spu.getBrandId())
                .brand(CollUtil.isEmpty(brands) ? null : brands.get(0).getName())
                .firstClassification(classificationMap.getOrDefault(spu.getFirstClassification(), null))
                .firstClassificationId(spu.getFirstClassification())
                .secondClassification(classificationMap.getOrDefault(spu.getSecondClassification(), null))
                .secondClassificationId(spu.getSecondClassification())
                .thirdClassification(classificationMap.getOrDefault(spu.getThirdClassification(), null))
                .thirdClassificationId(spu.getThirdClassification())
                .fourthClassification(classificationMap.getOrDefault(spu.getFourthClassification(), null))
                .fourthClassificationId(spu.getFourthClassification())
                .channelId(spu.getChannelId())
                .channel(CollUtil.isEmpty(channels) ? null : channels.get(0).getName())
                .seriesId(spu.getSeriesId())
                .series(CollUtil.isEmpty(series) ? null : series.get(0).getName())
                .categoryId(spu.getCategoryId())
                .category(CollUtil.isEmpty(categories) ? null : categories.get(0).getName())
                .creatorId(spu.getCreatorId())
                .updaterId(spu.getUpdaterId())
                .createTime(spu.getCreateTime())
                .updateTime(spu.getUpdateTime())
                .creatorName(userMap.getOrDefault(spu.getCreatorId(), ""))
                .updaterName(userMap.getOrDefault(spu.getUpdaterId(), ""))
                .build();
    }
}
