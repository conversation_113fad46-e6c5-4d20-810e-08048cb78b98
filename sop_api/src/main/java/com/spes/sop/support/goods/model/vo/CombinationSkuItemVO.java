package com.spes.sop.support.goods.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 组合品SKU项VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CombinationSkuItemVO {
    /**
     * 主键 id
     */
    private Long id;
    /**
     * 货品编码
     */
    private String skuCode;
    /**
     * SKU名称
     */
    private String skuName;
    /**
     * 条码
     */
    private String barCode;
    /**
     * 产品对应SPU
     */
    private String spuCode;
    private String spuName;
    /**
     * 产品规格
     */
    private Long spec;
    /**
     * SKU 数量
     */
    private Integer skuNum;
    /**
     * 是否主品
     */
    private Boolean main;
    /**
     * 是否赠品
     */
    private Boolean gift;
    /**
     * 标准价
     */
    private BigDecimal fairPrice;
    /**
     * 底价
     */
    private BigDecimal basePrice;
    /**
     * 成本
     */
    private BigDecimal cost;
    /**
     * 一级分类
     */
    private String firstClassification;
    /**
     * 二级分类
     */
    private String secondClassification;
    /**
     * 三级分类
     */
    private String thirdClassification;
    /**
     * 系列
     */
    private String series;
    /**
     * 产品分类
     */
    private String category;
} 