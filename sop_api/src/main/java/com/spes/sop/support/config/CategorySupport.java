package com.spes.sop.support.config;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.spes.sop.common.exception.BusinessException;
import com.spes.sop.common.page.BasePager;
import com.spes.sop.common.page.PageResult;
import com.spes.sop.common.security.SecurityContextUtil;
import com.spes.sop.config.enums.ConfigStatusEnum;
import com.spes.sop.config.service.GoodsCategoryService;
import com.spes.sop.config.service.model.bo.GoodsCategoryBO;
import com.spes.sop.config.service.model.command.GoodsCategoryCreateCommand;
import com.spes.sop.config.service.model.command.GoodsCategoryDeleteCommand;
import com.spes.sop.config.service.model.command.GoodsCategoryUpdateCommand;
import com.spes.sop.config.service.model.query.CategoryBOPagerQuery;
import com.spes.sop.controller.config.model.query.CategoryPagerQuery;
import com.spes.sop.controller.config.model.request.CategoryAddRequest;
import com.spes.sop.controller.config.model.request.CategoryEditRequest;
import com.spes.sop.controller.config.model.request.CategoryEnableRequest;
import com.spes.sop.controller.config.model.vo.CategoryVO;
import com.spes.sop.support.config.convert.CategoryConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Collections;
import java.util.Objects;

@Component
@RequiredArgsConstructor
@Slf4j
public class CategorySupport {

    private final GoodsCategoryService goodsCategoryService;

    /**
     * 分页查询分类列表
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public PageResult<CategoryVO> list(@NotNull CategoryPagerQuery query) {
        CategoryBOPagerQuery queryBuilder = CategoryBOPagerQuery.builder()
                .nameSearch(query.getNameSearch())
                .statuses(ObjectUtil.isNotNull(query.getStatuses()) ? Arrays.asList(query.getStatuses()) :
                        Collections.emptyList())
                .pager(new BasePager.Pager(query.getPageNum(), query.getPageSize()))
                .build();
        PageResult<GoodsCategoryBO> categories = goodsCategoryService.pager(queryBuilder);
        return PageResult.of(CollUtil.isEmpty(categories.getRecords()) ? Collections.emptyList() :
                        CategoryConverter.toCategoryVOList(categories.getRecords()),
                categories.getTotal(), query.getPageNum(), query.getPageSize());
    }

    /**
     * 新增分类
     *
     * @param request 新增请求
     */
    public void add(@NotNull CategoryAddRequest request) {
        // 检查名称重复
        if (existsByName(request.getName().trim(), null)) {
            throw new BusinessException("分类名称已存在：" + request.getName().trim());
        }
        // 获取当前操作用户
        Long currentUserId = SecurityContextUtil.getCurrentUserIdLenient();

        // 构建创建命令
        GoodsCategoryCreateCommand command = GoodsCategoryCreateCommand.builder()
                .name(request.getName().trim())
                .description(request.getDescription())
                .creatorId(currentUserId)
                .build();

        // 创建分类
        Long categoryId = goodsCategoryService.createCategory(command);
    }

    /**
     * 修改分类
     *
     * @param request 修改请求
     */
    public void edit(@NotNull CategoryEditRequest request) {
        // 检查名称重复（排除自身）
        if (StrUtil.isNotBlank(request.getName()) &&
                existsByName(request.getName().trim(), request.getId())) {
            throw new BusinessException("分类名称已存在：" + request.getName().trim());
        }

        // 获取当前操作用户
        Long currentUserId = SecurityContextUtil.getCurrentUserIdLenient();

        // 构建更新命令
        GoodsCategoryUpdateCommand command = GoodsCategoryUpdateCommand.builder()
                .id(request.getId())
                .name(StrUtil.isNotBlank(request.getName()) ? request.getName().trim() : null)
                .description(request.getDescription())
                .updaterId(currentUserId)
                .build();

        // 更新分类
        goodsCategoryService.updateCategory(command);
    }

    /**
     * 启用/禁用分类
     *
     * @param request 启用/禁用请求
     */
    public void enable(@NotNull CategoryEnableRequest request) {

        // 获取当前操作用户
        Long currentUserId = SecurityContextUtil.getCurrentUserIdLenient();

        // 获取分类信息后更新
        GoodsCategoryBO category = goodsCategoryService.getById(request.getId());
        ConfigStatusEnum status = ConfigStatusEnum.getByStatus(request.getStatus());
        if (ObjectUtil.isNull(category) || ObjectUtil.isNull(status)) {
            throw new BusinessException("分类不存在或状态值无效");
        }
        // 构建更新命令
        GoodsCategoryUpdateCommand command = GoodsCategoryUpdateCommand.builder()
                .id(request.getId())
                .name(category.getName())
                .status(status)
                .description(category.getDescription())
                .updaterId(currentUserId)
                .build();

        goodsCategoryService.updateCategory(command);
    }

    /**
     * 删除分类
     *
     * @param id 分类ID
     */
    public void delete(@NotNull Long id) {

        // 获取当前操作用户
        Long currentUserId = SecurityContextUtil.getCurrentUserIdLenient();

        // 构建删除命令
        GoodsCategoryDeleteCommand command = GoodsCategoryDeleteCommand.builder()
                .id(id)
                .deleterId(currentUserId)
                .build();

        // 删除分类
        goodsCategoryService.deleteCategory(command);
    }

    /**
     * 检查分类名称是否已存在
     *
     * @param name      分类名称
     * @param excludeId 排除的ID（用于更新时排除自身）
     * @return 是否存在
     */
    public Boolean existsByName(String name, Long excludeId) {
        if (StrUtil.isBlank(name)) {
            return false;
        }

        GoodsCategoryBO category = goodsCategoryService.getByName(name.trim());

        if (ObjectUtil.isNull(category)) {
            return false;
        }
        if (Objects.equals(category.getId(), excludeId)) {
            return false;
        }
        return true;
    }

}
