package com.spes.sop.support.goods.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 组合品SKU项DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CombinationSkuItemDTO {
    /**
     * 主键 id
     */
    private Long id;
    /**
     * 货品编码
     */
    private String skuCode;
    /**
     * SKU名称
     */
    private String skuName;
    /**
     * 条码
     */
    private String barCode;
    /**
     * 产品规格
     */
    private Integer spec;
    /**
     * SKU 数量
     */
    private Integer skuNum;
    /**
     * 是否主品
     */
    private Boolean main;
    /**
     * 标准价
     */
    private BigDecimal fairPrice;
    /**
     * 是否生效
     */
    private Boolean effective;
    /**
     * 生效时间
     */
    private Date effectiveTime;
    /**
     * 失效时间
     */
    private Date expirationTime;
    /**
     * 版本
     */
    private Integer version;

} 