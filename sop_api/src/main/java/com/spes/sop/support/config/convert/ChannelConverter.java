package com.spes.sop.support.config.convert;

import cn.hutool.core.collection.CollUtil;
import com.spes.sop.config.service.model.bo.GoodsChannelBO;
import com.spes.sop.controller.config.model.vo.ChannelVO;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 渠道转换器
 */
public class ChannelConverter {

    /**
     * 转换渠道BO为渠道VO
     *
     * @param channelBO 渠道BO
     * @return 渠道VO
     */
    public static ChannelVO toChannelVO(GoodsChannelBO channelBO) {
        if (channelBO == null) {
            return null;
        }

        return ChannelVO.builder()
                .id(channelBO.getId())
                .name(channelBO.getName())
                .description(channelBO.getDescription())
                .status(channelBO.getStatus() != null ? channelBO.getStatus().name() : null)
                .statusDesc(channelBO.getStatus() != null ? channelBO.getStatus().getStatusDesc() : null)
                .createTime(channelBO.getCreateTime())
                .build();
    }

    /**
     * 转换渠道BO列表为渠道VO列表
     *
     * @param channelBOList 渠道BO列表
     * @return 渠道VO列表
     */
    public static List<ChannelVO> toChannelVOList(List<GoodsChannelBO> channelBOList) {
        if (CollUtil.isEmpty(channelBOList)) {
            return Collections.emptyList();
        }

        return channelBOList.stream()
                .map(ChannelConverter::toChannelVO)
                .collect(Collectors.toList());
    }
} 