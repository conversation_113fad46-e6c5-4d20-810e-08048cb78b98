package com.spes.sop.support.config.convert;

import cn.hutool.core.collection.CollUtil;
import com.spes.sop.config.service.model.bo.GoodsSeriesBO;
import com.spes.sop.controller.config.model.vo.SeriesVO;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 系列转换器
 */
public class SeriesConverter {

    /**
     * 转换系列BO为系列VO
     *
     * @param seriesBO 系列BO
     * @return 系列VO
     */
    public static SeriesVO toSeriesVO(GoodsSeriesBO seriesBO) {
        if (seriesBO == null) {
            return null;
        }

        return SeriesVO.builder()
                .id(seriesBO.getId())
                .name(seriesBO.getName())
                .description(seriesBO.getDescription())
                .status(seriesBO.getStatus() != null ? seriesBO.getStatus().name() : null)
                .statusDesc(seriesBO.getStatus() != null ? seriesBO.getStatus().getStatusDesc() : null)
                .createTime(seriesBO.getCreateTime())
                .build();
    }

    /**
     * 转换系列BO列表为系列VO列表
     *
     * @param seriesBOList 系列BO列表
     * @return 系列VO列表
     */
    public static List<SeriesVO> toSeriesVOList(List<GoodsSeriesBO> seriesBOList) {
        if (CollUtil.isEmpty(seriesBOList)) {
            return Collections.emptyList();
        }

        return seriesBOList.stream()
                .map(SeriesConverter::toSeriesVO)
                .collect(Collectors.toList());
    }
} 