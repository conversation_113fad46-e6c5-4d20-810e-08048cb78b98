package com.spes.sop.support.config.model.vo;

import com.spes.sop.config.enums.ConfigStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ChannelVO {
    /**
     * 主键 id
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 状态
     *
     * @see ConfigStatusEnum
     */
    private String status;
    private String statusDesc;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createTime;
}
