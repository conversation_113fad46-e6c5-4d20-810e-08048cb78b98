package com.spes.sop.support.config;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.spes.sop.common.exception.BusinessException;
import com.spes.sop.common.page.BasePager;
import com.spes.sop.common.page.PageResult;
import com.spes.sop.common.security.SecurityContextUtil;
import com.spes.sop.config.enums.ConfigStatusEnum;
import com.spes.sop.config.service.GoodsSeriesService;
import com.spes.sop.config.service.model.bo.GoodsSeriesBO;
import com.spes.sop.config.service.model.command.GoodsSeriesCreateCommand;
import com.spes.sop.config.service.model.command.GoodsSeriesDeleteCommand;
import com.spes.sop.config.service.model.command.GoodsSeriesUpdateCommand;
import com.spes.sop.config.service.model.query.SeriesBOPagerQuery;
import com.spes.sop.controller.config.model.query.SeriesPagerQuery;
import com.spes.sop.controller.config.model.request.SeriesAddRequest;
import com.spes.sop.controller.config.model.request.SeriesEditRequest;
import com.spes.sop.controller.config.model.request.SeriesEnableRequest;
import com.spes.sop.controller.config.model.vo.SeriesVO;
import com.spes.sop.support.config.convert.SeriesConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Component
@RequiredArgsConstructor
@Slf4j
public class SeriesSupport {
    private final GoodsSeriesService goodsSeriesService;

    /**
     * 分页查询系列列表
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public PageResult<SeriesVO> list(@NotNull SeriesPagerQuery query) {
        SeriesBOPagerQuery queryBuilder = SeriesBOPagerQuery.builder()
                .nameSearch(query.getNameSearch())
                .statuses(ObjectUtil.isNotNull(query.getStatuses()) ? Arrays.asList(query.getStatuses()) :
                        Collections.emptyList())
                .pager(new BasePager.Pager(query.getPageNum(), query.getPageSize()))
                .build();
        PageResult<GoodsSeriesBO> series = goodsSeriesService.pager(queryBuilder);
        return PageResult.of(CollUtil.isEmpty(series.getRecords()) ? Collections.emptyList() :
                        SeriesConverter.toSeriesVOList(series.getRecords()),
                series.getTotal(), query.getPageNum(), query.getPageSize());
    }

    /**
     * 新增系列
     *
     * @param request 新增请求
     */
    public void add(@NotNull SeriesAddRequest request) {
        // 检查名称重复
        if (existsByName(request.getName().trim(), null)) {
            throw new BusinessException("系列名称已存在：" + request.getName().trim());
        }

        // 获取当前操作用户
        Long currentUserId = SecurityContextUtil.getCurrentUserIdLenient();

        // 构建创建命令
        GoodsSeriesCreateCommand command = GoodsSeriesCreateCommand.builder()
                .name(request.getName().trim())
                .description(request.getDescription())
                .creatorId(currentUserId)
                .build();

        // 创建系列
        Long seriesId = goodsSeriesService.createSeries(command);
        log.info("新增系列成功，ID：{}，名称：{}", seriesId, request.getName());
    }

    /**
     * 修改系列
     *
     * @param request 修改请求
     */
    public void edit(@NotNull SeriesEditRequest request) {
        // 检查名称重复（排除自身）
        if (StrUtil.isNotBlank(request.getName()) &&
                existsByName(request.getName().trim(), request.getId())) {
            throw new BusinessException("系列名称已存在：" + request.getName().trim());
        }

        // 获取当前操作用户
        Long currentUserId = SecurityContextUtil.getCurrentUserIdLenient();

        // 构建更新命令
        GoodsSeriesUpdateCommand command = GoodsSeriesUpdateCommand.builder()
                .id(request.getId())
                .name(StrUtil.isNotBlank(request.getName()) ? request.getName().trim() : null)
                .description(request.getDescription())
                .updaterId(currentUserId)
                .build();

        // 更新系列
        goodsSeriesService.updateSeries(command);
        log.info("修改系列成功，ID：{}，名称：{}", request.getId(), request.getName());
    }

    /**
     * 启用/禁用系列
     *
     * @param request 启用/禁用请求
     */
    public void enable(@NotNull SeriesEnableRequest request) {
        // 获取当前操作用户
        Long currentUserId = SecurityContextUtil.getCurrentUserIdLenient();

        GoodsSeriesBO series = getSeriesById(request.getId());
        ConfigStatusEnum status = ConfigStatusEnum.valueOf(request.getStatus());
        if (ObjectUtil.isNull(series) || ObjectUtil.isNull(status)) {
            throw new BusinessException("系列不存在或状态无效");
        }
        // 构建更新命令
        GoodsSeriesUpdateCommand command = GoodsSeriesUpdateCommand.builder()
                .id(request.getId())
                .status(status)
                .updaterId(currentUserId)
                .build();
        goodsSeriesService.updateSeries(command);
    }

    /**
     * 删除系列
     *
     * @param id 系列ID
     */
    public void delete(@NotNull Long id) {
        // 获取当前操作用户
        Long currentUserId = SecurityContextUtil.getCurrentUserIdLenient();

        // 构建删除命令
        GoodsSeriesDeleteCommand command = GoodsSeriesDeleteCommand.builder()
                .id(id)
                .deleterId(currentUserId)
                .build();

        // 删除系列
        goodsSeriesService.deleteSeries(command);
        log.info("删除系列成功，ID：{}", id);
    }

    /**
     * 检查系列名称是否已存在
     *
     * @param name      系列名称
     * @param excludeId 排除的ID（用于更新时排除自身）
     * @return 是否存在
     */
    public Boolean existsByName(String name, Long excludeId) {
        if (StrUtil.isBlank(name)) {
            return false;
        }

        GoodsSeriesBO series = getSeriesByName(name);
        if (ObjectUtil.isNull(series)) {
            return false;
        }
        if (Objects.equals(series.getId(), excludeId)) {
            return false;
        }
        return true;
    }

    /**
     * 根据ID获取系列
     *
     * @param id 系列ID
     * @return 系列信息
     */
    private GoodsSeriesBO getSeriesById(Long id) {
        if (ObjectUtil.isNull(id)) {
            return null;
        }
        List<GoodsSeriesBO> seriesList = goodsSeriesService.list(SeriesBOPagerQuery.builder()
                .ids(Collections.singletonList(id))
                .pager(new BasePager.Pager(1, 1))
                .build());
        return CollUtil.isEmpty(seriesList) ? null : seriesList.get(0);
    }

    /**
     * 根据名称获取系列
     *
     * @param name 系列名称
     * @return 系列信息
     */
    private GoodsSeriesBO getSeriesByName(String name) {
        if (StrUtil.isBlank(name)) {
            return null;
        }
        List<GoodsSeriesBO> seriesList = goodsSeriesService.list(SeriesBOPagerQuery.builder()
                .names(Collections.singletonList(name.trim()))
                .pager(new BasePager.Pager(1, 1))
                .build());
        return CollUtil.isEmpty(seriesList) ? null : seriesList.get(0);
    }
}
