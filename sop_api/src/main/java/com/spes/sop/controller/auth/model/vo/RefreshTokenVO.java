package com.spes.sop.controller.auth.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 刷新Token响应VO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefreshTokenVO {

    /**
     * 新的访问Token
     */
    private String accessToken;

    /**
     * 新的刷新Token
     */
    private String refreshToken;

    /**
     * Token类型
     */
    private String tokenType;

    /**
     * 访问Token过期时间（秒）
     */
    private Long expiresIn;

    /**
     * 刷新Token过期时间（秒）
     */
    private Long refreshExpiresIn;
} 
