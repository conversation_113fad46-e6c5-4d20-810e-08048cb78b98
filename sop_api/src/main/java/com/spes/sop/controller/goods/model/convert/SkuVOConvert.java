package com.spes.sop.controller.goods.model.convert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.spes.sop.controller.goods.model.vo.SkuDetailVO;
import com.spes.sop.controller.goods.model.vo.SkuListVO;
import com.spes.sop.support.goods.model.dto.SkuDetailDTO;
import com.spes.sop.support.goods.model.dto.SkuListDTO;
import com.spes.sop.support.goods.model.dto.SpuDetailDTO;
import com.spes.sop.support.goods.model.dto.SpuPageDTO;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public interface SkuVOConvert {


    static List<SkuListVO> skuListConvert(List<SkuListDTO> records, List<SpuPageDTO> spuInfos) {
        if (CollUtil.isEmpty(records)) {
            return Lists.newArrayList();
        }
        Map<Long, SpuPageDTO> spuMap = CollUtil.isNotEmpty(spuInfos) ?
                spuInfos.stream().collect(Collectors.toMap(SpuPageDTO::getId, Function.identity())) :
                Maps.newHashMap();
        return records.stream().map(sku -> {
            SpuPageDTO spu = spuMap.get(sku.getSpuId());
            return convert(sku, spu);
        }).collect(Collectors.toList());
    }

    static SkuListVO convert(SkuListDTO sku, SpuPageDTO spu) {
        return SkuListVO.builder()
                .id(sku.getId())
                .skuCode(sku.getSkuCode())
                .skuName(sku.getSkuName())
                .barCode(sku.getBarCode())
                .brand(ObjectUtil.isNull(spu) ? null : spu.getBrand())
                .spuCode(ObjectUtil.isNull(spu) ? null : spu.getSpuCode())
                .spuName(ObjectUtil.isNull(spu) ? null : spu.getSpuName())
                .spec(sku.getSpec())
                .onlineTime(sku.getOnlineTime())
                .firstClassification(ObjectUtil.isNull(spu) ? null : spu.getFirstClassification())
                .secondClassification(ObjectUtil.isNull(spu) ? null : spu.getSecondClassification())
                .thirdClassification(ObjectUtil.isNull(spu) ? null : spu.getThirdClassification())
                .series(ObjectUtil.isNull(spu) ? null : spu.getSeries())
                .category(ObjectUtil.isNull(spu) ? null : spu.getCategory())
                .channel(ObjectUtil.isNull(spu) ? null : spu.getChannel())
                .gift(sku.getGift())
                .card(sku.getCard())
                .status(sku.getStatus())
                .statusDesc(sku.getStatusDesc())
                .syncFail(sku.getSyncFail())
                .fairPrice(sku.getFairPrice())
                .basePrice(sku.getBasePrice())
                .cost(sku.getCost())
                .createTime(sku.getCreateTime())
                .creatorName(sku.getCreatorName())
                .updateTime(sku.getUpdateTime())
                .updaterName(sku.getUpdaterName())
                .build();
    }

    static SkuDetailVO skuDetailConvert(SkuDetailDTO skuDetail, SpuDetailDTO spuDetail) {
        return SkuDetailVO.builder()
                .id(skuDetail.getId())
                .skuCode(skuDetail.getSkuCode())
                .skuName(skuDetail.getSkuName())
                .barCode(skuDetail.getBarCode())
                .spuCode(ObjectUtil.isNull(spuDetail) ? null : spuDetail.getSpuCode())
                .spuName(ObjectUtil.isNull(spuDetail) ? null : spuDetail.getSpuName())
                .filingNumber(skuDetail.getFilingNumber())
                .spec(skuDetail.getSpec())
                .brand(ObjectUtil.isNull(spuDetail) ? null : spuDetail.getBrand())
                .firstClassification(ObjectUtil.isNull(spuDetail) ? null : spuDetail.getFirstClassification())
                .secondClassification(ObjectUtil.isNull(spuDetail) ? null : spuDetail.getSecondClassification())
                .thirdClassification(ObjectUtil.isNull(spuDetail) ? null : spuDetail.getThirdClassification())
                .fourthClassification(ObjectUtil.isNull(spuDetail) ? null : spuDetail.getFourthClassification())
                .series(ObjectUtil.isNull(spuDetail) ? null : spuDetail.getSeries())
                .category(ObjectUtil.isNull(spuDetail) ? null : spuDetail.getCategory())
                .channel(ObjectUtil.isNull(spuDetail) ? null : spuDetail.getChannel())
                .gift(skuDetail.getGift())
                .packageCard(skuDetail.getPackageCard())
                .status(skuDetail.getStatus())
                .statusDesc(skuDetail.getStatusDesc())
                .syncFail(skuDetail.getSyncFail())
                .fairPrice(skuDetail.getFairPrice())
                .basePrice(skuDetail.getBasePrice())
                .cost(skuDetail.getCost())
                .businessInfo(skuDetail.getBusinessInfo())
                .productInfo(skuDetail.getProductInfo())
                .supplyChainInfo(skuDetail.getSupplyChainInfo())
                .createTime(skuDetail.getCreateTime())
                .creatorName(skuDetail.getCreatorName())
                .updateTime(skuDetail.getUpdateTime())
                .updaterName(skuDetail.getUpdaterName())
                .build();
    }
}
