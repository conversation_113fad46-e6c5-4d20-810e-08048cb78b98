package com.spes.sop.controller.goods.model.convert;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.spes.sop.controller.goods.model.vo.SkuListVO;
import com.spes.sop.controller.goods.model.vo.SpuDetailVO;
import com.spes.sop.controller.goods.model.vo.SpuPageVO;
import com.spes.sop.support.goods.model.dto.SkuListDTO;
import com.spes.sop.support.goods.model.dto.SpuDetailDTO;
import com.spes.sop.support.goods.model.dto.SpuPageDTO;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public interface SpuVOConverter {

    static List<SpuPageVO> toSpuPageVOList(List<SpuPageDTO> records, List<SkuListDTO> skuInfos) {
        if (CollUtil.isEmpty(records)) {
            return Lists.newArrayList();
        }
        Map<Long, List<SkuListDTO>> skuMap = CollUtil.isNotEmpty(skuInfos) ?
                skuInfos.stream().collect(Collectors.groupingBy(SkuListDTO::getSpuId)) :
                Maps.newHashMap();
        return records.stream().map(spu -> {
            List<SkuListDTO> skus = skuMap.get(spu.getId());
            return convert(spu, skus);
        }).collect(Collectors.toList());
    }

    static SpuPageVO convert(SpuPageDTO spu, List<SkuListDTO> skus) {
        return SpuPageVO.builder()
                .id(spu.getId())
                .spuCode(spu.getSpuCode())
                .spuName(spu.getSpuName())
                .description(spu.getDescription())
                .brandId(spu.getBrandId())
                .brand(spu.getBrand())
                .firstClassification(spu.getFirstClassification())
                .firstClassificationId(spu.getFirstClassificationId())
                .secondClassification(spu.getSecondClassification())
                .secondClassificationId(spu.getSecondClassificationId())
                .thirdClassification(spu.getThirdClassification())
                .thirdClassificationId(spu.getThirdClassificationId())
                .fourthClassification(spu.getFourthClassification())
                .fourthClassificationId(spu.getFourthClassificationId())
                .channelId(spu.getChannelId())
                .channel(spu.getChannel())
                .seriesId(spu.getSeriesId())
                .series(spu.getSeries())
                .categoryId(spu.getCategoryId())
                .category(spu.getCategory())
                .creatorId(spu.getCreatorId())
                .creatorName(spu.getCreatorName())
                .updaterId(spu.getUpdaterId())
                .updaterName(spu.getUpdaterName())
                .createTime(spu.getCreateTime())
                .updateTime(spu.getUpdateTime())
                .skus(CollUtil.isEmpty(skus) ? null :
                        skus.stream().map(SkuListDTO::getSkuName).collect(Collectors.toList()))
                .build();
    }

    static SpuDetailVO toSpuDetailVO(SpuDetailDTO spuDetail, List<SkuListDTO> skuInfos) {
        return SpuDetailVO.builder()
                .id(spuDetail.getId())
                .spuCode(spuDetail.getSpuCode())
                .spuName(spuDetail.getSpuName())
                .description(spuDetail.getDescription())
                .brandId(spuDetail.getBrandId())
                .brand(spuDetail.getBrand())
                .firstClassification(spuDetail.getFirstClassification())
                .firstClassificationId(spuDetail.getFirstClassificationId())
                .secondClassification(spuDetail.getSecondClassification())
                .secondClassificationId(spuDetail.getSecondClassificationId())
                .thirdClassification(spuDetail.getThirdClassification())
                .thirdClassificationId(spuDetail.getThirdClassificationId())
                .fourthClassification(spuDetail.getFourthClassification())
                .fourthClassificationId(spuDetail.getFourthClassificationId())
                .channelId(spuDetail.getChannelId())
                .channel(spuDetail.getChannel())
                .seriesId(spuDetail.getSeriesId())
                .series(spuDetail.getSeries())
                .categoryId(spuDetail.getCategoryId())
                .category(spuDetail.getCategory())
                .creatorId(spuDetail.getCreatorId())
                .creatorName(spuDetail.getCreatorName())
                .updaterId(spuDetail.getUpdaterId())
                .updaterName(spuDetail.getUpdaterName())
                .createTime(spuDetail.getCreateTime())
                .updateTime(spuDetail.getUpdateTime())
                .skuItems(CollUtil.isEmpty(skuInfos) ? null :
                        skuInfos.stream().map(a -> convert(a)).collect(Collectors.toList()))
                .build();
    }

    static SkuListVO convert(SkuListDTO sku) {
        return SkuListVO.builder()
                .id(sku.getId())
                .skuCode(sku.getSkuCode())
                .skuName(sku.getSkuName())
                .barCode(sku.getBarCode())
                .spec(sku.getSpec())
                .onlineTime(sku.getOnlineTime())
                .gift(sku.getGift())
                .card(sku.getCard())
                .status(sku.getStatus())
                .statusDesc(sku.getStatusDesc())
                .syncFail(sku.getSyncFail())
                .fairPrice(sku.getFairPrice())
                .basePrice(sku.getBasePrice())
                .cost(sku.getCost())
                .createTime(sku.getCreateTime())
                .creatorName(sku.getCreatorName())
                .updateTime(sku.getUpdateTime())
                .updaterName(sku.getUpdaterName())
                .build();
    }
}
