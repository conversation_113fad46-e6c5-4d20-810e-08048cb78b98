package com.spes.sop.controller.goods.model.query;

import com.spes.sop.common.enums.GoodsStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 组合品分页查询请求
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CombinationPageQuery {
    /**
     * 组合品名称（模糊查询）
     */
    @Size(max = 255, message = "组合品名称长度不能超过255个字符")
    private String combinationNameSearch;

    /**
     * 组合品编码（精确查询）
     */
    @Size(max = 50, message = "组合品编码长度不能超过50个字符")
    private String combinationCode;

    /**
     * 一级分类
     */
    private Long firstClassification;

    /**
     * 二级分类
     */
    private Long secondClassification;

    /**
     * 三级分类
     */
    private Long thirdClassification;

    /**
     * 系列
     */
    private Long seriesId;
    /**
     * 产品分类
     */
    private List<Long> categoryIds;

    /**
     * 状态
     * @see GoodsStatusEnum#name()
     */
    private List<String> statuses;

    /**
     * 是否赠品
     */
    private Boolean gift;
    /**
     * 是否为包裹卡
     */
    private Boolean packageCard;

    /**
     * 页码（从1开始）
     */
    @Min(value = 1, message = "页码必须大于0")
    @Builder.Default
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    @Builder.Default
    private Integer pageSize = 10;
} 
