package com.spes.sop.controller.user;

import cn.hutool.core.util.ObjectUtil;
import com.spes.sop.common.exception.BusinessException;
import com.spes.sop.common.page.PageResult;
import com.spes.sop.common.result.Result;
import com.spes.sop.common.security.JwtUserDetails;
import com.spes.sop.common.security.SecurityContextUtil;
import com.spes.sop.controller.user.converter.UserControllerConverter;
import com.spes.sop.controller.user.model.query.UserListQuery;
import com.spes.sop.controller.user.model.request.UserCreateRequest;
import com.spes.sop.controller.user.model.request.UserPasswordUpdateRequest;
import com.spes.sop.controller.user.model.vo.UserVO;
import com.spes.sop.third.weaver.model.response.UserListPayload;
import com.spes.sop.third.weaver.service.WeaverWorkflowService;
import com.spes.sop.user.service.user.UserService;
import com.spes.sop.user.service.user.model.bo.UserBO;
import com.spes.sop.user.service.user.model.command.UserCreateCommand;
import com.spes.sop.user.service.user.model.command.UserPasswordUpdateCommand;
import com.spes.sop.user.service.user.model.query.UserGetQuery;
import com.spes.sop.user.service.user.model.query.UserPageQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * 用户管理控制器
 * 提供用户的增删改查、密码管理等功能
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/user")
public class UserController {

    private final UserService userService;

    private final WeaverWorkflowService weaverWorkflowService;

    /**
     * 分页查询用户列表
     *
     * @param query 查询条件
     * @return 用户分页列表
     */
    @PostMapping("/page")
    public Result<PageResult<UserVO>> getUserPage(@Validated @RequestBody UserListQuery query) {
        Objects.requireNonNull(query, "查询条件不能为空");

        log.info("分页查询用户列表，查询条件：{}", query);

        try {
            // 转换查询条件
            UserPageQuery pageQuery = UserControllerConverter.toUserPageQuery(query);

            // 查询用户列表和总数
            List<UserBO> userBOList = userService.getUserPage(pageQuery);
            Long totalCount = userService.getUserCount(pageQuery);

            // 转换为VO
            List<UserVO> userVOList = UserControllerConverter.toUserVOList(userBOList);

            // 构建分页结果
            PageResult<UserVO> pageResult = PageResult.of(userVOList, totalCount, query.getPageNum(),
                    query.getPageSize());

            log.info("分页查询用户列表成功，返回{}条记录，总数：{}", userVOList.size(), totalCount);

            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("分页查询用户列表失败，查询条件：{}", query, e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 新增用户
     *
     * @param request 用户创建请求
     * @return 创建结果
     */
    @PostMapping("/create")
    public Result<UserVO> createUser(@Validated @RequestBody UserCreateRequest request) {
        Objects.requireNonNull(request, "用户创建请求不能为空");

        log.info("新增用户，用户名：{}，工号：{}", request.getUsername(), request.getEmployeeId());

        UserListPayload.UserInfo weaverUser = weaverWorkflowService.getWeaverUser(request.getEmployeeId());
        if (ObjectUtil.isNull(weaverUser)) {
            throw new BusinessException("工号不存在");
        }
        // 获取当前操作用户
        Long currentUserId = SecurityContextUtil.getCurrentUserIdLenient();

        // 转换为Service层命令
        UserCreateCommand createCommand = UserControllerConverter.toUserCreateCommand(request, currentUserId,
                weaverUser);

        // 创建用户
        UserBO createdUser = userService.createUser(createCommand);

        // 转换为VO
        UserVO userVO = UserControllerConverter.toUserVO(createdUser);

        log.info("新增用户成功，用户ID：{}，用户名：{}", createdUser.getId(), createdUser.getUsername());

        return Result.success(userVO);
    }

    /**
     * 修改用户密码
     *
     * @param request 密码修改请求
     * @return 修改结果
     */
    @PostMapping("/password/update")
    public Result<Void> updatePassword(@Validated @RequestBody UserPasswordUpdateRequest request) {
        Objects.requireNonNull(request, "密码修改请求不能为空");
        // 获取当前操作用户

        Long currentUserId = SecurityContextUtil.getCurrentUserIdLenient();
        try {
            // 转换为Service层命令
            UserPasswordUpdateCommand updateCommand = UserControllerConverter.toUserPasswordUpdateCommand(request,
                    currentUserId);
            // 更新密码
            boolean success = userService.updateUserPassword(updateCommand);

            if (!success) {
                log.warn("修改用户密码失败，用户ID：{}", currentUserId);
                return Result.error("修改密码失败");
            }

            log.info("修改用户密码成功，用户ID：{}", currentUserId);

            return Result.success();

        } catch (Exception e) {
            log.error("修改用户密码失败，用户ID：{}", currentUserId, e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 重置用户密码（管理员功能）
     *
     * @param userId 用户ID
     * @return 重置结果
     */
    @PostMapping("/{userId}/password/reset")
    public Result<String> resetPassword(@PathVariable @NotNull Long userId) {
        log.info("重置用户密码，用户ID：{}", userId);

        try {
            // 获取当前操作用户
            JwtUserDetails userDetails = SecurityContextUtil.getCurrentUserDetails().get();
            if (!userDetails.getRoles().contains("ADMIN")) {
                throw new BusinessException("不是管理员不能重置密码");
            }

            // 生成默认密码（可以根据业务需求调整）
            String defaultPassword = "123456";

            // 重置密码
            boolean success = userService.resetUserPassword(userId, defaultPassword, userDetails.getUserId());

            if (!success) {
                log.warn("重置用户密码失败，用户ID：{}", userId);
                return Result.error("重置密码失败");
            }

            log.info("重置用户密码成功，用户ID：{}", userId);

            return Result.success("重置密码成功，新密码：" + defaultPassword);

        } catch (Exception e) {
            log.error("重置用户密码失败，用户ID：{}", userId, e);
            return Result.error(e.getMessage());
        }
    }


    /**
     * 检查工号是否存在
     *
     * @param employeeId 工号
     * @return 是否存在
     */
    @GetMapping("/check/employee")
    public Result<Boolean> checkEmployeeIdExists(@RequestParam String employeeId) {
        log.info("检查工号是否存在，工号：{}", employeeId);

        try {
            UserGetQuery getQuery = UserGetQuery.builder()
                    .employeeId(employeeId)
                    .build();

            UserBO existingUser = userService.getUser(getQuery);
            boolean exists = existingUser != null;

            log.info("检查工号是否存在完成，工号：{}，存在：{}", employeeId, exists);

            return Result.success(exists);

        } catch (Exception e) {
            log.error("检查工号是否存在失败，工号：{}", employeeId, e);
            return Result.error(e.getMessage());
        }
    }
}
