package com.spes.sop.controller.user.converter;

import cn.hutool.core.util.ObjectUtil;
import com.spes.sop.controller.user.model.query.UserListQuery;
import com.spes.sop.controller.user.model.request.UserCreateRequest;
import com.spes.sop.controller.user.model.request.UserPasswordUpdateRequest;
import com.spes.sop.controller.user.model.vo.UserVO;
import com.spes.sop.third.weaver.model.response.UserListPayload;
import com.spes.sop.user.service.user.model.bo.UserBO;
import com.spes.sop.user.service.user.model.command.UserCreateCommand;
import com.spes.sop.user.service.user.model.command.UserPasswordUpdateCommand;
import com.spes.sop.user.service.user.model.query.UserPageQuery;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用户Controller层转换器
 * 负责Controller层模型与Service层模型之间的转换
 *
 * <AUTHOR>
 */
public class UserControllerConverter {

    /**
     * UserListQuery转换为UserPageQuery
     *
     * @param listQuery Controller层查询对象
     * @return Service层分页查询对象
     */
    public static UserPageQuery toUserPageQuery(UserListQuery listQuery) {
        if (listQuery == null) {
            return null;
        }

        UserPageQuery.UserPageQueryBuilder builder = UserPageQuery.builder()
                .pageNum(listQuery.getPageNum())
                .pageSize(listQuery.getPageSize());

        // 用户名模糊查询
        if (StringUtils.hasText(listQuery.getUsername())) {
            builder.username(listQuery.getUsername().trim());
        }

        // 工号精确查询
        if (StringUtils.hasText(listQuery.getEmployeeId())) {
            builder.employeeIds(Collections.singletonList(listQuery.getEmployeeId().trim()));
        }

        // 角色精确查询
        if (StringUtils.hasText(listQuery.getRole())) {
            builder.roles(Collections.singletonList(listQuery.getRole().trim()));
        }

        //部门精确查询
        if (StringUtils.hasText(listQuery.getDepartment())) {
            builder.departments(Collections.singletonList(listQuery.getDepartment().trim()));
        }
        return builder.build();
    }

    /**
     * UserCreateRequest转换为UserCreateCommand
     *
     * @param createRequest Controller层创建请求
     * @param operatorId    操作人ID
     * @return Service层创建命令
     */
    public static UserCreateCommand toUserCreateCommand(UserCreateRequest createRequest, Long operatorId,
                                                        UserListPayload.UserInfo weaverUser) {
        if (createRequest == null) {
            return null;
        }

        return UserCreateCommand.builder()
                .username(createRequest.getUsername())
                .employeeId(createRequest.getEmployeeId())
                .role(createRequest.getRole())
                .department(weaverUser.getDepartmentname())
                .weaverId(Long.valueOf(weaverUser.getId()))
                .password(createRequest.getPassword())
                .operatorId(operatorId)
                .build();
    }

    /**
     * UserPasswordUpdateRequest转换为UserPasswordUpdateCommand
     *
     * @param updateRequest Controller层密码更新请求
     * @param operatorId    操作人ID
     * @return Service层密码更新命令
     */
    public static UserPasswordUpdateCommand toUserPasswordUpdateCommand(UserPasswordUpdateRequest updateRequest,
                                                                        Long operatorId) {
        if (updateRequest == null) {
            return null;
        }

        return UserPasswordUpdateCommand.builder()
                .userId(operatorId)
                .oldPassword(updateRequest.getOldPassword())
                .newPassword(updateRequest.getNewPassword())
                .operatorId(operatorId)
                .build();
    }

    /**
     * UserBO转换为UserVO
     *
     * @param userBO Service层业务对象
     * @return Controller层视图对象
     */
    public static UserVO toUserVO(UserBO userBO) {
        if (userBO == null) {
            return null;
        }

        return UserVO.builder()
                .id(userBO.getId())
                .username(userBO.getUsername())
                .employeeId(userBO.getEmployeeId())
                .role(ObjectUtil.isNotNull(userBO.getRole()) ? userBO.getRole().name() : null)
                .roleDesc(ObjectUtil.isNotNull(userBO.getRole()) ? userBO.getRole().getDescription() : null)
                .department(userBO.getDepartment())
                .weaverId(userBO.getWeaverId())
                .createTime(userBO.getCreateTime())
                .updateTime(userBO.getUpdateTime())
                .build();
    }

    /**
     * UserBO列表转换为UserVO列表
     *
     * @param userBOList Service层业务对象列表
     * @return Controller层视图对象列表
     */
    public static List<UserVO> toUserVOList(List<UserBO> userBOList) {
        if (CollectionUtils.isEmpty(userBOList)) {
            return Collections.emptyList();
        }

        return userBOList.stream()
                .filter(Objects::nonNull)
                .map(UserControllerConverter::toUserVO)
                .collect(Collectors.toList());
    }
} 