package com.spes.sop.controller.goods.model.vo.value;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 产品立项申请VO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuBusinessInfoValue {

    /**
     * 立项申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date applicationDate;

    /**
     * 产品上线时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date launchDate;

    /**
     * 预计售价
     */
    private BigDecimal estimatedPrice;

    /**
     * 是否渠道特供品（true-是，false-否）
     */
    private Boolean channelExclusive;

    /**
     * 产品用途
     */
    private String productPurpose;

    /**
     * 首批需求数量
     */
    private Integer firstBatchQuantity;

    /**
     * 工厂起订量
     */
    private Integer factoryMinOrderQuantity;

    /**
     * 最少起订量
     */
    private Integer minOrderQuantity;

    /**
     * 最少起订原因
     */
    private String minOrderReason;

    /**
     * 效期要求
     */
    private String shelfLifeRequirement;

    /**
     * 是否包销（true-是，false-否）
     */
    private Boolean exclusiveSale;

    /**
     * 销售负责人工号
     */
    private String salesResponsibleEmployeeId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 附件列表
     */
    private List<AttachmentValue> attachments;


} 
