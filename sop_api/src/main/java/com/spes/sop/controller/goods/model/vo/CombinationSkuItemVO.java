package com.spes.sop.controller.goods.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 组合品SKU项VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CombinationSkuItemVO {
    /**
     * 主键 id
     */
    private Long id;
    /**
     * 货品编码
     */
    private String skuCode;
    /**
     * SKU名称
     */
    private String skuName;
    /**
     * 条码
     */
    private String barCode;
    /**
     * 产品规格
     */
    private Integer spec;
    /**
     * SKU 数量
     */
    private Integer skuNum;
    /**
     * 是否主品
     */
    private Boolean main;
    /**
     * 是否赠品
     */
    private Boolean gift;
    /**
     * 标准价
     */
    private BigDecimal fairPrice;
    /**
     * 生效状态
     */
    private Boolean effective;
    /**
     * 生效时间
     */
    private Date effectiveTime;
    /**
     * 失效时间
     */
    private Date expirationTime;
} 