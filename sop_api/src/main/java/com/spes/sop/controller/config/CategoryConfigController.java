package com.spes.sop.controller.config;

import com.spes.sop.common.annotation.OperationLog;
import com.spes.sop.common.page.PageResult;
import com.spes.sop.common.result.Result;
import com.spes.sop.controller.config.model.query.CategoryPagerQuery;
import com.spes.sop.controller.config.model.request.CategoryAddRequest;
import com.spes.sop.controller.config.model.request.CategoryEditRequest;
import com.spes.sop.controller.config.model.request.CategoryEnableRequest;
import com.spes.sop.controller.config.model.vo.CategoryVO;
import com.spes.sop.support.config.CategorySupport;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;

/**
 * 产品分类管理控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/category")
public class CategoryConfigController {

    private final CategorySupport categorySupport;

    /**
     * 分页查询
     */
    @OperationLog("分页查询产品分类")
    @PostMapping("/list")
    public Result<PageResult<CategoryVO>> list(@RequestBody @Validated @NotNull CategoryPagerQuery query) {
        return Result.success(categorySupport.list(query));
    }

    /**
     * 新增
     */
    @OperationLog("新增产品分类")
    @PostMapping("/add")
    public Result<Void> add(@Validated @RequestBody @NotNull CategoryAddRequest request) {
        categorySupport.add(request);
        return Result.success();
    }

    /**
     * 修改
     */
    @OperationLog("修改产品分类")
    @PostMapping("/edit")
    public Result<Void> edit(@Validated @RequestBody @NotNull CategoryEditRequest request) {
        categorySupport.edit(request);
        return Result.success();
    }


    /**
     * 启用/禁用
     */
    @OperationLog("启用/禁用产品分类")
    @PostMapping("/enable")
    public Result<Void> enable(@RequestBody @Validated @NotNull CategoryEnableRequest request) {
        categorySupport.enable(request);
        return Result.success();
    }

    /**
     * 删除
     */
    @OperationLog("删除产品分类")
    @PostMapping("/delete")
    public Result<Void> delete(@NotNull Long id) {
        categorySupport.delete(id);
        return Result.success();
    }
}
