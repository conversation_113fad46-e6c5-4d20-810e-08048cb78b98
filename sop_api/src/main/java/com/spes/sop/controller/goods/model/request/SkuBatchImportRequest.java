package com.spes.sop.controller.goods.model.request;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * SKU批量导入请求模型
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuBatchImportRequest {

    /**
     * SKU名称
     */
    @ExcelProperty(value = "SKU名称", index = 0)
    private String skuName;

    /**
     * 货品规格
     */
    @ExcelProperty(value = "货品规格", index = 1)
    private Long specification;

    /**
     * 立项申请时间
     */
    @ExcelProperty(value = "立项申请时间", index = 2)
    @DateTimeFormat("yyyy-MM-dd")
    private Date applicationDate;

    /**
     * 产品上线时间
     */
    @ExcelProperty(value = "产品上线时间", index = 3)
    @DateTimeFormat("yyyy-MM-dd")
    private Date launchDate;

    /**
     * 是否渠道特供品（是/否）
     */
    @ExcelProperty(value = "是否渠道特供品", index = 4)
    private String channelExclusiveStr;

    /**
     * 产品用途
     */
    @ExcelProperty(value = "产品用途", index = 5)
    private String productPurpose;

    /**
     * 预估售价
     */
    @ExcelProperty(value = "预估售价", index = 6)
    private BigDecimal estimatedPrice;

    /**
     * 首批需求数量
     */
    @ExcelProperty(value = "首批需求数量", index = 7)
    private Integer firstBatchQuantity;

    /**
     * 工厂起订量
     */
    @ExcelProperty(value = "工厂起订量", index = 8)
    private Integer factoryMinOrderQuantity;

    /**
     * 最少起订量
     */
    @ExcelProperty(value = "最少起订量", index = 9)
    private Integer minOrderQuantity;

    /**
     * 最少起订原因
     */
    @ExcelProperty(value = "最少起订原因", index = 10)
    private String minOrderReason;

    /**
     * 效期要求
     */
    @ExcelProperty(value = "效期要求", index = 11)
    private String shelfLifeRequirement;

    /**
     * 是否包销（是/否）
     */
    @ExcelProperty(value = "是否包销", index = 12)
    private String exclusiveSaleStr;

    /**
     * 销售负责人
     */
    @ExcelProperty(value = "销售负责人", index = 13)
    private String salesResponsibleEmployeeId;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注", index = 14)
    private String remark;

    // 转换方法：将字符串转换为布尔值
    public Boolean getChannelExclusive() {
        if (channelExclusiveStr == null) {
            return null;
        }
        return "是".equals(channelExclusiveStr.trim());
    }

    public Boolean getExclusiveSale() {
        if (exclusiveSaleStr == null) {
            return null;
        }
        return "是".equals(exclusiveSaleStr.trim());
    }
} 