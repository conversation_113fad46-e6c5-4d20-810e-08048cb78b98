package com.spes.sop.controller.goods.model.vo.value;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 组合品关联ID集合
 * 用于收集组合品列表中的所有关联ID，便于批量查询
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CombinationRelatedIds {

    /**
     * SKU ID列表
     */
    private List<Long> skuIds = new ArrayList<>();

    /**
     * 分类ID列表
     */
    private List<Long> categoryIds = new ArrayList<>();

    /**
     * 渠道ID列表
     */
    private List<Long> channelIds = new ArrayList<>();

    /**
     * 系列ID列表
     */
    private List<Long> seriesIds = new ArrayList<>();

    /**
     * 分类ID列表（一、二、三级分类）
     */
    private List<Long> classificationIds = new ArrayList<>();

    /**
     * 获取SKU ID列表
     *
     * @return SKU ID列表
     */
    public List<Long> getSkuIds() {
        return skuIds;
    }

    /**
     * 获取分类ID列表
     *
     * @return 分类ID列表
     */
    public List<Long> getCategoryIds() {
        return categoryIds;
    }

    /**
     * 获取渠道ID列表
     *
     * @return 渠道ID列表
     */
    public List<Long> getChannelIds() {
        return channelIds;
    }

    /**
     * 获取系列ID列表
     *
     * @return 系列ID列表
     */
    public List<Long> getSeriesIds() {
        return seriesIds;
    }

    /**
     * 获取分类ID列表
     *
     * @return 分类ID列表
     */
    public List<Long> getClassificationIds() {
        return classificationIds;
    }
} 