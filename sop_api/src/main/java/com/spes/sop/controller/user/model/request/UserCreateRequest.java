package com.spes.sop.controller.user.model.request;

import com.spes.sop.common.enums.UserRoleEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 用户创建请求
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserCreateRequest {

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    @Size(min = 2, max = 50, message = "用户名长度必须在2-50个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_\\u4e00-\\u9fa5]+$", message = "用户名只能包含字母、数字、下划线和中文")
    private String username;

    /**
     * 工号
     */
    @NotBlank(message = "工号不能为空")
    @Size(min = 2, max = 20, message = "工号长度必须在2-20个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9-]+$", message = "工号只能包含字母、数字和连字符")
    private String employeeId;

    /**
     * 角色
     * @see UserRoleEnum#name()
     */
    @NotBlank(message = "角色不能为空")
    @Size(max = 50, message = "角色长度不能超过50个字符")
    private String role;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    private String password;
} 