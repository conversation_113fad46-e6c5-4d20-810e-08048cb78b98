package com.spes.sop.controller.config.model.vo;

import com.spes.sop.config.enums.ConfigStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ClassificationVO {

    /**
     * 主键 id
     */
    private Long id;

    /**
     * 级别
     */
    private Integer level;

    /**
     * 名称
     */
    private String name;

    /**
     * 状态
     *
     * @see ConfigStatusEnum
     */
    private String status;
    private String statusDesc;

    /**
     * 子级信息
     */
    private List<ClassificationVO> children;
}
