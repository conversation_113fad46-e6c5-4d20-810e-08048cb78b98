package com.spes.sop.controller.config.model.request;

import com.spes.sop.config.enums.ConfigStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ClassificationEnableRequest {
    /**
     * id
     */
    @NotNull(message = "分类ID不能为空")
    private Long id;

    /**
     * 状态
     *
     * @see ConfigStatusEnum#name()
     */
    @NotNull(message = "状态不能为空")
    private String status;
}
