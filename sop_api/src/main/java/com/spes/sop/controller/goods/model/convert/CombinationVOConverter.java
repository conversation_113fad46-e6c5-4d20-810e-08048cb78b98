package com.spes.sop.controller.goods.model.convert;

import com.spes.sop.controller.goods.model.vo.CombinationDetailVO;
import com.spes.sop.controller.goods.model.vo.CombinationPageVO;
import com.spes.sop.controller.goods.model.vo.CombinationSkuItemVO;
import com.spes.sop.support.goods.model.dto.CombinationDetailDTO;
import com.spes.sop.support.goods.model.dto.CombinationPageDTO;
import com.spes.sop.support.goods.model.dto.CombinationSkuItemDTO;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public interface CombinationVOConverter {

    static CombinationDetailVO toDetailVO(CombinationDetailDTO combination) {
        return CombinationDetailVO.builder()
                .id(combination.getId())
                .combinationCode(combination.getCombinationCode())
                .combinationName(combination.getCombinationName())
                .description(combination.getDescription())
                .brand(combination.getBrand())
                .status(combination.getStatus())
                .statusDesc(combination.getStatusDesc())
                .firstClassification(combination.getFirstClassification())
                .secondClassification(combination.getSecondClassification())
                .thirdClassification(combination.getThirdClassification())
                .fourthClassification(combination.getFourthClassification())
                .series(combination.getSeries())
                .category(combination.getCategory())
                .channel(combination.getChannel())
                .skuItems(convertToCombinationSkuItemVO(combination.getSkuItems()))
                .createTime(combination.getCreateTime())
                .updateTime(combination.getUpdateTime())
                .creatorId(combination.getCreatorId())
                .updaterId(combination.getUpdaterId())
                .build();

    }

    static Map<Integer, List<CombinationSkuItemVO>> convertToCombinationSkuItemVO(Map<Integer,
            List<CombinationSkuItemDTO>> skuItems) {
        return skuItems.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream()
                                .map(CombinationVOConverter::convert)
                                .collect(Collectors.toList())
                ));
    }

    static CombinationSkuItemVO convert(CombinationSkuItemDTO combinationSkuItemDTO) {
        return CombinationSkuItemVO.builder()
                .id(combinationSkuItemDTO.getId())
                .skuCode(combinationSkuItemDTO.getSkuCode())
                .skuName(combinationSkuItemDTO.getSkuName())
                .barCode(combinationSkuItemDTO.getBarCode())
                .spec(combinationSkuItemDTO.getSpec())
                .skuNum(combinationSkuItemDTO.getSkuNum())
                .main(combinationSkuItemDTO.getMain())
                .fairPrice(combinationSkuItemDTO.getFairPrice())
                .effective(combinationSkuItemDTO.getEffective())
                .effectiveTime(combinationSkuItemDTO.getEffectiveTime())
                .expirationTime(combinationSkuItemDTO.getExpirationTime())
                .build();

    }

    static CombinationPageVO convert(CombinationPageDTO dto) {
        return CombinationPageVO.builder()
                .id(dto.getId())
                .combinationCode(dto.getCombinationCode())
                .combinationName(dto.getCombinationName())
                .firstClassification(dto.getFirstClassification())
                .secondClassification(dto.getSecondClassification())
                .thirdClassification(dto.getThirdClassification())
                .fourthClassification(dto.getFourthClassification())
                .series(dto.getSeries())
                .category(dto.getCategory())
                .channel(dto.getChannel())
                .skuNames(dto.getSkuNames())
                .creatorId(dto.getCreatorId())
                .updaterId(dto.getUpdaterId())
                .createTime(dto.getCreateTime())
                .updateTime(dto.getUpdateTime())
                .creatorName(dto.getCreatorName())
                .updaterName(dto.getUpdaterName())
                .build();
    }

    static List<CombinationPageVO> toListVO(List<CombinationPageDTO> records) {
        return records.stream()
                .map(CombinationVOConverter::convert)
                .collect(Collectors.toList());
    }
}
