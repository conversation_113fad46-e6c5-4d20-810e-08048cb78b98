package com.spes.sop.controller.config;

import com.spes.sop.common.annotation.OperationLog;
import com.spes.sop.common.page.PageResult;
import com.spes.sop.common.result.Result;
import com.spes.sop.controller.config.model.query.SeriesPagerQuery;
import com.spes.sop.controller.config.model.request.SeriesAddRequest;
import com.spes.sop.controller.config.model.request.SeriesEditRequest;
import com.spes.sop.controller.config.model.request.SeriesEnableRequest;
import com.spes.sop.controller.config.model.vo.SeriesVO;
import com.spes.sop.support.config.SeriesSupport;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;

/**
 * 系列管理控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/series")
public class SeriesConfigController {

    private final SeriesSupport seriesSupport;

    /**
     * 分页查询系列
     */
    @OperationLog("分页查询系列")
    @PostMapping("/list")
    public Result<PageResult<SeriesVO>> list(@RequestBody @Validated @NotNull SeriesPagerQuery query) {
        return Result.success(seriesSupport.list(query));
    }

    /**
     * 新增系列
     */
    @OperationLog("新增系列")
    @PostMapping("/add")
    public Result<Void> add(@RequestBody @Validated @NotNull SeriesAddRequest req) {
        seriesSupport.add(req);
        return Result.success();
    }

    /**
     * 修改系列
     */
    @OperationLog("修改系列")
    @PostMapping("/edit")
    public Result<Void> edit(@RequestBody @Validated @NotNull SeriesEditRequest req) {
        seriesSupport.edit(req);
        return Result.success();
    }

    /**
     * 启用/禁用系列
     */
    @OperationLog("启用/禁用系列")
    @PostMapping("/enable")
    public Result<Void> enable(@RequestBody @Validated @NotNull SeriesEnableRequest request) {
        seriesSupport.enable(request);
        return Result.success();
    }

    /**
     * 删除系列
     */
    @OperationLog("删除系列")
    @PostMapping("/delete")
    public Result<Void> delete(@NotNull Long id) {
        seriesSupport.delete(id);
        return Result.success();
    }
}
