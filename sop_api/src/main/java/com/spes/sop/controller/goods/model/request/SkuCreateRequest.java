package com.spes.sop.controller.goods.model.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

/**
 * SKU新增请求
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuCreateRequest {
    /**
     * SKU名称
     */
    @NotBlank(message = "SKU名称不能为空")
    @Size(max = 100, message = "SKU名称长度不能超过100个字符")
    private String skuName;

    /**
     * 货品规格
     */
    private Long specification;


    /**
     * 立项申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date applicationDate;

    /**
     * 产品上线时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date launchDate;

    /**
     * 是否渠道特供品（true-是，false-否）
     */
    private Boolean channelExclusive;

    /**
     * 产品用途
     */
    private String productPurpose;

    /**
     * 预估售价
     */
    private BigDecimal estimatedPrice;

    /**
     * 首批需求数量
     */
    private Integer firstBatchQuantity;

    /**
     * 工厂起订量
     */
    private Integer factoryMinOrderQuantity;

    /**
     * 最少起订量
     */
    private Integer minOrderQuantity;

    /**
     * 最少起订原因
     */
    private String minOrderReason;

    /**
     * 效期要求
     */
    private String shelfLifeRequirement;

    /**
     * 是否包销（true-是，false-否）
     */
    private Boolean exclusiveSale;

    /**
     * 销售负责人
     */
    private String salesResponsibleEmployeeId;

    /**
     * 备注
     */
    private String remark;

} 
