package com.spes.sop.controller.goods.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuUpdateRequest {
    /**
     * SKU ID
     */
    @NotNull(message = "SKU ID不能为空")
    private Long id;

    /**
     * SKU名称
     */
    @NotBlank(message = "SKU名称不能为空")
    @Size(max = 100, message = "SKU名称长度不能超过100个字符")
    private String skuName;

}
