package com.spes.sop.controller.goods.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * SPU更新请求DTO
 *
 * <AUTHOR>

 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpuUpdateRequest {
    /**
     * SPU ID
     */
    @NotNull(message = "SPU ID不能为空")
    private Long id;
    /**
     * SPU名称
     */
    private String spuName;

    /**
     * 描述
     */
    private String description;

    /**
     * 品牌 id
     */
    private Long brandId;

    /**
     * 一级分类
     */
    private Long firstClassification;

    /**
     * 二级分类
     */
    private Long secondClassification;

    /**
     * 三级分类
     */
    private Long thirdClassification;

    /**
     * 四级类目
     */
    private Long fourthClassification;

    /**
     * 渠道 id
     */
    private Long channelId;

    /**
     * 系列 id
     */
    private Long seriesId;

    /**
     * 分类 id
     */
    private Long categoryId;

} 
