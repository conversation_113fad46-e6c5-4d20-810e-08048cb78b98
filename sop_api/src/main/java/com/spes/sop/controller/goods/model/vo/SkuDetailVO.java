package com.spes.sop.controller.goods.model.vo;


import com.spes.sop.common.enums.GoodsStatusEnum;
import com.spes.sop.controller.goods.model.vo.value.SkuBusinessInfoValue;
import com.spes.sop.controller.goods.model.vo.value.SkuProductInfoValue;
import com.spes.sop.controller.goods.model.vo.value.SkuSupplyChainInfoValue;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuDetailVO {
    /**
     * 主键 id
     */
    private Long id;
    /**
     * 货品编码
     */
    private String skuCode;
    /**
     * SKU名称
     */
    private String skuName;
    /**
     * 条码
     */
    private String barCode;
    /**
     * 产品对应SPU
     */
    private String spuCode;
    private String spuName;

    /**
     * 备案号
     */
    private String filingNumber;
    /**
     * 产品规格
     */
    private Integer spec;
    /**
     * 品牌
     */
    private String brand;
    /**
     * 一级分类
     */
    private String firstClassification;
    /**
     * 二级分类
     */
    private String secondClassification;
    /**
     * 三级分类
     */
    private String thirdClassification;
    /**
     * 四级分类
     */
    private String fourthClassification;
    /**
     * 系列
     */
    private String series;
    /**
     * 产品分类
     */
    private String category;
    /**
     * 渠道
     */
    private String channel;
    /**
     * 是否赠品
     */
    private Boolean gift;
    /**
     * 是否为包裹卡
     */
    private Boolean packageCard;
    /**
     * 状态
     *
     * @see GoodsStatusEnum#name()
     */
    private String status;
    /**
     * 状态描述
     *
     * @see GoodsStatusEnum#getDesc()
     */
    private String statusDesc;
    /**
     * 同步失败原因
     */
    private String syncFail;
    /**
     * 标准价
     */
    private BigDecimal fairPrice;
    /**
     * 底价
     */
    private BigDecimal basePrice;
    /**
     * 成本
     */
    private BigDecimal cost;
    /**
     * 运营信息
     */
    private SkuBusinessInfoValue businessInfo;
    /**
     * 产品信息
     */
    private SkuProductInfoValue productInfo;
    /**
     * 供应链信息
     */
    private SkuSupplyChainInfoValue supplyChainInfo;

    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 创建人
     */
    private String creatorName;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 更新人
     */
    private String updaterName;
}
