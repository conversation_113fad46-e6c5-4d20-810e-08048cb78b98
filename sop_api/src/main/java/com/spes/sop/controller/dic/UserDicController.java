package com.spes.sop.controller.dic;

import com.spes.sop.common.enums.UserRoleEnum;
import com.spes.sop.common.result.Result;
import com.spes.sop.controller.dic.model.vo.EnumVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户相关字典接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/dic/users")
public class UserDicController {

    /**
     * 用户角色枚举接口
     */
    @GetMapping("/roles")
    public Result<List<EnumVO>> getRoles() {
        return Result.success(Arrays.stream(UserRoleEnum.values())
                .map(role -> EnumVO.builder()
                        .name(role.name())
                        .desc(role.getDescription())
                        .build())
                .collect(Collectors.toList()));
    }
}