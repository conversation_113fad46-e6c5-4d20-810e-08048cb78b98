package com.spes.sop.controller.config;

import com.spes.sop.common.annotation.OperationLog;
import com.spes.sop.common.result.Result;
import com.spes.sop.controller.config.model.request.ClassificationAddRequest;
import com.spes.sop.controller.config.model.request.ClassificationEditRequest;
import com.spes.sop.controller.config.model.request.ClassificationEnableRequest;
import com.spes.sop.controller.config.model.vo.ClassificationVO;
import com.spes.sop.support.config.ClassificationSupport;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 产品类目管理控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/classification")
public class ClassificationConfigController {

    private final ClassificationSupport classificationSupport;

    /**
     * 查询产品类目
     */
    @OperationLog("查询产品类目")
    @PostMapping("/list")
    public Result<List<ClassificationVO>> list() {
        return Result.success(classificationSupport.getClassificationTree());
    }

    /**
     * 新增产品类目
     */
    @OperationLog("新增产品类目")
    @PostMapping("/add")
    public Result<Void> add(@Validated @RequestBody @NotNull
                                ClassificationAddRequest request) {
        classificationSupport.add(request);
        return Result.success();
    }

    /**
     * 修改产品类目
     */
    @OperationLog("修改产品类目")
    @PostMapping("/update")
    public Result<Void> update(@Validated @RequestBody @NotNull ClassificationEditRequest request) {
        classificationSupport.edit(request);
        return Result.success();
    }


    /**
     * 启用/禁用产品类目
     */
    @OperationLog("启用/禁用产品类目")
    @PostMapping("/enable")
    public Result<Void> enable(@Validated @RequestBody @NotNull ClassificationEnableRequest request) {
        classificationSupport.enable(request);
        return Result.success();
    }

    /**
     * 删除产品类目
     */
    @OperationLog("删除产品类目")
    @PostMapping("/delete")
    public Result<Void> delete(@NotNull(message = "id 不得为空") Long id) {
        classificationSupport.delete(id);
        return Result.success();
    }
}
