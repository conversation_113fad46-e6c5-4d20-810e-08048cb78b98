package com.spes.sop.controller.goods.model.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * SPU分页查询请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpuPageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * SPU名称（模糊查询）
     */
    @Size(max = 255, message = "SPU名称长度不能超过255个字符")
    private String spuNameSearch;

    /**
     * SPU编码（精确查询）
     */
    @Size(max = 50, message = "SPU编码长度不能超过50个字符")
    private String spuCode;

    /**
     * 品牌ID
     */
    private Long brandId;

    /**
     * 一级分类
     */
    private Long firstClassification;

    /**
     * 二级分类
     */
    private Long secondClassification;

    /**
     * 三级分类
     */
    private Long thirdClassification;

    /**
     * 四级分类
     */
    private Long fourthClassification;

    /**
     * 渠道ID
     */
    private Long channelId;

    /**
     * 系列ID
     */
    private Long seriesId;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 页码（从1开始）
     */
    @Min(value = 1, message = "页码必须大于0")
    @Builder.Default
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    @Builder.Default
    private Integer pageSize = 10;
} 
