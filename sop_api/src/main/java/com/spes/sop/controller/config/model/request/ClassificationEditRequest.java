package com.spes.sop.controller.config.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ClassificationEditRequest {
    /**
     * 分类ID
     */
    @NotNull(message = "分类ID不能为空")
    private Long id;
    /**
     * 名称
     */
    private String name;
}
