package com.spes.sop.controller.third;

import cn.hutool.core.collection.CollUtil;
import com.spes.sop.common.result.Result;
import com.spes.sop.controller.third.model.vo.KingdeeCustomerVO;
import com.spes.sop.third.kingdee.model.business.entity.KingdeeCustomerBO;
import com.spes.sop.third.kingdee.service.KingdeeCloudService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 金蝶相关接口
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/kingdee")
public class KingdeeController {

    private final KingdeeCloudService kingdeeCloudService;

    /**
     * 查询客户信息
     *
     * @return 客户信息
     */
    @PostMapping("/customer")
    public Result<List<KingdeeCustomerVO>> customer(@RequestBody String name) {
        List<KingdeeCustomerBO> customerInfo = kingdeeCloudService.getCustomerInfo(name);
        log.info("获取客户信息 = {}", customerInfo);
        if (CollUtil.isEmpty(customerInfo)) {
            return Result.success();
        }
        List<KingdeeCustomerVO> result = customerInfo.stream().map(customer -> KingdeeCustomerVO.builder()
                .number(customer.getNumber())
                .name(customer.getName())
                .build()).collect(Collectors.toList());
        return Result.success(result);
    }

}