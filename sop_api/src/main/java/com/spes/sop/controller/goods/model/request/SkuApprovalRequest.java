package com.spes.sop.controller.goods.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * SKU审批请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuApprovalRequest {

    /**
     * SKU ID列表
     */
    @NotEmpty(message = "SKU ID列表不能为空")
    private List<Long> ids;

    /**
     * 上传的附件
     */
    private List<String> attachments;
}
