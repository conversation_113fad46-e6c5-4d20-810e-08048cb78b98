package com.spes.sop.controller.goods;

import cn.hutool.core.collection.CollUtil;
import com.spes.sop.common.annotation.OperationLog;
import com.spes.sop.common.page.PageResult;
import com.spes.sop.common.result.Result;
import com.spes.sop.controller.goods.model.convert.SpuVOConverter;
import com.spes.sop.controller.goods.model.query.SpuPageQuery;
import com.spes.sop.controller.goods.model.request.SpuCreateRequest;
import com.spes.sop.controller.goods.model.request.SpuUpdateRequest;
import com.spes.sop.controller.goods.model.vo.SpuDetailVO;
import com.spes.sop.controller.goods.model.vo.SpuPageVO;
import com.spes.sop.support.goods.SkuSupport;
import com.spes.sop.support.goods.SpuSupport;
import com.spes.sop.support.goods.model.dto.SkuListDTO;
import com.spes.sop.support.goods.model.dto.SpuDetailDTO;
import com.spes.sop.support.goods.model.dto.SpuPageDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * SPU商品管理控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/spu")
public class SpuController {

    private final SpuSupport spuSupport;
    private final SkuSupport skuSupport;

    /**
     * SPU分页查询
     */
    @PostMapping("/page")
    public Result<PageResult<SpuPageVO>> page(@Validated @RequestBody @NotNull SpuPageQuery query) {
        PageResult<SpuPageDTO> page = spuSupport.page(query);
        if (CollUtil.isEmpty(page.getRecords())) {
            return Result.success(PageResult.of(Collections.emptyList(), 0L, query.getPageNum(), query.getPageSize()));
        }
        List<Long> spuIds = page.getRecords().stream().map(SpuPageDTO::getId).collect(Collectors.toList());

        List<SkuListDTO> skuInfos = skuSupport.getRelationSkus(spuIds);
        List<SpuPageVO> data = SpuVOConverter.toSpuPageVOList(page.getRecords(), skuInfos);
        return Result.success(PageResult.of(data, page.getTotal(), page.getPageNum(), page.getPageSize()));
    }

    /**
     * 根据ID查询SPU详情
     */
    @GetMapping("/get/{id}")
    public Result<SpuDetailVO> getById(@PathVariable Long id) {
        SpuDetailDTO spuDetail = spuSupport.getById(id);
        if (spuDetail == null) {
            return Result.error("未找到SPU信息");
        }
        List<SkuListDTO> skuInfos = skuSupport.getRelationSkus(Collections.singletonList(id));
        SpuDetailVO result = SpuVOConverter.toSpuDetailVO(spuDetail, skuInfos);
        return Result.success(result);
    }

    /**
     * 新增SPU
     */
    @OperationLog("新增SPU商品")
    @PostMapping
    public Result<Void> create(@Valid @RequestBody @NotNull SpuCreateRequest request) {
        spuSupport.create(request);
        return Result.success();
    }

    /**
     * 更新SPU信息
     */
    @OperationLog("更新SPU商品")
    @PostMapping("/update")
    public Result<Void> update(@Valid @RequestBody @NotNull SpuUpdateRequest request) {
        spuSupport.update(request);
        return Result.success();
    }
}