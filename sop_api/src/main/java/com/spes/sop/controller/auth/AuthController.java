package com.spes.sop.controller.auth;

import com.spes.sop.common.result.Result;
import com.spes.sop.common.security.JwtTokenUtil;
import com.spes.sop.common.security.SecurityContextUtil;
import com.spes.sop.controller.auth.model.request.LoginRequest;
import com.spes.sop.controller.auth.model.request.RefreshTokenRequest;
import com.spes.sop.controller.auth.model.vo.LoginVO;
import com.spes.sop.controller.auth.model.vo.RefreshTokenVO;
import com.spes.sop.user.service.user.UserService;
import com.spes.sop.user.service.user.model.bo.UserBO;
import com.spes.sop.user.service.user.model.query.UserGetQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Objects;

/**
 * 认证控制器
 * 提供用户登录、Token刷新、登出等认证相关功能
 * 支持Token版本控制的失效机制
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/auth")
@Validated
public class AuthController {

    private final JwtTokenUtil jwtTokenUtil;

    private final UserService userService;


    /**
     * 用户登录
     *
     * @param request 登录请求
     * @return 登录结果，包含访问Token和刷新Token
     */
    @PostMapping("/login")
    public Result<LoginVO> login(@Valid @RequestBody LoginRequest request) {
        Objects.requireNonNull(request, "登录请求不能为空");

        log.info("用户登录请求，用户名：{}", request.getUsername());

        try {

            boolean validate = userService.validateUserPassword(request.getUsername(), request.getPassword());
            if (!validate) {
                log.warn("用户登录失败，工号或密码错误：{}", request.getUsername());
                return Result.error("工号或密码错误");
            }
            UserBO user = userService.getUser(UserGetQuery.builder()
                    .employeeId(request.getUsername())
                    .build());

            // 生成访问Token（会自动生成新的Token版本）
            String accessToken = jwtTokenUtil.generateAccessToken(
                    user.getId(), user.getUsername(), user.getRole().name(), user.getEmployeeId(), user.getDepartment(),
                    user.getWeaverId());

            // 获取当前Token版本号，用于生成刷新Token
            JwtTokenUtil.UserTokenInfo tokenInfo = jwtTokenUtil.parseToken(accessToken);
            String refreshToken = jwtTokenUtil.generateRefreshToken(
                    user.getId(), user.getUsername(), tokenInfo.getTokenVersion());

            // 构建响应
            LoginVO loginVO = builderResult(accessToken, refreshToken, user);

            log.info("用户登录成功，用户ID：{}，用户名：{}", user.getId(), user.getUsername());

            return Result.success("登录成功", loginVO);

        } catch (Exception e) {
            log.error("用户登录失败，用户名：{}", request.getUsername(), e);
            return Result.error("登录失败：" + e.getMessage());
        }
    }


    /**
     * 刷新Token
     *
     * @param request 刷新Token请求
     * @return 新的访问Token和刷新Token
     */
    @PostMapping("/refresh")
    public Result<RefreshTokenVO> refreshToken(@Valid @RequestBody RefreshTokenRequest request) {
        Objects.requireNonNull(request, "刷新Token请求不能为空");

        log.info("Token刷新请求");

        try {
            // 验证刷新Token
            JwtTokenUtil.UserTokenInfo tokenInfo = jwtTokenUtil.validateRefreshToken(request.getRefreshToken());
            if (tokenInfo == null) {
                log.warn("刷新Token无效或已过期");
                return Result.error("刷新Token无效或已过期");
            }

            // 从数据库查询最新的用户信息
            UserBO user = userService.getUser(UserGetQuery.builder()
                    .id(tokenInfo.getUserId())
                    .build());

            if (user == null) {
                log.warn("用户不存在，用户ID：{}", tokenInfo.getUserId());
                return Result.error("用户不存在");
            }

            // 生成新的Token版本（实现refreshToken轮换）
            String newAccessToken = jwtTokenUtil.generateAccessToken(
                    user.getId(),
                    user.getUsername(),
                    user.getRole().name(),
                    user.getEmployeeId(),
                    user.getDepartment(),
                    user.getWeaverId());

            // 获取新的Token版本号，生成新的刷新Token
            JwtTokenUtil.UserTokenInfo newTokenInfo = jwtTokenUtil.parseToken(newAccessToken);
            String newRefreshToken = jwtTokenUtil.generateRefreshToken(
                    user.getId(),
                    user.getUsername(),
                    newTokenInfo.getTokenVersion());

            // 构建响应
            RefreshTokenVO refreshTokenVO = RefreshTokenVO.builder()
                    .accessToken(newAccessToken)
                    .refreshToken(newRefreshToken)
                    .tokenType("Bearer")
                    .expiresIn(86400L) // 24小时
                    .refreshExpiresIn(604800L) // 7天
                    .build();

            log.info("Token刷新成功，用户ID：{}，用户名：{}，新Token版本：{}",
                    user.getId(), user.getUsername(), newTokenInfo.getTokenVersion());

            return Result.success("Token刷新成功", refreshTokenVO);

        } catch (Exception e) {
            log.error("Token刷新失败", e);
            return Result.error("Token刷新失败：" + e.getMessage());
        }
    }

    /**
     * 用户登出
     * 通过更新Token版本号使所有Token失效
     *
     * @param request HTTP请求
     * @return 登出结果
     */
    @PostMapping("/logout")
    public Result<Void> logout(HttpServletRequest request) {
        log.info("用户登出请求");

        try {
            // 获取当前用户信息
            Long userId = SecurityContextUtil.getCurrentUserIdLenient();
            String username = SecurityContextUtil.getCurrentUsernameLenient();

            if (userId != null) {
                // 使用户的所有Token失效
                Long newTokenVersion = jwtTokenUtil.invalidateAllUserTokens(userId);
                log.info("用户登出成功，所有Token已失效，用户ID：{}，用户名：{}，新Token版本：{}",
                        userId, username, newTokenVersion);
            } else {
                log.warn("登出时无法获取用户信息，可能是开发模式或Token已失效");
            }

            return Result.success("登出成功");

        } catch (Exception e) {
            log.error("用户登出失败", e);
            return Result.error("登出失败：" + e.getMessage());
        }
    }


    private static LoginVO builderResult(String accessToken, String refreshToken, UserBO user) {
        LoginVO loginVO = new LoginVO();
        loginVO.setAccessToken(accessToken);
        loginVO.setRefreshToken(refreshToken);
        loginVO.setTokenType("Bearer");
        loginVO.setUserId(user.getId());
        loginVO.setEmployeeId(user.getEmployeeId());
        loginVO.setUsername(user.getUsername());
        loginVO.setRoles(user.getRole().name());
        loginVO.setDepartment(user.getDepartment());
        loginVO.setExpiresIn(86400L); // 24小时
        return loginVO;
    }

} 
