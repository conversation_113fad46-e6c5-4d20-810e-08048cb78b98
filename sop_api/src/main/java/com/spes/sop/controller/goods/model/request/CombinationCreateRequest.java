package com.spes.sop.controller.goods.model.request;

import com.spes.sop.controller.goods.model.request.value.CombinationSkuItem;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 组合品创建请求
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CombinationCreateRequest {

    /**
     * 组合品名称
     */
    @NotBlank(message = "组合品名称不能为空")
    private String combinationName;

    /**
     * 描述
     */
    private String description;

    /**
     * 组合品明细列表
     */
    @NotEmpty(message = "关联的 sku 列表")
    private List<CombinationSkuItem> skuIds;

} 
