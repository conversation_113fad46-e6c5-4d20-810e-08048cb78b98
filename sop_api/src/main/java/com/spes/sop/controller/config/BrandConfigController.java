package com.spes.sop.controller.config;

import com.spes.sop.common.annotation.OperationLog;
import com.spes.sop.common.page.PageResult;
import com.spes.sop.common.result.Result;
import com.spes.sop.controller.config.model.query.BrandPagerQuery;
import com.spes.sop.controller.config.model.request.BrandAddRequest;
import com.spes.sop.controller.config.model.request.BrandEditRequest;
import com.spes.sop.controller.config.model.request.BrandEnableRequest;
import com.spes.sop.controller.config.model.vo.BrandVO;
import com.spes.sop.support.config.BrandSupport;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;

/**
 * 品牌管理控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/brand")
public class BrandConfigController {

    private final BrandSupport brandSupport;

    /**
     * 品牌查询
     */
    @OperationLog("查询品牌")
    @PostMapping("/list")
    public Result<PageResult<BrandVO>> list(@RequestBody @Validated @NotNull BrandPagerQuery query) {
        return Result.success(brandSupport.list(query));
    }

    /**
     * 新增
     */
    @OperationLog("新增品牌")
    @PostMapping("/add")
    public Result<Void> add(@RequestBody @Validated @NotNull BrandAddRequest req) {
        brandSupport.add(req);
        return Result.success();
    }

    /**
     * 修改
     */
    @OperationLog("修改品牌")
    @PostMapping("/edit")
    public Result<Void> edit(@RequestBody @Validated @NotNull BrandEditRequest req) {
        brandSupport.edit(req);
        return Result.success();
    }


    /**
     * 启用/禁用
     */
    @OperationLog("启用/禁用品牌")
    @PostMapping("/enable")
    public Result<Void> enable(@RequestBody @Validated @NotNull BrandEnableRequest req) {
        brandSupport.enable(req);
        return Result.success();
    }

    /**
     * 删除
     */
    @OperationLog("删除品牌")
    @PostMapping("/delete")
    public Result<Void> delete(@NotNull Long id) {
        brandSupport.delete(id);
        return Result.success();
    }
}
