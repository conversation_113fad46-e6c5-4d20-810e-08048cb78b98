package com.spes.sop.controller.goods.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * SKU批量导入结果VO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuBatchImportResultVO {

    /**
     * 导入总数
     */
    private Integer totalCount;

    /**
     * 成功导入数量
     */
    private Integer successCount;

    /**
     * 失败数量
     */
    private Integer failCount;

    /**
     * 失败详情列表
     */
    private List<SkuImportFailDetail> failDetails;

    /**
     * 导入失败详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SkuImportFailDetail {
        /**
         * 行号
         */
        private Integer rowNum;

        /**
         * SKU名称
         */
        private String skuName;

        /**
         * 失败原因
         */
        private String failReason;
    }
} 