package com.spes.sop.controller.config;

import com.spes.sop.common.annotation.OperationLog;
import com.spes.sop.common.page.PageResult;
import com.spes.sop.common.result.Result;
import com.spes.sop.controller.config.model.query.ChannelPagerQuery;
import com.spes.sop.controller.config.model.request.ChannelAddRequest;
import com.spes.sop.controller.config.model.request.ChannelEditRequest;
import com.spes.sop.controller.config.model.request.ChannelEnableRequest;
import com.spes.sop.controller.config.model.vo.ChannelVO;
import com.spes.sop.support.config.ChannelSupport;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;

/**
 * 渠道管理控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/channel")
public class ChannelConfigController {

    private final ChannelSupport channelSupport;

    /**
     * 获取渠道列表
     */
    @OperationLog("获取渠道列表")
    @PostMapping("/list")
    public Result<PageResult<ChannelVO>> list(@RequestBody @Validated @NotNull ChannelPagerQuery query) {
        return Result.success(channelSupport.list(query));
    }

    /**
     * 新增渠道
     */
    @OperationLog("新增渠道")
    @PostMapping("/add")
    public Result<Void> add(@RequestBody @Validated @NotNull ChannelAddRequest request) {
        channelSupport.add(request);
        return Result.success();
    }

    /**
     * 修改渠道
     */
    @OperationLog("修改渠道")
    @PostMapping("/edit")
    public Result<Void> edit(@RequestBody @Validated @NotNull ChannelEditRequest request) {
        channelSupport.edit(request);
        return Result.success();
    }

    /**
     * 启用/禁用渠道
     */
    @OperationLog("启用/禁用渠道")
    @PostMapping("/enable")
    public Result<Void> enable(@RequestBody @Validated @NotNull ChannelEnableRequest request) {
        channelSupport.enable(request);
        return Result.success();
    }

    /**
     * 删除渠道
     */
    @OperationLog("删除渠道")
    @PostMapping("/delete")
    public Result<Void> delete(@NotNull Long id) {
        channelSupport.delete(id);
        return Result.success();
    }
}
