package com.spes.sop.controller.goods.model.query;

import com.spes.sop.common.enums.GoodsStatusEnum;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * SKU分页查询请求
 *
 * <AUTHOR>
 */
@Data
public class SkuPageQuery {
    /**
     * skuId
     */
    private Long id;
    /**
     * SPU ID
     */
    private Long spuId;

    /**
     * 一级分类
     */
    private Long firstClassification;
    /**
     * 二级分类
     */
    private Long secondClassification;

    /**
     * 三级分类
     */
    private Long thirdClassification;

    /**
     * SKU编码（精确查询）
     */
    @Size(max = 50, message = "SKU编码长度不能超过50个字符")
    private String skuCode;

    /**
     * SKU名称（模糊查询）
     */
    @Size(max = 100, message = "SKU名称长度不能超过100个字符")
    private String skuNameSearch;

    /**
     * 条码
     */
    private String barCode;

    /**
     * 系列
     */
    private Long seriesId;
    /**
     * 渠道
     */
    private List<Long> channelIds;


    /**
     * 状态
     *
     * @see GoodsStatusEnum#name()
     */
    private List<String> statuses;

    /**
     * 是否赠品
     */
    private Boolean gift;
    /**
     * 是否为包裹卡
     */
    private Boolean packageCard;


    /**
     * 页码（从1开始）
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer pageSize = 10;
} 
