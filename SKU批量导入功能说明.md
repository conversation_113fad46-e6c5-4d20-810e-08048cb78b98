# SKU批量导入功能说明

## 功能概述

本功能支持通过Excel文件批量导入SKU数据，提供了完整的文件上传、数据验证、批量处理和结果反馈机制。

## 接口说明

### 1. 批量导入接口

**接口地址：** `POST /api/sku/batch-import`

**请求参数：**
- `file`: MultipartFile类型，Excel文件（.xlsx或.xls格式）

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalCount": 10,
    "successCount": 8,
    "failCount": 2,
    "failDetails": [
      {
        "rowNum": 3,
        "skuName": "测试SKU-003",
        "failReason": "SKU名称不能为空"
      },
      {
        "rowNum": 5,
        "skuName": "测试SKU-005",
        "failReason": "预估金额格式错误"
      }
    ]
  }
}
```

### 2. 下载导入模板接口

**接口地址：** `GET /api/sku/download-template`

**响应：** 直接下载Excel模板文件

## Excel模板格式

| 列名 | 字段说明 | 是否必填 | 数据类型 | 示例值 |
|------|----------|----------|----------|--------|
| SKU名称 | 商品SKU名称 | 是 | 文本 | 测试SKU-001 |
| 货品规格 | 规格编号 | 否 | 数字 | 1 |
| 立项申请时间 | 申请日期 | 否 | 日期 | 2024-01-01 |
| 产品上线时间 | 上线日期 | 否 | 日期 | 2024-02-01 |
| 是否渠道特供品 | 特供标识 | 否 | 文本 | 是/否 |
| 产品用途 | 用途说明 | 否 | 文本 | 测试用途 |
| 预估金额 | 预估价格 | 否 | 数字 | 199.99 |
| 首批需求数量 | 首批数量 | 否 | 数字 | 1000 |
| 工厂起订量 | 工厂最小订量 | 否 | 数字 | 500 |
| 最少起订量 | 最小订量 | 否 | 数字 | 100 |
| 最少起订原因 | 起订原因 | 否 | 文本 | 测试起订原因 |
| 效期要求 | 保质期要求 | 否 | 文本 | 24个月 |
| 是否包销 | 包销标识 | 否 | 文本 | 是/否 |
| 销售负责人 | 负责人ID | 否 | 文本 | EMP001 |
| 备注 | 备注信息 | 否 | 文本 | 测试备注 |

## 使用流程

1. **下载模板**
   - 调用下载模板接口获取Excel模板
   - 或者使用测试类生成样例文件

2. **填写数据**
   - 按照模板格式填写SKU数据
   - 注意数据类型和格式要求
   - 布尔值字段请使用"是"或"否"

3. **上传文件**
   - 调用批量导入接口上传Excel文件
   - 系统会自动验证数据格式
   - 返回导入结果统计

4. **查看结果**
   - 检查导入成功数量
   - 查看失败记录的详细原因
   - 根据失败原因修正数据后重新导入

## 数据验证规则

- **SKU名称**：必填，长度不超过100个字符
- **日期字段**：格式为yyyy-MM-dd
- **布尔字段**：使用"是"或"否"
- **数字字段**：必须为有效数字格式
- **文本字段**：按照业务规则验证

## 错误处理

系统会对每一行数据进行验证，如果验证失败会记录：
- 行号（Excel中的行号）
- SKU名称（用于识别）
- 失败原因（具体的错误信息）

导入过程中遇到错误不会中断整个导入过程，会继续处理后续数据。

## 注意事项

1. **文件大小限制**：单个文件最大50MB
2. **文件格式**：仅支持.xlsx和.xls格式
3. **数据量建议**：建议单次导入不超过1000条记录
4. **权限要求**：需要有SKU创建权限
5. **事务处理**：每条记录独立处理，单条失败不影响其他记录

## 测试

可以运行测试类 `SkuBatchImportTest.generateTestExcel()` 生成测试用的Excel文件。

## 技术实现

- 使用EasyExcel进行Excel文件的读写
- 支持流式读取，内存占用低
- 提供完整的数据验证和错误处理机制
- 集成了现有的SKU创建流程和权限控制 